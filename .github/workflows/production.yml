name: Deploy-Production

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

# Authenticate to the server via ssh
# and run our deployment script
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout GIT Repository code
        uses: actions/checkout@v4
      - name: Deploy to server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          port: ${{ secrets.PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/tall-hill-520.xcloud.name/
            git reset --hard && git clean -df
            git pull origin master
            composer install --no-interaction --prefer-dist --optimize-autoloader
            php artisan clear-compiled
            php artisan optimize
            php artisan config:cache
            php artisan migrate --force
            yarn
            yarn build
            echo "Moving manifest.json to public/build"
            mv public/build/.vite/manifest.json public/build/manifest.json
            echo "Deployment successful"
