<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="'Package Management'" :breadcrumb-items="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Package Management', 'url' => route('admin.packages.index'), 'active' => true]
            ]" />
        </div>

        <!-- Filters -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </header>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.packages.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div class="input-area">
                        <label class="form-label">Search Packages</label>
                        <div class="relative">
                            <input type="text" name="q" value="{{ $filters['q'] ?? '' }}"
                                   class="form-control !pl-9" placeholder="Search by name or description...">
                            <iconify-icon icon="heroicons-outline:search" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                        </div>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Target Role</label>
                        <select name="role" class="form-control">
                            <option value="">All Roles</option>
                            <option value="investor" {{ ($filters['role'] ?? '') === 'investor' ? 'selected' : '' }}>Investor</option>
                            <option value="startup" {{ ($filters['role'] ?? '') === 'startup' ? 'selected' : '' }}>Startup</option>
                            <option value="analyst" {{ ($filters['role'] ?? '') === 'analyst' ? 'selected' : '' }}>Analyst</option>
                            <option value="all" {{ ($filters['role'] ?? '') === 'all' ? 'selected' : '' }}>All Users</option>
                        </select>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Per Page</label>
                        <select name="per_page" class="form-control">
                            <option value="10" {{ ($filters['per_page'] ?? 10) == 10 ? 'selected' : '' }}>10</option>
                            <option value="25" {{ ($filters['per_page'] ?? 10) == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ ($filters['per_page'] ?? 10) == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ ($filters['per_page'] ?? 10) == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:search" class="mr-2"></iconify-icon>
                            Search
                        </button>
                        <a href="{{ route('admin.packages.index') }}" class="btn btn-outline-secondary">
                            <iconify-icon icon="heroicons-outline:x-mark" class="mr-2"></iconify-icon>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Packages Table -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Packages ({{ $packages->total() }})</h4>
                <div class="flex space-x-2">
                    <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
                        <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                        Create Package
                    </a>
                </div>
            </header>
            <div class="card-body px-6 pb-6">
                @if($packages->count() > 0)
                    <div class="overflow-x-auto -mx-6">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                    <thead class="bg-slate-200 dark:bg-slate-700">
                                        <tr>
                                            <th scope="col" class="table-th">Package Name</th>
                                            <th scope="col" class="table-th">Price</th>
                                            <th scope="col" class="table-th">Target Role</th>
                                            <th scope="col" class="table-th">Purchases</th>
                                            <th scope="col" class="table-th">Status</th>
                                            <th scope="col" class="table-th">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @foreach($packages as $package)
                                            <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                                                <td class="table-td">
                                                    <div>
                                                        <div class="text-slate-600 dark:text-slate-300 text-sm font-medium">
                                                            {{ $package->name }}
                                                        </div>
                                                        @if($package->description)
                                                            <div class="text-slate-500 dark:text-slate-400 text-xs mt-1">
                                                                {{ Str::limit($package->description, 50) }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <span class="text-slate-900 dark:text-white font-medium">
                                                        ${{ number_format($package->price, 2) }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <span class="badge
                                                        @if($package->target_role === 'investor') bg-blue-500
                                                        @elseif($package->target_role === 'startup') bg-green-500
                                                        @elseif($package->target_role === 'analyst') bg-purple-500
                                                        @else bg-slate-500 @endif text-white">
                                                        {{ ucfirst($package->target_role) }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <span class="text-slate-900 dark:text-white font-medium">
                                                        {{ $package->package_purchases_count }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <span class="badge {{ $package->is_active ? 'bg-success-500' : 'bg-danger-500' }} text-white">
                                                        {{ $package->is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex space-x-3 rtl:space-x-reverse">
                                                        <a href="{{ route('admin.packages.edit', $package) }}"
                                                           class="action-btn" data-tippy-content="Edit">
                                                            <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                                        </a>

                                                        <form method="POST" action="{{ route('admin.packages.toggle-status', $package) }}" class="inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <button type="submit" class="action-btn"
                                                                    data-tippy-content="{{ $package->is_active ? 'Deactivate' : 'Activate' }}">
                                                                <iconify-icon icon="{{ $package->is_active ? 'heroicons:eye-slash' : 'heroicons:eye' }}"></iconify-icon>
                                                            </button>
                                                        </form>

                                                        @if(!$package->packagePurchases()->where('status', 'completed')->exists())
                                                            <form method="POST" action="{{ route('admin.packages.destroy', $package) }}"
                                                                  class="inline" onsubmit="return confirm('Are you sure you want to delete this package?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="action-btn" data-tippy-content="Delete">
                                                                    <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                                </button>
                                                            </form>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $packages->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <iconify-icon icon="heroicons-outline:cube" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
                        <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No packages found</h3>
                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                            @if(request()->hasAny(['q', 'status', 'role']))
                                Try adjusting your search criteria.
                            @else
                                Get started by creating a new package.
                            @endif
                        </p>
                        @if(!request()->hasAny(['q', 'status', 'role']))
                            <div class="mt-6">
                                <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
                                    <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                                    Create Package
                                </a>
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
