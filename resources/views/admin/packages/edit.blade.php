<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="'Edit Package'" :breadcrumb-items="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Packages', 'url' => route('admin.packages.index'), 'active' => false],
                ['name' => 'Edit Package', 'url' => route('admin.packages.edit', $package), 'active' => true]
            ]" />
        </div>

        <!-- Edit Form -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Edit Package</h4>
                <div>
                    <a href="{{ route('admin.packages.index') }}" class="btn inline-flex justify-center btn-outline-secondary">
                        <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                        Back to Packages
                    </a>
                </div>
            </header>
            <div class="card-body p-6">
                <form method="POST" action="{{ route('admin.packages.update', $package) }}" id="packageForm">
                    @csrf
                    @method('PUT')
                    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="card">
                            <div class="card-body p-6">
                                <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
                                    <div class="flex-1">
                                        <div class="card-title text-slate-900 dark:text-white">Basic Information</div>
                                    </div>
                                </header>
                                <div class="space-y-4">
                                    <div class="input-area">
                                        <label for="name" class="form-label">Package Name <span class="text-danger-500">*</span></label>
                                        <div class="relative">
                                            <input type="text" id="name" name="name" value="{{ old('name', $package->name) }}"
                                                   class="form-control !pl-9 @error('name') !border-danger-500 @enderror"
                                                   placeholder="Enter package name" required>
                                            <iconify-icon icon="heroicons-outline:cog-6-tooth" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                        </div>
                                        @error('name')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="input-area">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea id="description" name="description" rows="4"
                                                  class="form-control @error('description') !border-danger-500 @enderror"
                                                  placeholder="Enter package description">{{ old('description', $package->description) }}</textarea>
                                        @error('description')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>


                                </div>
                            </div>
                        </div>

                        <!-- Pricing & Configuration -->
                        <div class="card">
                            <div class="card-body p-6">
                                <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
                                    <div class="flex-1">
                                        <div class="card-title text-slate-900 dark:text-white">Pricing & Configuration</div>
                                    </div>
                                </header>
                                <div class="space-y-4">
                                    <div class="input-area">
                                        <label for="price" class="form-label">Price (USD) <span class="text-danger-500">*</span></label>
                                        <div class="relative">
                                            <input type="number" id="price" name="price" value="{{ old('price', $package->price) }}"
                                                   class="form-control !pl-9 @error('price') !border-danger-500 @enderror"
                                                   placeholder="0.00" step="0.01" min="0" required>
                                            <iconify-icon icon="heroicons-outline:currency-dollar" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                        </div>
                                        @error('price')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="input-area">
                                        <label for="target_role" class="form-label">Target Role <span class="text-danger-500">*</span></label>
                                        <select id="target_role" name="target_role" class="form-control @error('target_role') !border-danger-500 @enderror" required>
                                            <option value="">Select target role</option>
                                            <option value="investor" {{ old('target_role', $package->target_role) === 'investor' ? 'selected' : '' }}>Investor</option>
                                            <option value="startup" {{ old('target_role', $package->target_role) === 'startup' ? 'selected' : '' }}>Startup</option>
                                            <option value="analyst" {{ old('target_role', $package->target_role) === 'analyst' ? 'selected' : '' }}>Analyst</option>
                                            <option value="all" {{ old('target_role', $package->target_role) === 'all' ? 'selected' : '' }}>All Users</option>
                                        </select>
                                        @error('target_role')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="input-area">
                                        <label for="sort_order" class="form-label">Sort Order</label>
                                        <div class="relative">
                                            <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', $package->sort_order) }}"
                                                   class="form-control !pl-9 @error('sort_order') !border-danger-500 @enderror"
                                                   placeholder="0" min="0">
                                            <iconify-icon icon="heroicons-outline:bars-3" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                        </div>
                                        @error('sort_order')
                                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="checkbox-area">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" name="is_active" value="1"
                                                   class="form-checkbox" {{ old('is_active', $package->is_active) ? 'checked' : '' }}>
                                            <span class="text-slate-500 dark:text-slate-400 text-sm leading-6 ltr:ml-2 rtl:mr-2">
                                                Package is active and available for purchase
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Features Section -->
                    <div class="card mt-6">
                        <div class="card-body p-6">
                            <header class="flex mb-5 items-center border-b border-slate-100 dark:border-slate-700 pb-5 -mx-6 px-6">
                                <div class="flex-1">
                                    <div class="card-title text-slate-900 dark:text-white">Package Features</div>
                                    <div class="text-sm text-slate-500 dark:text-slate-400 mt-1">
                                        Add features that describe what this package includes
                                    </div>
                                </div>
                            </header>
                            <div id="features-container">
                                @if(old('features', $package->features))
                                    @foreach(old('features', $package->features) as $index => $feature)
                                        <div class="feature-item flex items-center space-x-3 mb-3">
                                            <div class="flex-1">
                                                <input type="text" name="features[]" value="{{ $feature }}"
                                                       class="form-control" placeholder="Enter feature description">
                                            </div>
                                            <button type="button" class="btn btn-outline-danger btn-sm remove-feature" style="display: none;">
                                                <iconify-icon icon="heroicons-outline:trash"></iconify-icon>
                                            </button>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="feature-item flex items-center space-x-3 mb-3">
                                        <div class="flex-1">
                                            <input type="text" name="features[]" value=""
                                                   class="form-control" placeholder="Enter feature description">
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm remove-feature" style="display: none;">
                                            <iconify-icon icon="heroicons-outline:trash"></iconify-icon>
                                        </button>
                                    </div>
                                @endif
                            </div>
                            <button type="button" id="add-feature" class="btn btn-outline-primary btn-sm mt-3">
                                <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                                Add Feature
                            </button>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 mt-8">
                        <a href="{{ route('admin.packages.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:check" class="mr-2"></iconify-icon>
                            Update Package
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const featuresContainer = document.getElementById('features-container');
            const addFeatureBtn = document.getElementById('add-feature');
            
            // Add feature functionality
            addFeatureBtn.addEventListener('click', function() {
                const featureItem = document.createElement('div');
                featureItem.className = 'feature-item flex items-center space-x-3 mb-3';
                featureItem.innerHTML = `
                    <div class="flex-1">
                        <input type="text" name="features[]" class="form-control" placeholder="Enter feature description">
                    </div>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-feature">
                        <iconify-icon icon="heroicons-outline:trash"></iconify-icon>
                    </button>
                `;
                featuresContainer.appendChild(featureItem);
                updateRemoveButtons();
            });
            
            // Remove feature functionality
            featuresContainer.addEventListener('click', function(e) {
                if (e.target.closest('.remove-feature')) {
                    e.target.closest('.feature-item').remove();
                    updateRemoveButtons();
                }
            });
            
            function updateRemoveButtons() {
                const featureItems = featuresContainer.querySelectorAll('.feature-item');
                featureItems.forEach((item, index) => {
                    const removeBtn = item.querySelector('.remove-feature');
                    if (featureItems.length > 1) {
                        removeBtn.style.display = 'block';
                    } else {
                        removeBtn.style.display = 'none';
                    }
                });
            }
            
            // Initialize
            updateRemoveButtons();
        });
    </script>
    @endpush
</x-app-layout>
