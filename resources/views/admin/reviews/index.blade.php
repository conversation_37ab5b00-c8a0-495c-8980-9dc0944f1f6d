<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :pageTitle="'Customer Reviews Management'" :breadcrumbItems="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Customer Reviews', 'url' => route('admin.reviews.index'), 'active' => true]
            ]" />
        </div>

        <!-- Filters -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </header>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.reviews.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div class="input-area">
                        <label class="form-label">Search Reviews</label>
                        <div class="relative">
                            <input type="text" name="q" value="{{ $filters['q'] ?? '' }}"
                                   class="form-control !pl-9" placeholder="Search by name, company, message...">
                            <iconify-icon icon="heroicons-outline:search" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                        </div>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Featured</label>
                        <select name="featured" class="form-control">
                            <option value="">All Reviews</option>
                            <option value="1" {{ ($filters['featured'] ?? '') === '1' ? 'selected' : '' }}>Featured Only</option>
                            <option value="0" {{ ($filters['featured'] ?? '') === '0' ? 'selected' : '' }}>Not Featured</option>
                        </select>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Status</label>
                        <select name="active" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="1" {{ ($filters['active'] ?? '') === '1' ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ ($filters['active'] ?? '') === '0' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Per Page</label>
                        <select name="per_page" class="form-control">
                            <option value="10" {{ ($filters['per_page'] ?? 10) == 10 ? 'selected' : '' }}>10</option>
                            <option value="25" {{ ($filters['per_page'] ?? 10) == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ ($filters['per_page'] ?? 10) == 50 ? 'selected' : '' }}>50</option>
                        </select>
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:search" class="mr-2"></iconify-icon>
                            Search
                        </button>
                        <a href="{{ route('admin.reviews.index') }}" class="btn btn-outline-secondary">
                            <iconify-icon icon="heroicons-outline:x-mark" class="mr-2"></iconify-icon>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Reviews Table -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Customer Reviews ({{ $reviews->total() }})</h4>
                <div class="flex space-x-2">
                    <a href="{{ route('admin.reviews.create') }}" class="btn btn-primary">
                        <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                        Create Review
                    </a>
                </div>
            </header>
            <div class="card-body px-6 pb-6">
                @if($reviews->count() > 0)
                    <div class="overflow-x-auto -mx-6">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                    <thead class="bg-slate-200 dark:bg-slate-700">
                                        <tr>
                                            <th scope="col" class="table-th">Customer</th>
                                            <th scope="col" class="table-th">Review</th>
                                            <th scope="col" class="table-th">Rating</th>
                                            <th scope="col" class="table-th">Status</th>
                                            <th scope="col" class="table-th">Sort Order</th>
                                            <th scope="col" class="table-th">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @foreach($reviews as $review)
                                            <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                                                <td class="table-td">
                                                    <div class="flex items-center space-x-3">
                                                        @if($review->avatar_url)
                                                            <img src="{{ $review->avatar_thumb_url }}" alt="{{ $review->customer_name }}" 
                                                                 class="w-10 h-10 rounded-full object-cover">
                                                        @else
                                                            <div class="w-10 h-10 rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center">
                                                                <iconify-icon icon="heroicons-outline:user" class="text-slate-500"></iconify-icon>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium">
                                                                {{ $review->customer_name }}
                                                            </div>
                                                            @if($review->formatted_designation)
                                                                <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                                    {{ $review->formatted_designation }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <div class="text-slate-600 dark:text-slate-300 text-sm">
                                                        {{ Str::limit($review->message, 80) }}
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    @if($review->rating)
                                                        <div class="flex items-center space-x-1">
                                                            @for($i = 1; $i <= 5; $i++)
                                                                <iconify-icon icon="heroicons-solid:star" 
                                                                    class="{{ $i <= $review->rating ? 'text-yellow-400' : 'text-slate-300' }} text-sm"></iconify-icon>
                                                            @endfor
                                                            <span class="text-slate-500 text-xs ml-1">({{ $review->rating }})</span>
                                                        </div>
                                                    @else
                                                        <span class="text-slate-500 text-sm">No rating</span>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex flex-col space-y-1">
                                                        <span class="badge {{ $review->is_active ? 'bg-success-500' : 'bg-danger-500' }} text-white text-xs">
                                                            {{ $review->is_active ? 'Active' : 'Inactive' }}
                                                        </span>
                                                        @if($review->is_featured)
                                                            <span class="badge bg-warning-500 text-white text-xs">
                                                                Featured
                                                            </span>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <span class="text-slate-900 dark:text-white font-medium">
                                                        {{ $review->sort_order }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex space-x-3 rtl:space-x-reverse">
                                                        <a href="{{ route('admin.reviews.edit', $review) }}" 
                                                           class="action-btn" data-tippy-content="Edit">
                                                            <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                                        </a>
                                                        
                                                        <form method="POST" action="{{ route('admin.reviews.toggle-featured', $review) }}" class="inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <button type="submit" class="action-btn" 
                                                                    data-tippy-content="{{ $review->is_featured ? 'Unfeature' : 'Feature' }}">
                                                                <iconify-icon icon="{{ $review->is_featured ? 'heroicons:star-solid' : 'heroicons:star' }}"></iconify-icon>
                                                            </button>
                                                        </form>

                                                        <form method="POST" action="{{ route('admin.reviews.toggle-active', $review) }}" class="inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <button type="submit" class="action-btn" 
                                                                    data-tippy-content="{{ $review->is_active ? 'Deactivate' : 'Activate' }}">
                                                                <iconify-icon icon="{{ $review->is_active ? 'heroicons:eye-slash' : 'heroicons:eye' }}"></iconify-icon>
                                                            </button>
                                                        </form>

                                                        <form method="POST" action="{{ route('admin.reviews.destroy', $review) }}" 
                                                              class="inline" onsubmit="return confirm('Are you sure you want to delete this review?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="action-btn" data-tippy-content="Delete">
                                                                <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $reviews->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <iconify-icon icon="heroicons-outline:chat-bubble-left-ellipsis" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
                        <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No customer reviews found</h3>
                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                            @if(request()->hasAny(['q', 'featured', 'active']))
                                Try adjusting your search criteria.
                            @else
                                Get started by creating your first customer review.
                            @endif
                        </p>
                        @if(!request()->hasAny(['q', 'featured', 'active']))
                            <div class="mt-6">
                                <a href="{{ route('admin.reviews.create') }}" class="btn btn-primary">
                                    <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                                    Create Review
                                </a>
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
