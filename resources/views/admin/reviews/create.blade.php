<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :pageTitle="'Create Customer Review'" :breadcrumbItems="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Customer Reviews', 'url' => route('admin.reviews.index'), 'active' => false],
                ['name' => 'Create Review', 'url' => route('admin.reviews.create'), 'active' => true]
            ]" />
        </div>

        <!-- Create Form -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Create Customer Review</h4>
                <div>
                    <a href="{{ route('admin.reviews.index') }}" class="btn inline-flex justify-center btn-outline-secondary">
                        <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                        Back to Reviews
                    </a>
                </div>
            </header>
            <div class="card-body p-6">
                <form method="POST" action="{{ route('admin.reviews.store') }}" enctype="multipart/form-data">
                    @csrf
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Customer Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="input-area">
                                <label for="customer_name" class="form-label">Customer Name <span class="text-danger-500">*</span></label>
                                <div class="relative">
                                    <input type="text" id="customer_name" name="customer_name" value="{{ old('customer_name') }}"
                                           class="form-control !pl-9 @error('customer_name') !border-danger-500 @enderror"
                                           placeholder="Enter customer name" required>
                                    <iconify-icon icon="heroicons-outline:user" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                </div>
                                @error('customer_name')
                                    <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="input-area">
                                <label for="designation" class="form-label">Designation</label>
                                <div class="relative">
                                    <input type="text" id="designation" name="designation" value="{{ old('designation') }}"
                                           class="form-control !pl-9 @error('designation') !border-danger-500 @enderror"
                                           placeholder="e.g., CEO, Founder">
                                    <iconify-icon icon="heroicons-outline:briefcase" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                </div>
                                @error('designation')
                                    <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="input-area">
                                <label for="company" class="form-label">Company</label>
                                <div class="relative">
                                    <input type="text" id="company" name="company" value="{{ old('company') }}"
                                           class="form-control !pl-9 @error('company') !border-danger-500 @enderror"
                                           placeholder="Enter company name">
                                    <iconify-icon icon="heroicons-outline:building-office" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                </div>
                                @error('company')
                                    <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="input-area">
                                <label for="rating" class="form-label">Rating</label>
                                <select id="rating" name="rating" class="form-control @error('rating') !border-danger-500 @enderror">
                                    <option value="">No Rating</option>
                                    @for($i = 1; $i <= 5; $i++)
                                        <option value="{{ $i }}" {{ old('rating') == $i ? 'selected' : '' }}>
                                            {{ $i }} Star{{ $i > 1 ? 's' : '' }}
                                        </option>
                                    @endfor
                                </select>
                                @error('rating')
                                    <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Review Message -->
                        <div class="input-area">
                            <label for="message" class="form-label">Review Message <span class="text-danger-500">*</span></label>
                            <textarea id="message" name="message" rows="4" 
                                      class="form-control @error('message') !border-danger-500 @enderror"
                                      placeholder="Enter the customer's review message..." required>{{ old('message') }}</textarea>
                            @error('message')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Avatar Upload -->
                        <div class="input-area">
                            <label for="avatar" class="form-label">Customer Avatar</label>
                            <div class="filegroup">
                                <input type="file" id="avatar" name="avatar" accept="image/*"
                                       class="form-control file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100 @error('avatar') !border-danger-500 @enderror">
                            </div>
                            @error('avatar')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                            <div class="text-slate-500 text-sm mt-1">
                                Upload a customer photo (JPEG, PNG, WebP). Max size: 2MB.
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="input-area">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <div class="relative">
                                    <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}"
                                           class="form-control !pl-9 @error('sort_order') !border-danger-500 @enderror"
                                           placeholder="0" min="0">
                                    <iconify-icon icon="heroicons-outline:bars-3" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                </div>
                                @error('sort_order')
                                    <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                @enderror
                                <div class="text-slate-500 text-sm mt-1">
                                    Lower numbers appear first. Default: 0
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="checkbox-area">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="is_featured" value="1" 
                                               class="form-checkbox" {{ old('is_featured') ? 'checked' : '' }}>
                                        <span class="text-slate-500 dark:text-slate-400 text-sm leading-6 ltr:ml-2 rtl:mr-2">
                                            Featured review (shown on homepage)
                                        </span>
                                    </label>
                                </div>

                                <div class="checkbox-area">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="is_active" value="1" 
                                               class="form-checkbox" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <span class="text-slate-500 dark:text-slate-400 text-sm leading-6 ltr:ml-2 rtl:mr-2">
                                            Review is active and visible
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 mt-8">
                        <a href="{{ route('admin.reviews.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:check" class="mr-2"></iconify-icon>
                            Create Review
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
