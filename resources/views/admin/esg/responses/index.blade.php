<x-app-layout>
<div class="space-y-8">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
            <h2 class="text-2xl font-bold leading-7 text-slate-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                ESG Responses Review
            </h2>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Review and analyze startup ESG assessment responses
            </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
            <a href="{{ route('admin.esg.index') }}" class="btn inline-flex justify-center btn-secondary">
                <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Back to ESG Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="p-6">
            <form method="GET" action="{{ route('admin.esg.responses.index') }}" class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
                <!-- Score Range -->
                <div>
                    <label for="min_score" class="block text-sm font-medium text-slate-700 dark:text-slate-300">Min Score</label>
                    <input type="number" name="min_score" id="min_score" value="{{ $filters['min_score'] ?? '' }}" min="0" max="100" step="0.1"
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder="0">
                </div>

                <div>
                    <label for="max_score" class="block text-sm font-medium text-slate-700 dark:text-slate-300">Max Score</label>
                    <input type="number" name="max_score" id="max_score" value="{{ $filters['max_score'] ?? '' }}" min="0" max="100" step="0.1"
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder="100">
                </div>

                <!-- Date Range -->
                <div>
                    <label for="from_date" class="block text-sm font-medium text-slate-700 dark:text-slate-300">From Date</label>
                    <input type="date" name="from_date" id="from_date" value="{{ $filters['from_date'] ?? '' }}"
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                </div>

                <div>
                    <label for="to_date" class="block text-sm font-medium text-slate-700 dark:text-slate-300">To Date</label>
                    <input type="date" name="to_date" id="to_date" value="{{ $filters['to_date'] ?? '' }}"
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                </div>

                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-slate-700 dark:text-slate-300">Search</label>
                    <input type="text" name="search" id="search" value="{{ $filters['search'] ?? '' }}" placeholder="Company or user name..."
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end col-span-full">
                    <button type="submit" class="btn btn-primary mr-3">
                        <iconify-icon icon="heroicons:funnel" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Filter
                    </button>
                    <a href="{{ route('admin.esg.responses.index') }}" class="btn btn-secondary">
                        <iconify-icon icon="heroicons:x-mark" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Responses Table -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white">ESG Assessment Responses</h3>
        </div>
        
        @if($responses->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                <thead class="bg-slate-50 dark:bg-slate-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Company</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">User</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">ESG Score</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Responses</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Completed</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                    @foreach($responses as $response)
                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-slate-900 dark:text-white">
                                {{ $response->company_name ?? 'N/A' }}
                            </div>
                            @if($response->company_description)
                            <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                {{ Str::limit($response->company_description, 60) }}
                            </div>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-slate-900 dark:text-white">{{ $response->user->name }}</div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">{{ $response->user->email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="text-sm font-medium text-slate-900 dark:text-white">
                                    {{ number_format($response->esg_score, 1) }}
                                </div>
                                <div class="ml-2">
                                    @if($response->esg_score >= 90)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        Excellent
                                    </span>
                                    @elseif($response->esg_score >= 80)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                        Good
                                    </span>
                                    @elseif($response->esg_score >= 70)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                        Fair
                                    </span>
                                    @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        Poor
                                    </span>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                            {{ $response->esgResponses->count() }} responses
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                            {{ $response->updated_at->format('M j, Y') }}
                            <div class="text-xs text-slate-400">
                                {{ $response->updated_at->diffForHumans() }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="{{ route('admin.esg.responses.view', $response) }}" 
                               class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                <iconify-icon icon="heroicons:eye" class="w-4 h-4"></iconify-icon>
                                <span class="sr-only">View details</span>
                            </a>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($responses->hasPages())
        <div class="px-6 py-4 border-t border-slate-200 dark:border-slate-700">
            {{ $responses->appends(request()->query())->links() }}
        </div>
        @endif
        @else
        <div class="px-6 py-12 text-center">
            <iconify-icon icon="heroicons:document-text" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
            <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No ESG responses found</h3>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                @if(request()->hasAny(['min_score', 'max_score', 'from_date', 'to_date', 'search']))
                    No responses match your current filters. Try adjusting your search criteria.
                @else
                    No startups have completed ESG assessments yet.
                @endif
            </p>
            @if(request()->hasAny(['min_score', 'max_score', 'from_date', 'to_date', 'search']))
            <div class="mt-6">
                <a href="{{ route('admin.esg.responses.index') }}" class="btn btn-secondary">
                    <iconify-icon icon="heroicons:x-mark" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                    Clear Filters
                </a>
            </div>
            @endif
        </div>
        @endif
    </div>

    <!-- Summary Statistics -->
    @if($responses->count() > 0)
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white mb-4">Response Summary</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $responses->total() }}</div>
                    <div class="text-sm text-slate-500 dark:text-slate-400">Total Responses</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ number_format($responses->avg('esg_score'), 1) }}
                    </div>
                    <div class="text-sm text-slate-500 dark:text-slate-400">Average Score</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ number_format($responses->max('esg_score'), 1) }}
                    </div>
                    <div class="text-sm text-slate-500 dark:text-slate-400">Highest Score</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ number_format($responses->min('esg_score'), 1) }}
                    </div>
                    <div class="text-sm text-slate-500 dark:text-slate-400">Lowest Score</div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
</x-app-layout>
