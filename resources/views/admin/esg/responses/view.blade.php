<x-app-layout>
<div class="space-y-8">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
            <h2 class="text-2xl font-bold leading-7 text-slate-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                ESG Response Details
            </h2>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Detailed view of {{ $startupProfile->company_name ?? $startupProfile->user->name }}'s ESG assessment
            </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
            <a href="{{ route('admin.esg.responses.index') }}" class="btn inline-flex justify-center btn-secondary">
                <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Back to Responses
            </a>
        </div>
    </div>

    <!-- Company Overview -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white">Company Overview</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                    <dt class="text-sm font-medium text-slate-500 dark:text-slate-400">Company Name</dt>
                    <dd class="mt-1 text-sm text-slate-900 dark:text-white">{{ $startupProfile->company_name ?? 'N/A' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-slate-500 dark:text-slate-400">Contact Person</dt>
                    <dd class="mt-1 text-sm text-slate-900 dark:text-white">{{ $startupProfile->user->name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-slate-500 dark:text-slate-400">Email</dt>
                    <dd class="mt-1 text-sm text-slate-900 dark:text-white">{{ $startupProfile->user->email }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-slate-500 dark:text-slate-400">Funding Stage</dt>
                    <dd class="mt-1 text-sm text-slate-900 dark:text-white">{{ ucfirst(str_replace('_', ' ', $startupProfile->funding_stage ?? 'N/A')) }}</dd>
                </div>
                @if($startupProfile->company_description)
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-slate-500 dark:text-slate-400">Description</dt>
                    <dd class="mt-1 text-sm text-slate-900 dark:text-white">{{ $startupProfile->company_description }}</dd>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- ESG Score Overview -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white">ESG Score Overview</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-4">
                <!-- Overall Score -->
                <div class="text-center">
                    <div class="text-3xl font-bold text-slate-900 dark:text-white">
                        {{ number_format($startupProfile->esg_score, 1) }}
                    </div>
                    <div class="text-sm text-slate-500 dark:text-slate-400">Overall Score</div>
                    <div class="mt-2">
                        @if($startupProfile->esg_score >= 90)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Excellent
                        </span>
                        @elseif($startupProfile->esg_score >= 80)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            Good
                        </span>
                        @elseif($startupProfile->esg_score >= 70)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                            Fair
                        </span>
                        @else
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            Poor
                        </span>
                        @endif
                    </div>
                </div>

                <!-- Category Scores -->
                @if(isset($scoreData['category_scores']))
                @foreach(['environmental', 'social', 'governance'] as $category)
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ number_format($scoreData['category_scores'][$category] ?? 0, 1) }}
                    </div>
                    <div class="text-sm text-slate-500 dark:text-slate-400">{{ ucfirst($category) }}</div>
                    <div class="mt-2">
                        <div class="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full" style="width: {{ ($scoreData['category_scores'][$category] ?? 0) }}%"></div>
                        </div>
                    </div>
                </div>
                @endforeach
                @endif
            </div>
        </div>
    </div>

    <!-- ESG Responses by Category -->
    @if($responsesByCategory->isNotEmpty())
    @foreach(['environmental', 'social', 'governance'] as $category)
    @if($responsesByCategory->has($category))
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white flex items-center">
                @if($category === 'environmental')
                <iconify-icon icon="heroicons:leaf" class="w-5 h-5 text-green-600 dark:text-green-400 mr-2"></iconify-icon>
                @elseif($category === 'social')
                <iconify-icon icon="heroicons:users" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></iconify-icon>
                @else
                <iconify-icon icon="heroicons:building-office" class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2"></iconify-icon>
                @endif
                {{ ucfirst($category) }} Responses
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-6">
                @foreach($responsesByCategory[$category] as $response)
                <div class="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-slate-900 dark:text-white">
                                {{ $response->esgQuestion->question_text }}
                            </h4>
                            @if($response->esgQuestion->help_text)
                            <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                {{ $response->esgQuestion->help_text }}
                            </p>
                            @endif
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-200">
                                Weight: {{ $response->esgQuestion->weight }}
                            </span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <dt class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Response</dt>
                        <dd class="mt-1 text-sm text-slate-900 dark:text-white">
                            @if($response->esgQuestion->type === 'yes_no')
                                @if($response->response_value === 'yes')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Yes
                                </span>
                                @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    No
                                </span>
                                @endif
                            @elseif($response->esgQuestion->type === 'scale')
                                <div class="flex items-center">
                                    <span class="text-lg font-medium">{{ $response->response_value }}/5</span>
                                    <div class="ml-3 flex-1 max-w-xs">
                                        <div class="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                                            <div class="bg-primary-600 h-2 rounded-full" style="width: {{ ($response->response_value / 5) * 100 }}%"></div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                {{ $response->response_value }}
                            @endif
                        </dd>
                    </div>
                    @if($response->notes)
                    <div class="mt-3">
                        <dt class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Notes</dt>
                        <dd class="mt-1 text-sm text-slate-900 dark:text-white">{{ $response->notes }}</dd>
                    </div>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
    @endforeach
    @else
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="px-6 py-12 text-center">
            <iconify-icon icon="heroicons:document-text" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
            <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No detailed responses found</h3>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                This startup has an ESG score but no detailed response data is available.
            </p>
        </div>
    </div>
    @endif

    <!-- Assessment Timeline -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white">Assessment Timeline</h3>
        </div>
        <div class="p-6">
            <div class="flow-root">
                <ul role="list" class="-mb-8">
                    <li>
                        <div class="relative pb-8">
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white dark:ring-slate-800">
                                        <iconify-icon icon="heroicons:check" class="w-5 h-5 text-white"></iconify-icon>
                                    </span>
                                </div>
                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                    <div>
                                        <p class="text-sm text-slate-500 dark:text-slate-400">
                                            ESG assessment completed with score of 
                                            <span class="font-medium text-slate-900 dark:text-white">{{ number_format($startupProfile->esg_score, 1) }}</span>
                                        </p>
                                    </div>
                                    <div class="whitespace-nowrap text-right text-sm text-slate-500 dark:text-slate-400">
                                        {{ $startupProfile->updated_at->format('M j, Y') }}
                                        <div class="text-xs">{{ $startupProfile->updated_at->diffForHumans() }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="relative">
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white dark:ring-slate-800">
                                        <iconify-icon icon="heroicons:user-plus" class="w-5 h-5 text-white"></iconify-icon>
                                    </span>
                                </div>
                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                    <div>
                                        <p class="text-sm text-slate-500 dark:text-slate-400">
                                            Company profile created
                                        </p>
                                    </div>
                                    <div class="whitespace-nowrap text-right text-sm text-slate-500 dark:text-slate-400">
                                        {{ $startupProfile->created_at->format('M j, Y') }}
                                        <div class="text-xs">{{ $startupProfile->created_at->diffForHumans() }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
</x-app-layout>
