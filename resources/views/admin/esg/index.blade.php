<x-app-layout>
<div class="space-y-8">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
            <h2 class="text-2xl font-bold leading-7 text-slate-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                ESG Management Dashboard
            </h2>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Manage Environmental, Social, and Governance assessment system
            </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
            <a href="{{ route('admin.esg.questions.create') }}" class="btn inline-flex justify-center btn-primary">
                <iconify-icon icon="heroicons:plus" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Add Question
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        <!-- Total Questions -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:question-mark-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Total Questions</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['total_questions'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Questions -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:check-circle" class="w-5 h-5 text-green-600 dark:text-green-400"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Active Questions</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['active_questions'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Responses -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:chat-bubble-left-right" class="w-5 h-5 text-purple-600 dark:text-purple-400"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Total Responses</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['total_responses'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completed Assessments -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:document-check" class="w-5 h-5 text-indigo-600 dark:text-indigo-400"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['completed_assessments'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Score -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:star" class="w-5 h-5 text-yellow-600 dark:text-yellow-400"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Average Score</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['average_score'], 1) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Reviews -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:clock" class="w-5 h-5 text-orange-600 dark:text-orange-400"></iconify-icon>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Recent (7 days)</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['pending_reviews'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Breakdown -->
    <div class="grid grid-cols-1 gap-5 lg:grid-cols-3">
        <!-- Environmental -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-slate-900 dark:text-white">Environmental</h3>
                        <p class="text-sm text-slate-500 dark:text-slate-400">Environmental impact assessment</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:leaf" class="w-6 h-6 text-green-600 dark:text-green-400"></iconify-icon>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-500 dark:text-slate-400">Questions: {{ $categoryStats['environmental']['questions'] }}</span>
                        <span class="font-medium text-slate-900 dark:text-white">Avg: {{ number_format($categoryStats['environmental']['avg_score'], 1) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-slate-900 dark:text-white">Social</h3>
                        <p class="text-sm text-slate-500 dark:text-slate-400">Social responsibility assessment</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:users" class="w-6 h-6 text-blue-600 dark:text-blue-400"></iconify-icon>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-500 dark:text-slate-400">Questions: {{ $categoryStats['social']['questions'] }}</span>
                        <span class="font-medium text-slate-900 dark:text-white">Avg: {{ number_format($categoryStats['social']['avg_score'], 1) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Governance -->
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-slate-900 dark:text-white">Governance</h3>
                        <p class="text-sm text-slate-500 dark:text-slate-400">Corporate governance assessment</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <iconify-icon icon="heroicons:building-office" class="w-6 h-6 text-purple-600 dark:text-purple-400"></iconify-icon>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-500 dark:text-slate-400">Questions: {{ $categoryStats['governance']['questions'] }}</span>
                        <span class="font-medium text-slate-900 dark:text-white">Avg: {{ number_format($categoryStats['governance']['avg_score'], 1) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <a href="{{ route('admin.esg.questions.index') }}" class="flex items-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                    <iconify-icon icon="heroicons:cog-6-tooth" class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3"></iconify-icon>
                    <div>
                        <div class="text-sm font-medium text-slate-900 dark:text-white">Manage Questions</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">Add, edit, or organize questions</div>
                    </div>
                </a>
                
                <a href="{{ route('admin.esg.responses.index') }}" class="flex items-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                    <iconify-icon icon="heroicons:document-text" class="w-8 h-8 text-green-600 dark:text-green-400 mr-3"></iconify-icon>
                    <div>
                        <div class="text-sm font-medium text-slate-900 dark:text-white">Review Responses</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">Analyze startup assessments</div>
                    </div>
                </a>
                
                <a href="{{ route('admin.esg.configuration') }}" class="flex items-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                    <iconify-icon icon="heroicons:adjustments-horizontal" class="w-8 h-8 text-purple-600 dark:text-purple-400 mr-3"></iconify-icon>
                    <div>
                        <div class="text-sm font-medium text-slate-900 dark:text-white">Scoring Config</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">Configure weights and scoring</div>
                    </div>
                </a>
                
                <a href="{{ route('admin.investment.esg-analytics') }}" class="flex items-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                    <iconify-icon icon="heroicons:chart-bar" class="w-8 h-8 text-orange-600 dark:text-orange-400 mr-3"></iconify-icon>
                    <div>
                        <div class="text-sm font-medium text-slate-900 dark:text-white">View Analytics</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">Detailed scoring analytics</div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    @if($recentActivity->isNotEmpty())
    <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white mb-4">Recent ESG Activity</h3>
            <div class="flow-root">
                <ul role="list" class="-mb-8">
                    @foreach($recentActivity as $index => $activity)
                    <li>
                        <div class="relative pb-8">
                            @if(!$loop->last)
                            <span class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-slate-200 dark:bg-slate-600" aria-hidden="true"></span>
                            @endif
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white dark:ring-slate-800">
                                        <iconify-icon icon="heroicons:check" class="w-5 h-5 text-white"></iconify-icon>
                                    </span>
                                </div>
                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                    <div>
                                        <p class="text-sm text-slate-500 dark:text-slate-400">
                                            {{ $activity['description'] }}
                                            @if(isset($activity['score']))
                                            <span class="font-medium text-slate-900 dark:text-white">(Score: {{ number_format($activity['score'], 1) }})</span>
                                            @endif
                                        </p>
                                    </div>
                                    <div class="whitespace-nowrap text-right text-sm text-slate-500 dark:text-slate-400">
                                        {{ $activity['created_at']->diffForHumans() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
    @endif
</div>
</x-app-layout>
