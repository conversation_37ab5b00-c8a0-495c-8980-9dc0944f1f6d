<x-app-layout>
<div class="space-y-8">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
            <h2 class="text-2xl font-bold leading-7 text-slate-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                Create ESG Question
            </h2>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Add a new question to the ESG assessment questionnaire
            </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
            <a href="{{ route('admin.esg.questions.index') }}" class="btn inline-flex justify-center btn-secondary">
                <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Back to Questions
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <form method="POST" action="{{ route('admin.esg.questions.store') }}" class="space-y-6 p-6">
            @csrf

            <!-- Question Text -->
            <div>
                <label for="question_text" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                    Question Text <span class="text-red-500">*</span>
                </label>
                <textarea name="question_text" id="question_text" rows="3" required
                    class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    placeholder="Enter the ESG question...">{{ old('question_text') }}</textarea>
                @error('question_text')
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <!-- Category and Type -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <select name="category" id="category" required
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        <option value="">Select Category</option>
                        @foreach($categories as $category)
                        <option value="{{ $category }}" {{ old('category') === $category ? 'selected' : '' }}>
                            {{ ucfirst($category) }}
                        </option>
                        @endforeach
                    </select>
                    @error('category')
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Question Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Question Type <span class="text-red-500">*</span>
                    </label>
                    <select name="type" id="type" required
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        <option value="">Select Type</option>
                        @foreach($questionTypes as $type)
                        <option value="{{ $type }}" {{ old('type') === $type ? 'selected' : '' }}>
                            {{ str_replace('_', ' ', ucfirst($type)) }}
                        </option>
                        @endforeach
                    </select>
                    @error('type')
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Options (for multiple choice and scale questions) -->
            <div id="options-section" style="display: none;">
                <label for="options" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                    Answer Options
                </label>
                <div id="options-container" class="mt-2 space-y-2">
                    <!-- Options will be added dynamically -->
                </div>
                <button type="button" id="add-option" class="mt-2 btn btn-sm btn-secondary">
                    <iconify-icon icon="heroicons:plus" class="text-sm ltr:mr-1 rtl:ml-1"></iconify-icon>
                    Add Option
                </button>
            </div>

            <!-- Weight and Sort Order -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Weight -->
                <div>
                    <label for="weight" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Weight <span class="text-red-500">*</span>
                    </label>
                    <input type="number" name="weight" id="weight" min="1" max="20" value="{{ old('weight', 10) }}" required
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">Weight for scoring calculation (1-20)</p>
                    @error('weight')
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Sort Order
                    </label>
                    <input type="number" name="sort_order" id="sort_order" min="0" value="{{ old('sort_order') }}"
                        class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">Leave empty to auto-assign</p>
                    @error('sort_order')
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Help Text -->
            <div>
                <label for="help_text" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                    Help Text
                </label>
                <textarea name="help_text" id="help_text" rows="2"
                    class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    placeholder="Optional help text to guide users...">{{ old('help_text') }}</textarea>
                @error('help_text')
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <!-- Checkboxes -->
            <div class="space-y-4">
                <!-- Required -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_required" id="is_required" value="1" {{ old('is_required') ? 'checked' : '' }}
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 dark:border-slate-600 rounded">
                    <label for="is_required" class="ml-2 block text-sm text-slate-700 dark:text-slate-300">
                        Required question
                    </label>
                </div>

                <!-- Active -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 dark:border-slate-600 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-slate-700 dark:text-slate-300">
                        Active (visible to users)
                    </label>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-slate-200 dark:border-slate-700">
                <a href="{{ route('admin.esg.questions.index') }}" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <iconify-icon icon="heroicons:check" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                    Create Question
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const optionsSection = document.getElementById('options-section');
    const optionsContainer = document.getElementById('options-container');
    const addOptionBtn = document.getElementById('add-option');
    let optionCount = 0;

    // Show/hide options section based on question type
    typeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        if (selectedType === 'multiple_choice' || selectedType === 'scale') {
            optionsSection.style.display = 'block';
            if (optionCount === 0) {
                addOption(); // Add first option
                addOption(); // Add second option
            }
        } else {
            optionsSection.style.display = 'none';
            optionsContainer.innerHTML = '';
            optionCount = 0;
        }
    });

    // Add option functionality
    addOptionBtn.addEventListener('click', function() {
        addOption();
    });

    function addOption() {
        optionCount++;
        const optionDiv = document.createElement('div');
        optionDiv.className = 'flex items-center space-x-2';
        optionDiv.innerHTML = `
            <input type="text" name="options[]" placeholder="Option ${optionCount}" 
                class="flex-1 rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
            <button type="button" onclick="removeOption(this)" class="text-red-600 hover:text-red-800">
                <iconify-icon icon="heroicons:trash" class="w-4 h-4"></iconify-icon>
            </button>
        `;
        optionsContainer.appendChild(optionDiv);
    }

    // Make removeOption function global
    window.removeOption = function(button) {
        button.parentElement.remove();
    };

    // Trigger change event on page load to handle old values
    if (typeSelect.value) {
        typeSelect.dispatchEvent(new Event('change'));
    }
});
</script>
</x-app-layout>
