<x-app-layout>
<div class="space-y-8">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
            <h2 class="text-2xl font-bold leading-7 text-slate-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                ESG Questions Management
            </h2>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Manage questionnaire questions for ESG assessments
            </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
            <a href="{{ route('admin.esg.questions.create') }}" class="btn inline-flex justify-center btn-primary">
                <iconify-icon icon="heroicons:plus" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                Add Question
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="p-6">
            <form method="GET" action="{{ route('admin.esg.questions.index') }}" class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-slate-700 dark:text-slate-300">Category</label>
                    <select name="category" id="category" class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                        <option value="{{ $category }}" {{ $filters['category'] === $category ? 'selected' : '' }}>
                            {{ ucfirst($category) }}
                        </option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-slate-700 dark:text-slate-300">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        <option value="">All Status</option>
                        <option value="active" {{ $filters['status'] === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ $filters['status'] === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>

                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-slate-700 dark:text-slate-300">Search</label>
                    <input type="text" name="search" id="search" value="{{ $filters['search'] }}" placeholder="Search questions..." class="mt-1 block w-full rounded-md border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="btn btn-primary w-full">
                        <iconify-icon icon="heroicons:funnel" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Questions Table -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white">ESG Questions</h3>
        </div>
        
        @if($questions->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                <thead class="bg-slate-50 dark:bg-slate-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Question</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Weight</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Order</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                    @foreach($questions as $question)
                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-slate-900 dark:text-white">
                                {{ Str::limit($question->question_text, 60) }}
                            </div>
                            @if($question->help_text)
                            <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                {{ Str::limit($question->help_text, 80) }}
                            </div>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($question->category === 'environmental') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($question->category === 'social') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @else bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                @endif">
                                {{ ucfirst($question->category) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                            {{ str_replace('_', ' ', ucfirst($question->type)) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                            {{ $question->weight }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($question->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Active
                            </span>
                            @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                Inactive
                            </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                            {{ $question->sort_order }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{{ route('admin.esg.questions.edit', $question) }}" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                    <iconify-icon icon="heroicons:pencil" class="w-4 h-4"></iconify-icon>
                                </a>
                                <form method="POST" action="{{ route('admin.esg.questions.destroy', $question) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this question?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                        <iconify-icon icon="heroicons:trash" class="w-4 h-4"></iconify-icon>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($questions->hasPages())
        <div class="px-6 py-4 border-t border-slate-200 dark:border-slate-700">
            {{ $questions->appends(request()->query())->links() }}
        </div>
        @endif
        @else
        <div class="px-6 py-12 text-center">
            <iconify-icon icon="heroicons:question-mark-circle" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
            <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No questions found</h3>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">Get started by creating your first ESG question.</p>
            <div class="mt-6">
                <a href="{{ route('admin.esg.questions.create') }}" class="btn btn-primary">
                    <iconify-icon icon="heroicons:plus" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                    Add Question
                </a>
            </div>
        </div>
        @endif
    </div>
</div>
</x-app-layout>
