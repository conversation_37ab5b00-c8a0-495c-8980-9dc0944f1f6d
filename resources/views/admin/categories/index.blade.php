<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-primary-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:folder" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Total Categories</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">{{ $stats['total'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-success-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:folder-open" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Parent Categories</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">{{ $stats['parent_categories'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-warning-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:folder-minus" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Child Categories</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">{{ $stats['child_categories'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body p-6">
                    <div class="flex items-center">
                        <div class="flex-none h-12 w-12 rounded-full bg-info-500 text-white flex items-center justify-center">
                            <iconify-icon icon="heroicons:bars-3-bottom-left" class="text-xl"></iconify-icon>
                        </div>
                        <div class="flex-1 ltr:ml-4 rtl:mr-4">
                            <div class="text-slate-600 dark:text-slate-300 text-sm font-medium mb-1">Max Depth</div>
                            <div class="text-slate-900 dark:text-white text-xl font-semibold">{{ $stats['max_depth'] }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories Management -->
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h4 class="card-title">Categories Management</h4>
                    <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                        <iconify-icon icon="heroicons:plus" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Add Category
                    </a>
                </div>
            </div>

            <div class="card-body px-6 pb-6">
                <!-- Search and Filter -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6">
                    <form method="GET" class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="{{ $filters['search'] ?? '' }}"
                                   placeholder="Search categories..."
                                   class="form-control">
                        </div>
                        <div>
                            <select name="type" class="form-control">
                                <option value="">All Types</option>
                                <option value="category" {{ ($filters['type'] ?? '') === 'category' ? 'selected' : '' }}>Category</option>
                                <option value="industry" {{ ($filters['type'] ?? '') === 'industry' ? 'selected' : '' }}>Industry</option>
                                <option value="technology" {{ ($filters['type'] ?? '') === 'technology' ? 'selected' : '' }}>Technology</option>
                                <option value="keyword" {{ ($filters['type'] ?? '') === 'keyword' ? 'selected' : '' }}>Keyword</option>
                            </select>
                        </div>
                        <div>
                            <select name="parent_id" class="form-control">
                                <option value="">All Levels</option>
                                <option value="root" {{ ($filters['parent_id'] ?? '') === 'root' ? 'selected' : '' }}>Root Categories Only</option>
                                @foreach($allCategories as $parent)
                                    @if($parent->type === 'category')
                                        <option value="{{ $parent->id }}" {{ ($filters['parent_id'] ?? '') == $parent->id ? 'selected' : '' }}>
                                            Children of: {{ $parent->name }}
                                        </option>
                                    @endif
                                @endforeach
                            </select>
                        </div>
                        <button type="submit" class="btn btn-outline-primary">
                            <iconify-icon icon="heroicons:magnifying-glass"></iconify-icon>
                            Search
                        </button>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                            <iconify-icon icon="heroicons:x-mark"></iconify-icon>
                            Clear
                        </a>
                    </form>
                </div>

                <!-- Categories Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                        <thead class="border-t border-slate-100 dark:border-slate-800">
                            <tr>
                                <th scope="col" class="table-th">Category Hierarchy</th>
                                <th scope="col" class="table-th">Type</th>
                                <th scope="col" class="table-th">Parent</th>
                                <th scope="col" class="table-th">Children</th>
                                <th scope="col" class="table-th">Description</th>
                                <th scope="col" class="table-th">Created</th>
                                <th scope="col" class="table-th">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                            @forelse($categories as $category)
                                <tr>
                                    <td class="table-td font-medium">
                                        <div class="flex items-center">
                                            <!-- Hierarchical indentation -->
                                            @if($category->depth > 0)
                                                <span class="text-slate-400 mr-2">
                                                    {!! str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $category->depth) !!}
                                                    <iconify-icon icon="heroicons:arrow-turn-down-right" class="text-xs"></iconify-icon>
                                                </span>
                                            @endif

                                            <!-- Category icon based on depth -->
                                            @if($category->depth === 0)
                                                <iconify-icon icon="heroicons:folder" class="text-blue-500 mr-2"></iconify-icon>
                                            @else
                                                <iconify-icon icon="heroicons:folder-minus" class="text-green-500 mr-2"></iconify-icon>
                                            @endif

                                            <span>{{ $category->name }}</span>

                                            <!-- Depth indicator -->
                                            @if($category->depth > 0)
                                                <span class="ml-2 text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                                                    Level {{ $category->depth }}
                                                </span>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <span class="badge
                                            @if($category->type === 'category') badge-primary-light
                                            @elseif($category->type === 'industry') badge-success-light
                                            @elseif($category->type === 'technology') badge-warning-light
                                            @else badge-info-light
                                            @endif">
                                            {{ ucfirst($category->type) }}
                                        </span>
                                    </td>
                                    <td class="table-td">
                                        @if($category->parent_id)
                                            <div class="flex items-center">
                                                <iconify-icon icon="heroicons:arrow-up" class="text-slate-400 mr-1"></iconify-icon>
                                                <span class="text-sm">{{ $category->parent->name ?? 'Unknown' }}</span>
                                            </div>
                                        @else
                                            <span class="text-sm text-slate-400">Root Category</span>
                                        @endif
                                    </td>
                                    <td class="table-td">
                                        @php
                                            $childrenCount = $category->children()->count();
                                        @endphp
                                        @if($childrenCount > 0)
                                            <div class="flex items-center">
                                                <iconify-icon icon="heroicons:arrow-down" class="text-slate-400 mr-1"></iconify-icon>
                                                <span class="text-sm">{{ $childrenCount }} {{ Str::plural('child', $childrenCount) }}</span>
                                            </div>
                                        @else
                                            <span class="text-sm text-slate-400">No children</span>
                                        @endif
                                    </td>
                                    <td class="table-td">
                                        <div class="max-w-xs truncate">{{ $category->description ?: 'No description' }}</div>
                                    </td>
                                    <td class="table-td">{{ $category->created_at->format('M d, Y') }}</td>
                                    <td class="table-td">
                                        <div class="flex space-x-3 rtl:space-x-reverse">
                                            <a href="{{ route('admin.categories.edit', $category) }}" class="action-btn">
                                                <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                            </a>
                                            <form method="POST" action="{{ route('admin.categories.toggle-status', $category) }}" class="inline">
                                                @csrf
                                                @method('PUT')
                                                <button type="button" class="action-btn opacity-50 cursor-not-allowed"
                                                        title="Status toggle not available for taxonomy model" disabled>
                                                    <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                </button>
                                            </form>
                                            <form method="POST" action="{{ route('admin.categories.destroy', $category) }}" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="action-btn text-danger-500" 
                                                        onclick="return confirm('Are you sure you want to delete this category?')">
                                                    <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="table-td text-center py-8">
                                        <div class="text-slate-500">No categories found</div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($categories->hasPages())
                    <div class="mt-6">
                        {{ $categories->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

</x-app-layout>
