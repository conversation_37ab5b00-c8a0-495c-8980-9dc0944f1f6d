<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Edit Category Form -->
        <div class="card">
            <div class="card-body">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h4 class="card-title">Edit Category</h4>
                        <p class="text-slate-600 dark:text-slate-400 mt-1">
                            Editing: <strong>{{ $category->name }}</strong>
                            <span class="text-slate-500"> (Type: {{ ucfirst($category->type) }})</span>
                        </p>
                    </div>
                    <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                        <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Back to Categories
                    </a>
                </div>

                @if ($errors->any())
                    <div class="alert alert-danger mb-6">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.categories.update', $category) }}" class="space-y-6">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Category Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label">Category Name *</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   class="form-control @error('name') !border-danger-500 @enderror" 
                                   value="{{ old('name', $category->name) }}" 
                                   required 
                                   placeholder="Enter category name">
                            @error('name')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Category Type -->
                        <div>
                            <label for="type" class="form-label">Category Type *</label>
                            <select id="type"
                                    name="type"
                                    class="form-control @error('type') !border-danger-500 @enderror"
                                    required>
                                <option value="">Select Type</option>
                                @foreach($categoryTypes as $type)
                                    <option value="{{ $type }}" {{ old('type', $category->type) === $type ? 'selected' : '' }}>
                                        {{ ucfirst($type) }}
                                    </option>
                                @endforeach
                            </select>
                            @error('type')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Parent Category -->
                        <div>
                            <label for="parent_id" class="form-label">Parent Category</label>
                            <select id="parent_id"
                                    name="parent_id"
                                    class="form-control @error('parent_id') !border-danger-500 @enderror">
                                <option value="">None (Root Category)</option>
                                @foreach($parentCategories as $parent)
                                    @if($parent->type === 'category')
                                        <option value="{{ $parent->id }}" {{ old('parent_id', $category->parent_id) == $parent->id ? 'selected' : '' }}>
                                            {{ str_repeat('— ', $parent->depth) }}{{ $parent->name }}
                                        </option>
                                    @endif
                                @endforeach
                            </select>
                            @error('parent_id')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                            <div class="text-slate-500 text-sm mt-1">
                                Select a parent category to make this a subcategory, or leave empty for a root category.
                                @if($category->parent_id)
                                    <br><strong>Current parent:</strong> {{ $category->parent->name ?? 'None' }}
                                @endif
                            </div>
                        </div>


                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="4" 
                                  class="form-control @error('description') !border-danger-500 @enderror" 
                                  placeholder="Enter category description (optional)">{{ old('description', $category->description) }}</textarea>
                        @error('description')
                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Category Information -->
                    <div class="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                        <h5 class="font-medium text-slate-900 dark:text-white mb-3">Category Information</h5>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-slate-600 dark:text-slate-400">Type:</span>
                                <span class="font-medium text-slate-900 dark:text-white">{{ ucfirst($category->type) }}</span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-400">Created:</span>
                                <span class="font-medium text-slate-900 dark:text-white">{{ $category->created_at->format('M j, Y') }}</span>
                            </div>
                            <div>
                                <span class="text-slate-600 dark:text-slate-400">Last Updated:</span>
                                <span class="font-medium text-slate-900 dark:text-white">{{ $category->updated_at->format('M j, Y') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 pt-6 border-t border-slate-100 dark:border-slate-700">
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons:check" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Update Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


</x-app-layout>
