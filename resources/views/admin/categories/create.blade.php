<x-app-layout>
    <div class="space-y-8">
        <!-- Breadcrumb -->
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Create Category Form -->
        <div class="card">
            <div class="card-body space-y-6 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h4 class="card-title">Create New Category</h4>
                    <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                        <iconify-icon icon="heroicons:arrow-left" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                        Back to Categories
                    </a>
                </div>

                @if ($errors->any())
                    <div class="alert alert-danger mb-6">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.categories.store') }}" class="space-y-6">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Category Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label">Category Name *</label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   class="form-control @error('name') !border-danger-500 @enderror"
                                   value="{{ old('name') }}"
                                   required
                                   placeholder="Enter category name">
                            @error('name')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Category Type -->
                        <div>
                            <label for="type" class="form-label">Category Type *</label>
                            <select id="type"
                                    name="type"
                                    class="form-control @error('type') !border-danger-500 @enderror"
                                    required>
                                <option value="">Select Type</option>
                                @foreach($categoryTypes as $type)
                                    <option value="{{ $type }}" {{ old('type') === $type ? 'selected' : '' }}>
                                        {{ ucfirst($type) }}
                                    </option>
                                @endforeach
                            </select>
                            @error('type')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Parent Category -->
                        <div>
                            <label for="parent_id" class="form-label">Parent Category</label>
                            <select id="parent_id"
                                    name="parent_id"
                                    class="form-control @error('parent_id') !border-danger-500 @enderror">
                                <option value="">None (Root Category)</option>
                                @foreach($parentCategories as $parent)
                                    @if($parent->type === 'category')
                                        <option value="{{ $parent->id }}" {{ old('parent_id') == $parent->id ? 'selected' : '' }}>
                                            {{ str_repeat('— ', $parent->depth) }}{{ $parent->name }}
                                        </option>
                                    @endif
                                @endforeach
                            </select>
                            @error('parent_id')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                            <div class="text-slate-500 text-sm mt-1">
                                Select a parent category to create a subcategory, or leave empty for a root category.
                            </div>
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number"
                                   id="sort_order"
                                   name="sort_order"
                                   value="{{ old('sort_order', 0) }}"
                                   class="form-control @error('sort_order') !border-danger-500 @enderror"
                                   placeholder="0"
                                   min="0">
                            @error('sort_order')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                            <div class="text-slate-500 text-sm mt-1">
                                Lower numbers appear first. Default is 0.
                            </div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="form-label">Status</label>
                            <div class="flex items-center space-x-3">
                                <label class="inline-flex items-center">
                                    <input type="radio"
                                           name="is_active"
                                           value="1"
                                           class="form-radio"
                                           {{ old('is_active', '1') === '1' ? 'checked' : '' }}>
                                    <span class="ltr:ml-2 rtl:mr-2">Active</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio"
                                           name="is_active"
                                           value="0"
                                           class="form-radio"
                                           {{ old('is_active') === '0' ? 'checked' : '' }}>
                                    <span class="ltr:ml-2 rtl:mr-2">Inactive</span>
                                </label>
                            </div>
                            @error('is_active')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description"
                                  name="description"
                                  rows="4"
                                  class="form-control @error('description') !border-danger-500 @enderror"
                                  placeholder="Enter category description (optional)">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 pt-6 border-t border-slate-100 dark:border-slate-700">
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons:plus" class="text-lg ltr:mr-2 rtl:ml-2"></iconify-icon>
                            Create Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


</x-app-layout>
