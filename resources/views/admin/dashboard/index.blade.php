<x-app-layout>

    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0">{{ __('Admin Dashboard') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:calendar"></iconify-icon>
                    <span>This Month</span>
                </span>
            </button>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:filter"></iconify-icon>
                    <span>Filter</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->



    <!-- Role-based KPI Dashboard -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Role-based Analytics Dashboard') }}</h4>
                    <div class="text-sm text-slate-500">Comprehensive metrics by user roles</div>
                </header>
                <div class="card-body p-6">
                    <!-- Investor Metrics -->
                    <div class="mb-8">
                        <h5 class="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                            <iconify-icon class="text-2xl text-success-500 mr-2" icon="heroicons-outline:user-group"></iconify-icon>
                            Investor Metrics
                        </h5>
                        <div class="grid md:grid-cols-4 grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($metrics['totalInvestors']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Total Investor</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($metrics['activeInvestors']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Active Investor</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($metrics['yearlyInvestors']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Yearly Investor</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($metrics['monthlyInvestors']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Monthly Investor</div>
                            </div>
                        </div>
                    </div>

                    <!-- Startup Metrics -->
                    <div class="mb-8">
                        <h5 class="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                            <iconify-icon class="text-2xl text-info-500 mr-2" icon="heroicons-outline:office-building"></iconify-icon>
                            Startup Metrics
                        </h5>
                        <div class="grid md:grid-cols-4 grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($metrics['totalStartups']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Total Startup</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($metrics['activeStartups']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Active Startup</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($metrics['yearlyStartups']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Yearly Startup</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($metrics['monthlyStartups']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Monthly Startup</div>
                            </div>
                        </div>
                    </div>

                    <!-- Consultant Metrics -->
                    <div class="mb-6">
                        <h5 class="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                            <iconify-icon class="text-2xl text-warning-500 mr-2" icon="heroicons-outline:academic-cap"></iconify-icon>
                            Consultant Metrics
                        </h5>
                        <div class="grid md:grid-cols-4 grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                <div class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($metrics['totalConsultants']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Total Consultant</div>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                <div class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($metrics['activeConsultants']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Active Consultant</div>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                <div class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($metrics['yearlyConsultants']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Yearly Consultant</div>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                <div class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($metrics['monthlyConsultants']) }}</div>
                                <div class="text-sm text-slate-600 dark:text-slate-400">Monthly Consultant</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-app-layout>