<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :pageTitle="'Contact Submission Details'" :breadcrumbItems="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Contact Submissions', 'url' => route('admin.contacts.index'), 'active' => false],
                ['name' => 'Contact Details', 'url' => route('admin.contacts.show', $contact), 'active' => true]
            ]" />
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
            <!-- Contact Information -->
            <div class="xl:col-span-2">
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">Contact Information</h4>
                        <div>
                            <a href="{{ route('admin.contacts.index') }}" class="btn inline-flex justify-center btn-outline-secondary">
                                <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                                Back to Contacts
                            </a>
                        </div>
                    </header>
                    <div class="card-body p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Name</label>
                                <div class="text-slate-900 dark:text-white font-medium">{{ $contact->name }}</div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Email</label>
                                <div class="text-slate-900 dark:text-white font-medium">
                                    <a href="mailto:{{ $contact->email }}" class="text-primary-500 hover:text-primary-600">
                                        {{ $contact->email }}
                                    </a>
                                </div>
                            </div>
                            @if($contact->phone)
                                <div>
                                    <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Phone</label>
                                    <div class="text-slate-900 dark:text-white font-medium">
                                        <a href="tel:{{ $contact->phone }}" class="text-primary-500 hover:text-primary-600">
                                            {{ $contact->phone }}
                                        </a>
                                    </div>
                                </div>
                            @endif
                            <div>
                                <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Subject</label>
                                <div class="text-slate-900 dark:text-white font-medium">{{ $contact->subject }}</div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Status</label>
                                <div>
                                    <span class="badge {{ $contact->getStatusBadgeColor() }} text-white">
                                        {{ ucfirst($contact->status) }}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Submitted</label>
                                <div class="text-slate-900 dark:text-white font-medium">
                                    {{ $contact->created_at->format('M d, Y \a\t h:i A') }}
                                </div>
                            </div>
                        </div>

                        <!-- Message -->
                        <div class="mt-6">
                            <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Message</label>
                            <div class="mt-2 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                <div class="text-slate-900 dark:text-white whitespace-pre-wrap">{{ $contact->message }}</div>
                            </div>
                        </div>

                        <!-- Technical Information -->
                        @if($contact->ip_address || $contact->user_agent)
                            <div class="mt-6 pt-6 border-t border-slate-200 dark:border-slate-700">
                                <h5 class="text-sm font-medium text-slate-600 dark:text-slate-300 mb-4">Technical Information</h5>
                                <div class="grid grid-cols-1 gap-4">
                                    @if($contact->ip_address)
                                        <div>
                                            <label class="text-xs font-medium text-slate-500 dark:text-slate-400">IP Address</label>
                                            <div class="text-slate-700 dark:text-slate-300 text-sm font-mono">{{ $contact->ip_address }}</div>
                                        </div>
                                    @endif
                                    @if($contact->user_agent)
                                        <div>
                                            <label class="text-xs font-medium text-slate-500 dark:text-slate-400">User Agent</label>
                                            <div class="text-slate-700 dark:text-slate-300 text-sm font-mono break-all">{{ $contact->user_agent }}</div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Actions & Notes -->
            <div class="xl:col-span-1">
                <!-- Quick Actions -->
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">Quick Actions</h4>
                    </header>
                    <div class="card-body p-6">
                        <div class="space-y-3">
                            <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject }}" 
                               class="btn btn-primary w-full">
                                <iconify-icon icon="heroicons-outline:envelope" class="mr-2"></iconify-icon>
                                Reply via Email
                            </a>

                            @if($contact->status !== 'replied')
                                <form method="POST" action="{{ route('admin.contacts.mark-replied', $contact) }}">
                                    @csrf
                                    @method('PUT')
                                    <button type="submit" class="btn btn-success w-full">
                                        <iconify-icon icon="heroicons-outline:check-circle" class="mr-2"></iconify-icon>
                                        Mark as Replied
                                    </button>
                                </form>
                            @endif

                            @if($contact->status !== 'archived')
                                <form method="POST" action="{{ route('admin.contacts.archive', $contact) }}">
                                    @csrf
                                    @method('PUT')
                                    <button type="submit" class="btn btn-outline-secondary w-full">
                                        <iconify-icon icon="heroicons-outline:archive-box" class="mr-2"></iconify-icon>
                                        Archive
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Admin Notes -->
                <div class="card mt-6">
                    <header class="card-header">
                        <h4 class="card-title">Admin Notes</h4>
                    </header>
                    <div class="card-body p-6">
                        <form method="POST" action="{{ route('admin.contacts.update', $contact) }}">
                            @csrf
                            @method('PUT')
                            
                            <div class="input-area mb-4">
                                <label for="status" class="form-label">Status</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="new" {{ $contact->status === 'new' ? 'selected' : '' }}>New</option>
                                    <option value="read" {{ $contact->status === 'read' ? 'selected' : '' }}>Read</option>
                                    <option value="replied" {{ $contact->status === 'replied' ? 'selected' : '' }}>Replied</option>
                                    <option value="archived" {{ $contact->status === 'archived' ? 'selected' : '' }}>Archived</option>
                                </select>
                            </div>

                            <div class="input-area mb-4">
                                <label for="admin_notes" class="form-label">Notes</label>
                                <textarea id="admin_notes" name="admin_notes" rows="4" 
                                          class="form-control" 
                                          placeholder="Add internal notes about this contact submission...">{{ $contact->admin_notes }}</textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-full">
                                <iconify-icon icon="heroicons-outline:check" class="mr-2"></iconify-icon>
                                Update
                            </button>
                        </form>

                        @if($contact->read_at)
                            <div class="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    <div>Read: {{ $contact->read_at->format('M d, Y \a\t h:i A') }}</div>
                                    @if($contact->readBy)
                                        <div>By: {{ $contact->readBy->name }}</div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
