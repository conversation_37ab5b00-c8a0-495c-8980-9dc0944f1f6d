<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :pageTitle="'Contact Submissions Management'" :breadcrumbItems="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Contact Submissions', 'url' => route('admin.contacts.index'), 'active' => true]
            ]" />
        </div>

        <!-- Filters -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </header>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.contacts.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="input-area">
                        <label class="form-label">Search</label>
                        <div class="relative">
                            <input type="text" name="q" value="{{ $filters['q'] ?? '' }}"
                                   class="form-control !pl-9" placeholder="Search by name, email, subject...">
                            <iconify-icon icon="heroicons-outline:search" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                        </div>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="">All Statuses</option>
                            @foreach($statusOptions as $statusOption)
                                <option value="{{ $statusOption }}" {{ ($filters['status'] ?? '') === $statusOption ? 'selected' : '' }}>
                                    {{ ucfirst($statusOption) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Per Page</label>
                        <select name="per_page" class="form-control">
                            <option value="10" {{ ($filters['per_page'] ?? 10) == 10 ? 'selected' : '' }}>10</option>
                            <option value="25" {{ ($filters['per_page'] ?? 10) == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ ($filters['per_page'] ?? 10) == 50 ? 'selected' : '' }}>50</option>
                        </select>
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:search" class="mr-2"></iconify-icon>
                            Search
                        </button>
                        <a href="{{ route('admin.contacts.index') }}" class="btn btn-outline-secondary">
                            <iconify-icon icon="heroicons-outline:x-mark" class="mr-2"></iconify-icon>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Contact Submissions Table -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Contact Submissions ({{ $contacts->total() }})</h4>
            </header>
            <div class="card-body px-6 pb-6">
                @if($contacts->count() > 0)
                    <div class="overflow-x-auto -mx-6">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                    <thead class="bg-slate-200 dark:bg-slate-700">
                                        <tr>
                                            <th scope="col" class="table-th">Contact Info</th>
                                            <th scope="col" class="table-th">Subject</th>
                                            <th scope="col" class="table-th">Status</th>
                                            <th scope="col" class="table-th">Submitted</th>
                                            <th scope="col" class="table-th">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @foreach($contacts as $contact)
                                            <tr class="hover:bg-slate-50 dark:hover:bg-slate-700 {{ $contact->isNew() ? 'bg-yellow-50 dark:bg-yellow-900/20' : '' }}">
                                                <td class="table-td">
                                                    <div>
                                                        <div class="text-slate-600 dark:text-slate-300 text-sm font-medium flex items-center">
                                                            @if($contact->isNew())
                                                                <span class="w-2 h-2 bg-warning-500 rounded-full mr-2"></span>
                                                            @endif
                                                            {{ $contact->name }}
                                                        </div>
                                                        <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                            {{ $contact->email }}
                                                        </div>
                                                        @if($contact->phone)
                                                            <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                                {{ $contact->phone }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <div class="text-slate-600 dark:text-slate-300 text-sm font-medium">
                                                        {{ $contact->subject }}
                                                    </div>
                                                    <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                        {{ Str::limit($contact->message, 60) }}
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <span class="badge {{ $contact->getStatusBadgeColor() }} text-white">
                                                        {{ ucfirst($contact->status) }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <div class="text-slate-600 dark:text-slate-300 text-sm">
                                                        {{ $contact->created_at->format('M d, Y') }}
                                                    </div>
                                                    <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                        {{ $contact->created_at->format('h:i A') }}
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex space-x-3 rtl:space-x-reverse">
                                                        <a href="{{ route('admin.contacts.show', $contact) }}" 
                                                           class="action-btn" data-tippy-content="View Details">
                                                            <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                        </a>
                                                        
                                                        @if($contact->status !== 'replied')
                                                            <form method="POST" action="{{ route('admin.contacts.mark-replied', $contact) }}" class="inline">
                                                                @csrf
                                                                @method('PUT')
                                                                <button type="submit" class="action-btn" data-tippy-content="Mark as Replied">
                                                                    <iconify-icon icon="heroicons:check-circle"></iconify-icon>
                                                                </button>
                                                            </form>
                                                        @endif

                                                        @if($contact->status !== 'archived')
                                                            <form method="POST" action="{{ route('admin.contacts.archive', $contact) }}" class="inline">
                                                                @csrf
                                                                @method('PUT')
                                                                <button type="submit" class="action-btn" data-tippy-content="Archive">
                                                                    <iconify-icon icon="heroicons:archive-box"></iconify-icon>
                                                                </button>
                                                            </form>
                                                        @endif

                                                        <form method="POST" action="{{ route('admin.contacts.destroy', $contact) }}" 
                                                              class="inline" onsubmit="return confirm('Are you sure you want to delete this contact submission?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="action-btn" data-tippy-content="Delete">
                                                                <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $contacts->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <iconify-icon icon="heroicons-outline:envelope" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
                        <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No contact submissions found</h3>
                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                            @if(request()->hasAny(['q', 'status']))
                                Try adjusting your search criteria.
                            @else
                                Contact submissions will appear here when users submit the contact form.
                            @endif
                        </p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
