<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Alert Messages -->
        @if (session('message') && session('type'))
            <x-alert :message="session('message')" :type="session('type')"/>
        @endif

        <!-- Logo Management Form -->
        <div class="grid grid-cols-12 gap-5">
            <div class="col-span-12">
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">{{ __('Logo Management') }}</h4>
                        <div class="text-sm text-slate-500">Upload and manage your website logos for different themes and locations</div>
                    </header>
                    <div class="card-body p-6">
                        <form action="{{ route('admin.website-settings.update-logos') }}" 
                              method="POST" 
                              enctype="multipart/form-data"
                              class="space-y-8">
                            @csrf

                            <!-- Header Logos Section -->
                            <div>
                                <h5 class="text-lg font-semibold text-slate-900 dark:text-white mb-6 flex items-center">
                                    <iconify-icon class="text-2xl text-blue-500 mr-2" icon="heroicons-outline:desktop-computer"></iconify-icon>
                                    Header Logos
                                </h5>
                                <div class="grid lg:grid-cols-2 grid-cols-1 gap-6">
                                    
                                    <!-- Header Logo (Light Theme) -->
                                    <div class="bg-slate-50 dark:bg-slate-900 rounded-lg p-6 border border-slate-200 dark:border-slate-700">
                                        <div class="text-center">
                                            <h6 class="text-md font-medium text-slate-900 dark:text-white mb-4">Light Theme Header Logo</h6>
                                            <div class="mb-4">
                                                <div class="w-full h-32 bg-white border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
                                                    @if($settings->header_logo)
                                                        <img id="headerLogoPreview" 
                                                             src="{{ $settings->header_logo_url }}" 
                                                             alt="Header Logo" 
                                                             class="max-h-28 max-w-full object-contain">
                                                    @else
                                                        <div id="headerLogoPreview" class="text-slate-400 text-center">
                                                            <iconify-icon class="text-4xl mb-2" icon="heroicons-outline:photograph"></iconify-icon>
                                                            <p class="text-sm">No logo uploaded</p>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="relative">
                                                <input type="file" 
                                                       name="header_logo" 
                                                       id="header_logo"
                                                       class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                       accept="image/jpeg,image/jpg,image/png,image/svg+xml"
                                                       onchange="previewImage(event, 'headerLogoPreview')">
                                                <label for="header_logo" class="btn btn-outline-primary cursor-pointer">
                                                    <iconify-icon class="mr-2" icon="heroicons-outline:upload"></iconify-icon>
                                                    Choose Header Logo
                                                </label>
                                            </div>
                                            <div class="mt-2 text-xs text-slate-500">
                                                Max size: 5MB | Formats: JPEG, PNG, SVG | Max dimensions: 1200x400px
                                            </div>
                                            @error('header_logo')
                                                <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Header Logo (Dark Theme) -->
                                    <div class="bg-slate-800 rounded-lg p-6 border border-slate-600">
                                        <div class="text-center">
                                            <h6 class="text-md font-medium text-white mb-4">Dark Theme Header Logo</h6>
                                            <div class="mb-4">
                                                <div class="w-full h-32 bg-slate-700 border-2 border-dashed border-slate-500 rounded-lg flex items-center justify-center">
                                                    @if($settings->header_logo_dark)
                                                        <img id="headerLogoDarkPreview" 
                                                             src="{{ $settings->header_logo_dark_url }}" 
                                                             alt="Header Logo Dark" 
                                                             class="max-h-28 max-w-full object-contain">
                                                    @else
                                                        <div id="headerLogoDarkPreview" class="text-slate-400 text-center">
                                                            <iconify-icon class="text-4xl mb-2" icon="heroicons-outline:photograph"></iconify-icon>
                                                            <p class="text-sm">No dark logo uploaded</p>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="relative">
                                                <input type="file" 
                                                       name="header_logo_dark" 
                                                       id="header_logo_dark"
                                                       class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                       accept="image/jpeg,image/jpg,image/png,image/svg+xml"
                                                       onchange="previewImage(event, 'headerLogoDarkPreview')">
                                                <label for="header_logo_dark" class="btn btn-outline-light cursor-pointer">
                                                    <iconify-icon class="mr-2" icon="heroicons-outline:upload"></iconify-icon>
                                                    Choose Dark Header Logo
                                                </label>
                                            </div>
                                            <div class="mt-2 text-xs text-slate-400">
                                                Max size: 5MB | Formats: JPEG, PNG, SVG | Max dimensions: 1200x400px
                                            </div>
                                            @error('header_logo_dark')
                                                <div class="mt-2 text-sm text-red-400">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!-- Footer Logos Section -->
                            <div>
                                <h5 class="text-lg font-semibold text-slate-900 dark:text-white mb-6 flex items-center">
                                    <iconify-icon class="text-2xl text-green-500 mr-2" icon="heroicons-outline:template"></iconify-icon>
                                    Footer Logos
                                </h5>
                                <div class="grid lg:grid-cols-2 grid-cols-1 gap-6">
                                    
                                    <!-- Footer Logo (Light Theme) -->
                                    <div class="bg-slate-50 dark:bg-slate-900 rounded-lg p-6 border border-slate-200 dark:border-slate-700">
                                        <div class="text-center">
                                            <h6 class="text-md font-medium text-slate-900 dark:text-white mb-4">Light Theme Footer Logo</h6>
                                            <div class="mb-4">
                                                <div class="w-full h-24 bg-white border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
                                                    @if($settings->footer_logo)
                                                        <img id="footerLogoPreview" 
                                                             src="{{ $settings->footer_logo_url }}" 
                                                             alt="Footer Logo" 
                                                             class="max-h-20 max-w-full object-contain">
                                                    @else
                                                        <div id="footerLogoPreview" class="text-slate-400 text-center">
                                                            <iconify-icon class="text-3xl mb-1" icon="heroicons-outline:photograph"></iconify-icon>
                                                            <p class="text-xs">No footer logo</p>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="relative">
                                                <input type="file" 
                                                       name="footer_logo" 
                                                       id="footer_logo"
                                                       class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                       accept="image/jpeg,image/jpg,image/png,image/svg+xml"
                                                       onchange="previewImage(event, 'footerLogoPreview')">
                                                <label for="footer_logo" class="btn btn-outline-primary cursor-pointer">
                                                    <iconify-icon class="mr-2" icon="heroicons-outline:upload"></iconify-icon>
                                                    Choose Footer Logo
                                                </label>
                                            </div>
                                            <div class="mt-2 text-xs text-slate-500">
                                                Max size: 2MB | Formats: JPEG, PNG, SVG | Max dimensions: 600x200px
                                            </div>
                                            @error('footer_logo')
                                                <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Footer Logo (Dark Theme) -->
                                    <div class="bg-slate-800 rounded-lg p-6 border border-slate-600">
                                        <div class="text-center">
                                            <h6 class="text-md font-medium text-white mb-4">Dark Theme Footer Logo</h6>
                                            <div class="mb-4">
                                                <div class="w-full h-24 bg-slate-700 border-2 border-dashed border-slate-500 rounded-lg flex items-center justify-center">
                                                    @if($settings->footer_logo_dark)
                                                        <img id="footerLogoDarkPreview" 
                                                             src="{{ $settings->footer_logo_dark_url }}" 
                                                             alt="Footer Logo Dark" 
                                                             class="max-h-20 max-w-full object-contain">
                                                    @else
                                                        <div id="footerLogoDarkPreview" class="text-slate-400 text-center">
                                                            <iconify-icon class="text-3xl mb-1" icon="heroicons-outline:photograph"></iconify-icon>
                                                            <p class="text-xs">No dark footer logo</p>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="relative">
                                                <input type="file" 
                                                       name="footer_logo_dark" 
                                                       id="footer_logo_dark"
                                                       class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                       accept="image/jpeg,image/jpg,image/png,image/svg+xml"
                                                       onchange="previewImage(event, 'footerLogoDarkPreview')">
                                                <label for="footer_logo_dark" class="btn btn-outline-light cursor-pointer">
                                                    <iconify-icon class="mr-2" icon="heroicons-outline:upload"></iconify-icon>
                                                    Choose Dark Footer Logo
                                                </label>
                                            </div>
                                            <div class="mt-2 text-xs text-slate-400">
                                                Max size: 2MB | Formats: JPEG, PNG, SVG | Max dimensions: 600x200px
                                            </div>
                                            @error('footer_logo_dark')
                                                <div class="mt-2 text-sm text-red-400">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex justify-between items-center pt-6 border-t border-slate-200 dark:border-slate-700">
                                <a href="{{ route('admin.website-settings.index') }}" 
                                   class="btn btn-outline-secondary">
                                    <iconify-icon class="mr-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                                    Back to Settings
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <iconify-icon class="mr-2" icon="heroicons-outline:save"></iconify-icon>
                                    Update Logos
                                </button>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function previewImage(event, previewId) {
            const file = event.target.files[0];
            const preview = document.getElementById(previewId);
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="max-h-28 max-w-full object-contain">`;
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
    @endpush
</x-app-layout>
