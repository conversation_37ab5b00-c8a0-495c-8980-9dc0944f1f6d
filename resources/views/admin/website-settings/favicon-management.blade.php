<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Alert Messages -->
        @if (session('message') && session('type'))
            <x-alert :message="session('message')" :type="session('type')"/>
        @endif

        <!-- Favicon Management Form -->
        <div class="grid grid-cols-12 gap-5">
            <div class="col-span-12">
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">{{ __('Favicon Management') }}</h4>
                        <div class="text-sm text-slate-500">Upload and manage your website favicons for different browsers and devices</div>
                    </header>
                    <div class="card-body p-6">
                        <form action="{{ route('admin.website-settings.update-favicons') }}" 
                              method="POST" 
                              enctype="multipart/form-data"
                              class="space-y-8">
                            @csrf

                            <!-- Favicon Information -->
                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                                <div class="flex items-start space-x-4">
                                    <div class="w-12 h-12 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center flex-shrink-0">
                                        <iconify-icon class="text-2xl text-blue-600" icon="heroicons-outline:information-circle"></iconify-icon>
                                    </div>
                                    <div>
                                        <h5 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">About Favicons</h5>
                                        <p class="text-blue-800 dark:text-blue-200 text-sm mb-3">
                                            Favicons are small icons that appear in browser tabs, bookmarks, and mobile home screens. 
                                            Different formats and sizes are used for optimal display across various devices and browsers.
                                        </p>
                                        <ul class="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                                            <li>• <strong>ICO format:</strong> Traditional favicon for older browsers</li>
                                            <li>• <strong>PNG 16x16:</strong> Standard browser tab icon</li>
                                            <li>• <strong>PNG 32x32:</strong> High-resolution browser tab icon</li>
                                            <li>• <strong>PNG 192x192:</strong> Android home screen icon</li>
                                            <li>• <strong>SVG format:</strong> Scalable vector icon for modern browsers</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Favicon Upload Grid -->
                            <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-6">
                                
                                <!-- ICO Favicon -->
                                <div class="bg-slate-50 dark:bg-slate-900 rounded-lg p-6 border border-slate-200 dark:border-slate-700">
                                    <div class="text-center">
                                        <h6 class="text-md font-medium text-slate-900 dark:text-white mb-4">ICO Format</h6>
                                        <div class="mb-4">
                                            <div class="w-16 h-16 mx-auto bg-white border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
                                                @if($settings->favicon_ico)
                                                    <img id="faviconIcoPreview" 
                                                         src="{{ $settings->favicon_ico_url }}" 
                                                         alt="Favicon ICO" 
                                                         class="w-8 h-8 object-contain">
                                                @else
                                                    <div id="faviconIcoPreview" class="text-slate-400">
                                                        <iconify-icon class="text-2xl" icon="heroicons-outline:globe-alt"></iconify-icon>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input type="file" 
                                                   name="favicon_ico" 
                                                   id="favicon_ico"
                                                   class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                   accept=".ico"
                                                   onchange="previewFavicon(event, 'faviconIcoPreview')">
                                            <label for="favicon_ico" class="btn btn-outline-primary btn-sm cursor-pointer">
                                                <iconify-icon class="mr-1" icon="heroicons-outline:upload"></iconify-icon>
                                                Choose ICO
                                            </label>
                                        </div>
                                        <div class="mt-2 text-xs text-slate-500">
                                            Max: 1MB | .ico format
                                        </div>
                                        @error('favicon_ico')
                                            <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- PNG 16x16 -->
                                <div class="bg-slate-50 dark:bg-slate-900 rounded-lg p-6 border border-slate-200 dark:border-slate-700">
                                    <div class="text-center">
                                        <h6 class="text-md font-medium text-slate-900 dark:text-white mb-4">PNG 16x16</h6>
                                        <div class="mb-4">
                                            <div class="w-16 h-16 mx-auto bg-white border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
                                                @if($settings->favicon_png_16)
                                                    <img id="faviconPng16Preview" 
                                                         src="{{ $settings->favicon_png_16_url }}" 
                                                         alt="Favicon PNG 16x16" 
                                                         class="w-4 h-4 object-contain">
                                                @else
                                                    <div id="faviconPng16Preview" class="text-slate-400">
                                                        <iconify-icon class="text-xl" icon="heroicons-outline:globe-alt"></iconify-icon>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input type="file" 
                                                   name="favicon_png_16" 
                                                   id="favicon_png_16"
                                                   class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                   accept="image/png"
                                                   onchange="previewFavicon(event, 'faviconPng16Preview')">
                                            <label for="favicon_png_16" class="btn btn-outline-primary btn-sm cursor-pointer">
                                                <iconify-icon class="mr-1" icon="heroicons-outline:upload"></iconify-icon>
                                                Choose PNG
                                            </label>
                                        </div>
                                        <div class="mt-2 text-xs text-slate-500">
                                            Max: 512KB | PNG format
                                        </div>
                                        @error('favicon_png_16')
                                            <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- PNG 32x32 -->
                                <div class="bg-slate-50 dark:bg-slate-900 rounded-lg p-6 border border-slate-200 dark:border-slate-700">
                                    <div class="text-center">
                                        <h6 class="text-md font-medium text-slate-900 dark:text-white mb-4">PNG 32x32</h6>
                                        <div class="mb-4">
                                            <div class="w-16 h-16 mx-auto bg-white border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
                                                @if($settings->favicon_png_32)
                                                    <img id="faviconPng32Preview" 
                                                         src="{{ $settings->favicon_png_32_url }}" 
                                                         alt="Favicon PNG 32x32" 
                                                         class="w-8 h-8 object-contain">
                                                @else
                                                    <div id="faviconPng32Preview" class="text-slate-400">
                                                        <iconify-icon class="text-2xl" icon="heroicons-outline:globe-alt"></iconify-icon>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input type="file" 
                                                   name="favicon_png_32" 
                                                   id="favicon_png_32"
                                                   class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                   accept="image/png"
                                                   onchange="previewFavicon(event, 'faviconPng32Preview')">
                                            <label for="favicon_png_32" class="btn btn-outline-primary btn-sm cursor-pointer">
                                                <iconify-icon class="mr-1" icon="heroicons-outline:upload"></iconify-icon>
                                                Choose PNG
                                            </label>
                                        </div>
                                        <div class="mt-2 text-xs text-slate-500">
                                            Max: 512KB | PNG format
                                        </div>
                                        @error('favicon_png_32')
                                            <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- PNG 192x192 -->
                                <div class="bg-slate-50 dark:bg-slate-900 rounded-lg p-6 border border-slate-200 dark:border-slate-700">
                                    <div class="text-center">
                                        <h6 class="text-md font-medium text-slate-900 dark:text-white mb-4">PNG 192x192</h6>
                                        <div class="mb-4">
                                            <div class="w-16 h-16 mx-auto bg-white border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
                                                @if($settings->favicon_png_192)
                                                    <img id="faviconPng192Preview" 
                                                         src="{{ $settings->favicon_png_192_url }}" 
                                                         alt="Favicon PNG 192x192" 
                                                         class="w-12 h-12 object-contain">
                                                @else
                                                    <div id="faviconPng192Preview" class="text-slate-400">
                                                        <iconify-icon class="text-3xl" icon="heroicons-outline:globe-alt"></iconify-icon>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input type="file" 
                                                   name="favicon_png_192" 
                                                   id="favicon_png_192"
                                                   class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                   accept="image/png"
                                                   onchange="previewFavicon(event, 'faviconPng192Preview')">
                                            <label for="favicon_png_192" class="btn btn-outline-primary btn-sm cursor-pointer">
                                                <iconify-icon class="mr-1" icon="heroicons-outline:upload"></iconify-icon>
                                                Choose PNG
                                            </label>
                                        </div>
                                        <div class="mt-2 text-xs text-slate-500">
                                            Max: 2MB | PNG format
                                        </div>
                                        @error('favicon_png_192')
                                            <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- SVG Favicon -->
                                <div class="bg-slate-50 dark:bg-slate-900 rounded-lg p-6 border border-slate-200 dark:border-slate-700">
                                    <div class="text-center">
                                        <h6 class="text-md font-medium text-slate-900 dark:text-white mb-4">SVG Format</h6>
                                        <div class="mb-4">
                                            <div class="w-16 h-16 mx-auto bg-white border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center">
                                                @if($settings->favicon_svg)
                                                    <img id="faviconSvgPreview" 
                                                         src="{{ $settings->favicon_svg_url }}" 
                                                         alt="Favicon SVG" 
                                                         class="w-10 h-10 object-contain">
                                                @else
                                                    <div id="faviconSvgPreview" class="text-slate-400">
                                                        <iconify-icon class="text-2xl" icon="heroicons-outline:code"></iconify-icon>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input type="file" 
                                                   name="favicon_svg" 
                                                   id="favicon_svg"
                                                   class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                   accept="image/svg+xml"
                                                   onchange="previewFavicon(event, 'faviconSvgPreview')">
                                            <label for="favicon_svg" class="btn btn-outline-primary btn-sm cursor-pointer">
                                                <iconify-icon class="mr-1" icon="heroicons-outline:upload"></iconify-icon>
                                                Choose SVG
                                            </label>
                                        </div>
                                        <div class="mt-2 text-xs text-slate-500">
                                            Max: 1MB | SVG format
                                        </div>
                                        @error('favicon_svg')
                                            <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                            </div>

                            <!-- Action Buttons -->
                            <div class="flex justify-between items-center pt-6 border-t border-slate-200 dark:border-slate-700">
                                <a href="{{ route('admin.website-settings.index') }}" 
                                   class="btn btn-outline-secondary">
                                    <iconify-icon class="mr-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                                    Back to Settings
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <iconify-icon class="mr-2" icon="heroicons-outline:save"></iconify-icon>
                                    Update Favicons
                                </button>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function previewFavicon(event, previewId) {
            const file = event.target.files[0];
            const preview = document.getElementById(previewId);
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const sizeClass = previewId.includes('16') ? 'w-4 h-4' : 
                                     previewId.includes('32') ? 'w-8 h-8' : 
                                     previewId.includes('192') ? 'w-12 h-12' : 'w-10 h-10';
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="${sizeClass} object-contain">`;
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
    @endpush
</x-app-layout>
