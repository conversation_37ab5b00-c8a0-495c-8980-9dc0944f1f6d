<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="$pageTitle" :breadcrumb-items="$breadcrumbItems" />
        </div>

        <!-- Alert Messages -->
        @if (session('message') && session('type'))
            <x-alert :message="session('message')" :type="session('type')"/>
        @endif

        <!-- Website Settings Dashboard -->
        <div class="grid grid-cols-12 gap-5">
            <div class="col-span-12">
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">{{ __('Website Settings Management') }}</h4>
                        <div class="text-sm text-slate-500">Manage your website's appearance and configuration</div>
                    </header>
                    <div class="card-body p-6">
                        <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-6">
                            
                            <!-- Logo Management Card -->
                            <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center mr-4">
                                        <iconify-icon class="text-2xl text-blue-600" icon="heroicons-outline:photograph"></iconify-icon>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Logo Management</h3>
                                        <p class="text-sm text-slate-600 dark:text-slate-400">Upload and manage site logos</p>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">Header Logo:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ $settings->header_logo ? '✓ Uploaded' : '✗ Not set' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">Footer Logo:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ $settings->footer_logo ? '✓ Uploaded' : '✗ Not set' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">Dark Theme:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ ($settings->header_logo_dark || $settings->footer_logo_dark) ? '✓ Configured' : '✗ Not set' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-6">
                                    <a href="{{ route('admin.website-settings.logo-management') }}" 
                                       class="btn btn-primary w-full">
                                        <iconify-icon class="mr-2" icon="heroicons-outline:cog"></iconify-icon>
                                        Manage Logos
                                    </a>
                                </div>
                            </div>

                            <!-- Favicon Management Card -->
                            <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 rounded-full bg-green-500 bg-opacity-20 flex items-center justify-center mr-4">
                                        <iconify-icon class="text-2xl text-green-600" icon="heroicons-outline:globe-alt"></iconify-icon>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Favicon Management</h3>
                                        <p class="text-sm text-slate-600 dark:text-slate-400">Configure browser icons</p>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">ICO Format:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ $settings->favicon_ico ? '✓ Uploaded' : '✗ Not set' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">PNG Variants:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ ($settings->favicon_png_16 || $settings->favicon_png_32 || $settings->favicon_png_192) ? '✓ Available' : '✗ Not set' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">SVG Format:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ $settings->favicon_svg ? '✓ Uploaded' : '✗ Not set' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-6">
                                    <a href="{{ route('admin.website-settings.favicon-management') }}" 
                                       class="btn btn-success w-full">
                                        <iconify-icon class="mr-2" icon="heroicons-outline:cog"></iconify-icon>
                                        Manage Favicons
                                    </a>
                                </div>
                            </div>

                            <!-- Email Settings Card -->
                            <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 rounded-full bg-purple-500 bg-opacity-20 flex items-center justify-center mr-4">
                                        <iconify-icon class="text-2xl text-purple-600" icon="heroicons-outline:mail"></iconify-icon>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Email Settings</h3>
                                        <p class="text-sm text-slate-600 dark:text-slate-400">Configure SMTP and email templates</p>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">SMTP Host:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ $settings->mail_host ? '✓ Configured' : '✗ Not set' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">From Address:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            {{ $settings->mail_from_address ? '✓ Set' : '✗ Not set' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600 dark:text-slate-400">Status:</span>
                                        <span class="text-slate-900 dark:text-white">
                                            @if($settings->mail_host && $settings->mail_from_address)
                                                <span class="text-green-600">✓ Ready</span>
                                            @else
                                                <span class="text-red-600">✗ Incomplete</span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-6">
                                    <a href="{{ route('admin.website-settings.email-settings') }}" 
                                       class="btn btn-purple w-full">
                                        <iconify-icon class="mr-2" icon="heroicons-outline:cog"></iconify-icon>
                                        Configure Email
                                    </a>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Settings Overview -->
        <div class="grid grid-cols-12 gap-5">
            <div class="col-span-12">
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">{{ __('Current Configuration Overview') }}</h4>
                        <div class="text-sm text-slate-500">Quick overview of your current website settings</div>
                    </header>
                    <div class="card-body p-6">
                        <div class="grid lg:grid-cols-2 grid-cols-1 gap-8">
                            
                            <!-- Logo Preview Section -->
                            <div>
                                <h5 class="text-lg font-semibold text-slate-900 dark:text-white mb-4">Logo Preview</h5>
                                <div class="space-y-4">
                                    @if($settings->header_logo)
                                        <div class="flex items-center space-x-4 p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                                            <img src="{{ $settings->header_logo_url }}" alt="Header Logo" class="h-12 w-auto">
                                            <div>
                                                <div class="text-sm font-medium text-slate-900 dark:text-white">Header Logo</div>
                                                <div class="text-xs text-slate-500">Light theme</div>
                                            </div>
                                        </div>
                                    @endif
                                    
                                    @if($settings->header_logo_dark)
                                        <div class="flex items-center space-x-4 p-4 bg-slate-800 rounded-lg">
                                            <img src="{{ $settings->header_logo_dark_url }}" alt="Header Logo Dark" class="h-12 w-auto">
                                            <div>
                                                <div class="text-sm font-medium text-white">Header Logo</div>
                                                <div class="text-xs text-slate-400">Dark theme</div>
                                            </div>
                                        </div>
                                    @endif
                                    
                                    @if(!$settings->header_logo && !$settings->header_logo_dark)
                                        <div class="text-center py-8 text-slate-500">
                                            <iconify-icon class="text-4xl mb-2" icon="heroicons-outline:photograph"></iconify-icon>
                                            <p>No logos uploaded yet</p>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Email Configuration Section -->
                            <div>
                                <h5 class="text-lg font-semibold text-slate-900 dark:text-white mb-4">Email Configuration</h5>
                                <div class="space-y-3">
                                    <div class="flex justify-between py-2 border-b border-slate-200 dark:border-slate-700">
                                        <span class="text-slate-600 dark:text-slate-400">Driver:</span>
                                        <span class="text-slate-900 dark:text-white font-medium">
                                            {{ $settings->mail_driver ?: 'Not configured' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between py-2 border-b border-slate-200 dark:border-slate-700">
                                        <span class="text-slate-600 dark:text-slate-400">Host:</span>
                                        <span class="text-slate-900 dark:text-white font-medium">
                                            {{ $settings->mail_host ?: 'Not configured' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between py-2 border-b border-slate-200 dark:border-slate-700">
                                        <span class="text-slate-600 dark:text-slate-400">Port:</span>
                                        <span class="text-slate-900 dark:text-white font-medium">
                                            {{ $settings->mail_port ?: 'Not configured' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between py-2 border-b border-slate-200 dark:border-slate-700">
                                        <span class="text-slate-600 dark:text-slate-400">From Address:</span>
                                        <span class="text-slate-900 dark:text-white font-medium">
                                            {{ $settings->mail_from_address ?: 'Not configured' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between py-2">
                                        <span class="text-slate-600 dark:text-slate-400">From Name:</span>
                                        <span class="text-slate-900 dark:text-white font-medium">
                                            {{ $settings->mail_from_name ?: 'Not configured' }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
