<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="'Package Purchase Management'" :breadcrumb-items="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Package Purchases', 'url' => route('admin.package-purchases.index'), 'active' => true]
            ]" />
        </div>

        <!-- Filters -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Search & Filter</h4>
            </header>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.package-purchases.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="input-area">
                        <label class="form-label">Search</label>
                        <div class="relative">
                            <input type="text" name="q" value="{{ $filters['q'] ?? '' }}"
                                   class="form-control !pl-9" placeholder="Search by user name, email, or package...">
                            <iconify-icon icon="heroicons-outline:search" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                        </div>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="">All Statuses</option>
                            @foreach($statusOptions as $status)
                                <option value="{{ $status }}" {{ ($filters['status'] ?? '') === $status ? 'selected' : '' }}>
                                    {{ ucfirst($status) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="input-area">
                        <label class="form-label">Package</label>
                        <select name="package" class="form-control">
                            <option value="">All Packages</option>
                            @foreach($packages as $package)
                                <option value="{{ $package->id }}" {{ ($filters['package'] ?? '') == $package->id ? 'selected' : '' }}>
                                    {{ $package->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:search" class="mr-2"></iconify-icon>
                            Search
                        </button>
                        <a href="{{ route('admin.package-purchases.index') }}" class="btn btn-outline-secondary">
                            <iconify-icon icon="heroicons-outline:x-mark" class="mr-2"></iconify-icon>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Purchases Table -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Package Purchases ({{ $purchases->total() }})</h4>
            </header>
            <div class="card-body px-6 pb-6">
                @if($purchases->count() > 0)
                    <div class="overflow-x-auto -mx-6">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                    <thead class="bg-slate-200 dark:bg-slate-700">
                                        <tr>
                                            <th scope="col" class="table-th">User</th>
                                            <th scope="col" class="table-th">Package</th>
                                            <th scope="col" class="table-th">Amount</th>
                                            <th scope="col" class="table-th">Status</th>
                                            <th scope="col" class="table-th">Purchase Date</th>
                                            <th scope="col" class="table-th">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @foreach($purchases as $purchase)
                                            <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                                                <td class="table-td">
                                                    <div>
                                                        <div class="text-slate-600 dark:text-slate-300 text-sm font-medium">
                                                            {{ $purchase->user->name }}
                                                        </div>
                                                        <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                            {{ $purchase->user->email }}
                                                        </div>
                                                        <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                            Role: {{ ucfirst($purchase->user->role) }}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <div>
                                                        <div class="text-slate-600 dark:text-slate-300 text-sm font-medium">
                                                            {{ $purchase->package->name }}
                                                        </div>
                                                        <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                            Target: {{ ucfirst($purchase->package->target_role) }}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <span class="text-slate-900 dark:text-white font-medium">
                                                        {{ $purchase->formatted_amount }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <span class="badge {{ $purchase->status_badge_class }}">
                                                        {{ ucfirst($purchase->status) }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    @if($purchase->purchased_at)
                                                        <div class="text-slate-600 dark:text-slate-300 text-sm">
                                                            {{ $purchase->purchased_at->format('M d, Y') }}
                                                        </div>
                                                        <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                            {{ $purchase->purchased_at->format('h:i A') }}
                                                        </div>
                                                    @else
                                                        <span class="text-slate-500 dark:text-slate-400 text-sm">N/A</span>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex space-x-3 rtl:space-x-reverse">
                                                        <a href="{{ route('admin.package-purchases.show', $purchase) }}"
                                                           class="action-btn" data-tippy-content="View Details">
                                                            <iconify-icon icon="heroicons:eye"></iconify-icon>
                                                        </a>

                                                        @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                                                            @if(in_array($purchase->status, ['failed', 'pending']))
                                                                <form method="POST" action="{{ route('admin.package-purchases.destroy', $purchase) }}"
                                                                      class="inline" onsubmit="return confirm('Are you sure you want to delete this purchase record?')">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="action-btn" data-tippy-content="Delete">
                                                                        <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                                    </button>
                                                                </form>
                                                            @endif
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $purchases->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <iconify-icon icon="heroicons-outline:shopping-cart" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
                        <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No package purchases found</h3>
                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                            @if(request()->hasAny(['q', 'status', 'package']))
                                Try adjusting your search criteria.
                            @else
                                Package purchases will appear here once users start purchasing packages.
                            @endif
                        </p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
