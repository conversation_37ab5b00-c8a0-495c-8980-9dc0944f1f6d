<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :page-title="'Package Purchase Details'" :breadcrumb-items="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Package Purchases', 'url' => route('admin.package-purchases.index'), 'active' => false],
                ['name' => 'Purchase Details', 'url' => route('admin.package-purchases.show', $purchase), 'active' => true]
            ]" />
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
            <!-- Purchase Information -->
            <div class="xl:col-span-2">
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">Purchase Information</h4>
                        <div>
                            <a href="{{ route('admin.package-purchases.index') }}" class="btn inline-flex justify-center btn-outline-secondary">
                                <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                                Back to Purchases
                            </a>
                        </div>
                    </header>
                    <div class="card-body p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Purchase ID</label>
                                    <div class="text-slate-900 dark:text-white font-medium">#{{ $purchase->id }}</div>
                                </div>
                                
                                <div>
                                    <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Package</label>
                                    <div class="text-slate-900 dark:text-white font-medium">{{ $purchase->package->name }}</div>
                                    @if($purchase->package->description)
                                        <div class="text-slate-500 dark:text-slate-400 text-sm mt-1">{{ $purchase->package->description }}</div>
                                    @endif
                                </div>

                                <div>
                                    <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Amount</label>
                                    <div class="text-slate-900 dark:text-white font-medium text-lg">{{ $purchase->formatted_amount }}</div>
                                </div>

                                <div>
                                    <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Status</label>
                                    <div>
                                        <span class="badge {{ $purchase->status_badge_class }}">
                                            {{ ucfirst($purchase->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Purchase Date</label>
                                    <div class="text-slate-900 dark:text-white font-medium">
                                        @if($purchase->purchased_at)
                                            {{ $purchase->purchased_at->format('M d, Y \a\t h:i A') }}
                                        @else
                                            <span class="text-slate-500 dark:text-slate-400">Not completed</span>
                                        @endif
                                    </div>
                                </div>

                                <div>
                                    <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Created Date</label>
                                    <div class="text-slate-900 dark:text-white font-medium">
                                        {{ $purchase->created_at->format('M d, Y \a\t h:i A') }}
                                    </div>
                                </div>

                                @if($purchase->stripe_payment_intent_id)
                                    <div>
                                        <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Stripe Payment Intent</label>
                                        <div class="text-slate-900 dark:text-white font-mono text-sm">{{ $purchase->stripe_payment_intent_id }}</div>
                                    </div>
                                @endif

                                @if($purchase->metadata)
                                    <div>
                                        <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Metadata</label>
                                        <div class="text-slate-900 dark:text-white text-sm">
                                            <pre class="bg-slate-100 dark:bg-slate-700 p-2 rounded text-xs">{{ json_encode($purchase->metadata, JSON_PRETTY_PRINT) }}</pre>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Features -->
                @if($purchase->package->features && count($purchase->package->features) > 0)
                    <div class="card mt-6">
                        <header class="card-header">
                            <h4 class="card-title">Package Features</h4>
                        </header>
                        <div class="card-body p-6">
                            <ul class="space-y-2">
                                @foreach($purchase->package->features as $feature)
                                    <li class="flex items-start space-x-3">
                                        <iconify-icon icon="heroicons:check-circle" class="text-success-500 mt-0.5 flex-shrink-0"></iconify-icon>
                                        <span class="text-slate-600 dark:text-slate-300">{{ $feature }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </div>

            <!-- User Information -->
            <div>
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">Customer Information</h4>
                    </header>
                    <div class="card-body p-6">
                        <div class="space-y-4">
                            <div class="text-center">
                                @if($purchase->user->photo)
                                    <img src="{{ $purchase->user->photo }}" alt="{{ $purchase->user->name }}" 
                                         class="w-16 h-16 rounded-full mx-auto mb-3">
                                @else
                                    <div class="w-16 h-16 rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center mx-auto mb-3">
                                        <iconify-icon icon="heroicons:user" class="text-2xl text-slate-500"></iconify-icon>
                                    </div>
                                @endif
                                <h5 class="text-slate-900 dark:text-white font-medium">{{ $purchase->user->name }}</h5>
                                <p class="text-slate-500 dark:text-slate-400 text-sm">{{ $purchase->user->email }}</p>
                            </div>

                            <div class="border-t border-slate-200 dark:border-slate-700 pt-4">
                                <div class="space-y-3">
                                    <div>
                                        <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Role</label>
                                        <div class="text-slate-900 dark:text-white">{{ ucfirst($purchase->user->role) }}</div>
                                    </div>

                                    @if($purchase->user->phone)
                                        <div>
                                            <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Phone</label>
                                            <div class="text-slate-900 dark:text-white">{{ $purchase->user->phone }}</div>
                                        </div>
                                    @endif

                                    @if($purchase->user->city || $purchase->user->country)
                                        <div>
                                            <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Location</label>
                                            <div class="text-slate-900 dark:text-white">
                                                {{ collect([$purchase->user->city, $purchase->user->country])->filter()->implode(', ') }}
                                            </div>
                                        </div>
                                    @endif

                                    <div>
                                        <label class="text-sm font-medium text-slate-600 dark:text-slate-300">Member Since</label>
                                        <div class="text-slate-900 dark:text-white">{{ $purchase->user->created_at->format('M d, Y') }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="border-t border-slate-200 dark:border-slate-700 pt-4">
                                <a href="{{ route('admin.subscription-users.show', $purchase->user) }}" 
                                   class="btn btn-outline-primary w-full">
                                    <iconify-icon icon="heroicons:user" class="mr-2"></iconify-icon>
                                    View User Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Management -->
                @if(in_array(auth()->user()->role, ['admin', 'super-admin']))
                    <div class="card mt-6">
                        <header class="card-header">
                            <h4 class="card-title">Status Management</h4>
                        </header>
                        <div class="card-body p-6">
                            <form method="POST" action="{{ route('admin.package-purchases.update', $purchase) }}">
                                @csrf
                                @method('PUT')
                                <div class="space-y-4">
                                    <div class="input-area">
                                        <label for="status" class="form-label">Update Status</label>
                                        <select id="status" name="status" class="form-control">
                                            <option value="pending" {{ $purchase->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                            <option value="completed" {{ $purchase->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="failed" {{ $purchase->status === 'failed' ? 'selected' : '' }}>Failed</option>
                                            <option value="refunded" {{ $purchase->status === 'refunded' ? 'selected' : '' }}>Refunded</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-full">
                                        <iconify-icon icon="heroicons:check" class="mr-2"></iconify-icon>
                                        Update Status
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
