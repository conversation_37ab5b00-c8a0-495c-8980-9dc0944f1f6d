<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :pageTitle="'Edit ' . ucwords(str_replace('-', ' ', $legalPage->type))" :breadcrumbItems="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Legal Pages', 'url' => route('admin.legal-pages.index'), 'active' => false],
                ['name' => 'Edit ' . ucwords(str_replace('-', ' ', $legalPage->type)), 'url' => route('admin.legal-pages.edit', $legalPage), 'active' => true]
            ]" />
        </div>

        <!-- Edit Form -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Edit {{ ucwords(str_replace('-', ' ', $legalPage->type)) }}</h4>
                <div>
                    <a href="{{ route('admin.legal-pages.index') }}" class="btn inline-flex justify-center btn-outline-secondary">
                        <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2" icon="heroicons-outline:arrow-left"></iconify-icon>
                        Back to Legal Pages
                    </a>
                </div>
            </header>
            <div class="card-body p-6">
                <form method="POST" action="{{ route('admin.legal-pages.update', $legalPage) }}" id="legalPageForm">
                    @csrf
                    @method('PUT')
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="input-area">
                                <label for="type" class="form-label">Page Type</label>
                                <div class="relative">
                                    <input type="text" id="type" value="{{ ucwords(str_replace('-', ' ', $legalPage->type)) }}"
                                           class="form-control !pl-9 bg-slate-100 dark:bg-slate-700" readonly>
                                    <iconify-icon icon="heroicons-outline:lock-closed" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                </div>
                                <div class="text-slate-500 text-sm mt-1">
                                    Page type cannot be changed after creation.
                                </div>
                            </div>

                            <div class="input-area">
                                <label for="title" class="form-label">Page Title <span class="text-danger-500">*</span></label>
                                <div class="relative">
                                    <input type="text" id="title" name="title" value="{{ old('title', $legalPage->title) }}"
                                           class="form-control !pl-9 @error('title') !border-danger-500 @enderror"
                                           placeholder="Enter page title" required>
                                    <iconify-icon icon="heroicons-outline:document-text" class="absolute left-2 top-1/2 -translate-y-1/2 text-base text-slate-500"></iconify-icon>
                                </div>
                                @error('title')
                                    <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="input-area">
                            <label for="content" class="form-label">Content <span class="text-danger-500">*</span></label>
                            <div id="content-editor" class="quill-editor" style="min-height: 400px;"></div>
                            <textarea id="content" name="content" style="display: none;" required>{{ old('content', $legalPage->content) }}</textarea>
                            @error('content')
                                <div class="text-danger-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                            <div class="text-slate-500 text-sm mt-1">
                                Use the rich text editor to format your content with headings, lists, links, and more.
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="checkbox-area">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="is_active" value="1"
                                       class="form-checkbox" {{ old('is_active', $legalPage->is_active) ? 'checked' : '' }}>
                                <span class="text-slate-500 dark:text-slate-400 text-sm leading-6 ltr:ml-2 rtl:mr-2">
                                    Page is active and visible to users
                                </span>
                            </label>
                        </div>

                        <!-- Last Updated Info -->
                        @if($legalPage->last_updated_at)
                            <div class="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                                <div class="flex items-center text-sm text-slate-600 dark:text-slate-300">
                                    <iconify-icon icon="heroicons-outline:clock" class="mr-2"></iconify-icon>
                                    <span>Last updated: {{ $legalPage->last_updated_at->format('M d, Y \a\t h:i A') }}</span>
                                    @if($legalPage->updatedBy)
                                        <span class="ml-2">by {{ $legalPage->updatedBy->name }}</span>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 mt-8">
                        <a href="{{ route('admin.legal-pages.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <iconify-icon icon="heroicons-outline:check" class="mr-2"></iconify-icon>
                            Update Legal Page
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @push('styles')
        <!-- Quill.js CSS -->
        <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
        <style>
            /* Custom styles for Quill.js integration with DashCode Tailwind CSS */
            .quill-editor {
                background-color: white;
                border: 1px solid #d1d5db;
                border-radius: 0.375rem;
                overflow: hidden;
            }

            .dark .quill-editor {
                background-color: #1e293b;
                border-color: #475569;
            }

            .ql-toolbar {
                border-bottom: 1px solid #e5e7eb;
                padding: 8px;
                font-family: inherit;
                font-size: 14px;
                line-height: 1.5;
            }

            .dark .ql-toolbar {
                border-bottom-color: #475569;
                background-color: #334155;
            }

            .ql-editor {
                padding: 12px;
                min-height: inherit;
                color: #374151;
            }

            .dark .ql-editor {
                color: #e2e8f0;
            }

            .ql-editor.ql-blank::before {
                color: #9ca3af;
                font-style: normal;
                left: 12px;
            }

            .dark .ql-editor.ql-blank::before {
                color: #64748b;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                .ql-toolbar {
                    padding: 6px;
                    font-size: 12px;
                }

                .ql-toolbar .ql-formats {
                    margin-right: 10px;
                }

                .ql-editor {
                    padding: 10px;
                }
            }

            /* Focus state */
            .quill-editor:focus-within {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
        </style>
    @endpush

    @push('scripts')
    <!-- Quill.js JavaScript -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing Quill.js editor for Legal Pages...');

            // Wait for Quill to be available
            if (typeof Quill === 'undefined') {
                console.error('Quill.js is not loaded');
                return;
            }

            // Initialize Content Editor with full toolbar
            const contentEditor = new Quill('#content-editor', {
                theme: 'snow',
                placeholder: 'Enter the legal page content here...',
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        ['link'],
                        [{ 'align': [] }],
                        [{ 'color': [] }, { 'background': [] }],
                        ['blockquote', 'code-block'],
                        ['clean']
                    ]
                }
            });

            // Set initial content if available
            const initialContent = document.getElementById('content').value;
            if (initialContent) {
                contentEditor.root.innerHTML = initialContent;
            }

            // Sync editor content with hidden textarea on change
            contentEditor.on('text-change', function() {
                document.getElementById('content').value = contentEditor.root.innerHTML;
            });

            // Sync content before form submission
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function() {
                    document.getElementById('content').value = contentEditor.root.innerHTML;
                });
            }

            console.log('Quill.js editor initialized successfully for Legal Pages!');
        });
    </script>
@endpush
</x-app-layout>
