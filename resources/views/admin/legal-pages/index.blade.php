<x-app-layout>
    <div class="space-y-8">
        <div>
            <x-breadcrumb :pageTitle="'Legal Pages Management'" :breadcrumbItems="[
                ['name' => 'Admin', 'url' => route('admin.dashboard'), 'active' => false],
                ['name' => 'Legal Pages', 'url' => route('admin.legal-pages.index'), 'active' => true]
            ]" />
        </div>

        <!-- Legal Pages Table -->
        <div class="card">
            <header class="card-header">
                <h4 class="card-title">Legal Pages ({{ $legalPages->count() }})</h4>
                <div class="flex space-x-2">
                    <a href="{{ route('admin.legal-pages.create') }}" class="btn btn-primary">
                        <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                        Create Legal Page
                    </a>
                </div>
            </header>
            <div class="card-body px-6 pb-6">
                @if($legalPages->count() > 0)
                    <div class="overflow-x-auto -mx-6">
                        <div class="inline-block min-w-full align-middle">
                            <div class="overflow-hidden">
                                <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700">
                                    <thead class="bg-slate-200 dark:bg-slate-700">
                                        <tr>
                                            <th scope="col" class="table-th">Type</th>
                                            <th scope="col" class="table-th">Title</th>
                                            <th scope="col" class="table-th">Status</th>
                                            <th scope="col" class="table-th">Last Updated</th>
                                            <th scope="col" class="table-th">Updated By</th>
                                            <th scope="col" class="table-th">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">
                                        @foreach($legalPages as $legalPage)
                                            <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                                                <td class="table-td">
                                                    <div class="flex items-center">
                                                        <iconify-icon icon="heroicons-outline:document-text" class="text-slate-500 mr-2"></iconify-icon>
                                                        <span class="text-slate-600 dark:text-slate-300 text-sm font-medium">
                                                            {{ ucwords(str_replace('-', ' ', $legalPage->type)) }}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <div class="text-slate-600 dark:text-slate-300 text-sm font-medium">
                                                        {{ $legalPage->title }}
                                                    </div>
                                                    <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                        {{ Str::limit(strip_tags($legalPage->content), 60) }}
                                                    </div>
                                                </td>
                                                <td class="table-td">
                                                    <span class="badge {{ $legalPage->is_active ? 'bg-success-500' : 'bg-danger-500' }} text-white">
                                                        {{ $legalPage->is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </td>
                                                <td class="table-td">
                                                    <div class="text-slate-600 dark:text-slate-300 text-sm">
                                                        {{ $legalPage->last_updated_at ? $legalPage->last_updated_at->format('M d, Y') : 'Never' }}
                                                    </div>
                                                    @if($legalPage->last_updated_at)
                                                        <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                            {{ $legalPage->last_updated_at->format('h:i A') }}
                                                        </div>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    @if($legalPage->updatedBy)
                                                        <div class="text-slate-600 dark:text-slate-300 text-sm">
                                                            {{ $legalPage->updatedBy->name }}
                                                        </div>
                                                        <div class="text-slate-500 dark:text-slate-400 text-xs">
                                                            {{ $legalPage->updatedBy->email }}
                                                        </div>
                                                    @else
                                                        <span class="text-slate-500 dark:text-slate-400 text-sm">N/A</span>
                                                    @endif
                                                </td>
                                                <td class="table-td">
                                                    <div class="flex space-x-3 rtl:space-x-reverse">
                                                        <a href="{{ route('admin.legal-pages.edit', $legalPage) }}" 
                                                           class="action-btn" data-tippy-content="Edit">
                                                            <iconify-icon icon="heroicons:pencil-square"></iconify-icon>
                                                        </a>
                                                        
                                                        <form method="POST" action="{{ route('admin.legal-pages.toggle-status', $legalPage) }}" class="inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <button type="submit" class="action-btn" 
                                                                    data-tippy-content="{{ $legalPage->is_active ? 'Deactivate' : 'Activate' }}">
                                                                <iconify-icon icon="{{ $legalPage->is_active ? 'heroicons:eye-slash' : 'heroicons:eye' }}"></iconify-icon>
                                                            </button>
                                                        </form>

                                                        <form method="POST" action="{{ route('admin.legal-pages.destroy', $legalPage) }}" 
                                                              class="inline" onsubmit="return confirm('Are you sure you want to delete this legal page?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="action-btn" data-tippy-content="Delete">
                                                                <iconify-icon icon="heroicons:trash"></iconify-icon>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-12">
                        <iconify-icon icon="heroicons-outline:document-text" class="mx-auto h-12 w-12 text-slate-400"></iconify-icon>
                        <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No legal pages found</h3>
                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                            Get started by creating your first legal page.
                        </p>
                        <div class="mt-6">
                            <a href="{{ route('admin.legal-pages.create') }}" class="btn btn-primary">
                                <iconify-icon icon="heroicons-outline:plus" class="mr-2"></iconify-icon>
                                Create Legal Page
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
