<x-app-layout>
    <!-- START:: Breadcrumbs -->
    <div class="flex justify-between flex-wrap items-center mb-6">
        <h4 class="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4 mb-4 sm:mb-0 flex space-x-3 rtl:space-x-reverse">{{ __('Reports Dashboard') }}</h4>
        <div class="flex sm:space-x-4 space-x-2 sm:justify-end items-center rtl:space-x-reverse">
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:calendar"></iconify-icon>
                    <span>This Month</span>
                </span>
            </button>
            <button class="btn leading-0 inline-flex justify-center bg-white text-slate-700 dark:bg-slate-800 dark:text-slate-300 !font-normal">
                <span class="flex items-center">
                    <iconify-icon class="text-xl ltr:mr-2 rtl:ml-2 font-light" icon="heroicons-outline:download"></iconify-icon>
                    <span>Export</span>
                </span>
            </button>
        </div>
    </div>
    <!-- END:: Breadcrumbs -->

    <!-- Overview Cards -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-primary-500" icon="heroicons-outline:users"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Total Users') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ number_format($data['totalUsers']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="2xl:col-span-3 lg:col-span-6 col-span-12">
            <div class="py-[18px] px-4 rounded-[6px] bg-[#FFEDE5] dark:bg-slate-900 card">
                <div class="flex items-center space-x-6 rtl:space-x-reverse">
                    <div class="flex-none">
                        <div class="w-12 h-12 rounded-full bg-success-500 bg-opacity-10 flex items-center justify-center">
                            <iconify-icon class="text-2xl text-success-500" icon="heroicons-outline:credit-card"></iconify-icon>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                            {{ __('Active Subscriptions') }}
                        </div>
                        <div class="text-slate-900 dark:text-white text-lg font-medium">
                            {{ number_format($data['activeSubscriptions']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>

    <!-- Quick Links -->
    <div class="grid grid-cols-12 gap-5 mb-5">
        <div class="col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Quick Reports') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div class="grid md:grid-cols-4 grid-cols-2 gap-4">
                        <a href="{{ route('admin.reports.subscriptions') }}" class="bg-slate-50 dark:bg-slate-900 rounded-lg p-4 hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                <div class="w-10 h-10 rounded-full bg-primary-500 bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-xl text-primary-500" icon="heroicons-outline:credit-card"></iconify-icon>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Subscription Reports') }}</div>
                                    <div class="text-xs text-slate-500 dark:text-slate-400">{{ __('Detailed subscription analytics') }}</div>
                                </div>
                            </div>
                        </a>



                        <a href="{{ route('admin.reports.users') }}" class="bg-slate-50 dark:bg-slate-900 rounded-lg p-4 hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                <div class="w-10 h-10 rounded-full bg-warning-500 bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-xl text-warning-500" icon="heroicons-outline:users"></iconify-icon>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('User Reports') }}</div>
                                    <div class="text-xs text-slate-500 dark:text-slate-400">{{ __('User growth and retention') }}</div>
                                </div>
                            </div>
                        </a>

                        <a href="{{ route('admin.reports.refund-requests') }}" class="bg-slate-50 dark:bg-slate-900 rounded-lg p-4 hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                <div class="w-10 h-10 rounded-full bg-info-500 bg-opacity-10 flex items-center justify-center">
                                    <iconify-icon class="text-xl text-info-500" icon="heroicons-outline:arrow-circle-left"></iconify-icon>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-900 dark:text-white">{{ __('Refund Requests') }}</div>
                                    <div class="text-xs text-slate-500 dark:text-slate-400">{{ __('Manage pending refund requests') }}</div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-12 gap-5">


        <div class="lg:col-span-4 col-span-12">
            <div class="card">
                <header class="card-header">
                    <h4 class="card-title">{{ __('Subscriptions by Product') }}</h4>
                </header>
                <div class="card-body p-6">
                    <div id="subscriptions-pie-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>


        // Subscriptions Pie Chart
        const subscriptionData = @json($data['subscriptionsByProduct']);
        const pieChart = new ApexCharts(document.querySelector("#subscriptions-pie-chart"), {
            series: subscriptionData.map(item => item.count),
            chart: {
                type: 'pie',
                height: 350
            },
            labels: subscriptionData.map(item => item.name),
            colors: ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
        });
        pieChart.render();
    </script>
    @endpush
</x-app-layout>
