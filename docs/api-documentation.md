# Investment Platform API Documentation

## Overview

This document provides comprehensive API documentation for the Laravel + React investment platform. The API follows RESTful principles and uses JSON for data exchange.

## Base URL
```
https://impactintels.test/api
```

## Authentication

All authenticated endpoints require Laravel Sanctum tokens. Include the token in the Authorization header:

```
Authorization: Bearer {your-token}
```

## Content Type

All requests should include the following headers:
```
Content-Type: application/json
Accept: application/json
```

## API Documentation Sections

### Core Platform APIs
- **[Package Management API](api/packages.md)** - One-time package purchases with Stripe integration
- **[Payment & Subscription API](payment-subscription-api.md)** - Recurring subscriptions and payment methods
- **[Investor Registration API](investor-api-documentation.md)** - Investor profile management
- **[Startup Registration API](startup-api-documentation.md)** - Startup profile management
- **[Refund Management API](api/refunds.md)** - Pro-rated refund processing

### Quick References
- **[Package API Quick Reference](api/packages-quick-reference.md)** - Essential package endpoints for React developers

---

## Authentication Endpoints

### Register User
**Endpoint:** `POST /api/register`

**Request Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "role": "startup"
}
```

**Response (201):**
```json
{
    "success": true,
    "message": "User registered successfully",
    "data": {
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "role": "startup"
        },
        "token": "1|abc123..."
    }
}
```

### Login
**Endpoint:** `POST /api/login`

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response (200):**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "role": "startup"
        },
        "token": "1|abc123..."
    }
}
```

### Get Current User
**Endpoint:** `GET /api/user`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "startup",
        "email_verified_at": "2025-07-15T10:00:00.000000Z"
    }
}
```

### Logout
**Endpoint:** `POST /api/logout`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

---

## Investment Platform Endpoints

### Categories
**Endpoint:** `GET /api/investment/categories`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Technology",
            "slug": "technology",
            "description": "Technology startups"
        }
    ]
}
```

### Dashboard Statistics

#### Investor Stats
**Endpoint:** `GET /api/investment/dashboard/investor-stats`
**Authentication:** Required (Investor role)

**Response (200):**
```json
{
    "success": true,
    "data": {
        "total_startups": 15,
        "matched_startups": 8,
        "pending_interests": 3,
        "approved_interests": 5
    }
}
```

#### Startup Stats
**Endpoint:** `GET /api/investment/dashboard/startup-stats`
**Authentication:** Required (Startup role)

**Response (200):**
```json
{
    "success": true,
    "data": {
        "total_investors": 12,
        "matched_investors": 6,
        "pending_interests": 2,
        "approved_interests": 4,
        "esg_score": 85.5,
        "profile_completion": 90
    }
}
```

---

## Startup Profile Management

### Create/Update Startup Profile
**Endpoint:** `POST /api/investment/startup-profiles`
**Authentication:** Required (Startup role)

**Request Body:**
```json
{
    "company_name": "TechCorp Inc",
    "description": "AI-powered solutions",
    "business_model": "B2B SaaS",
    "funding_stage": "Series A",
    "funding_amount_sought": 1000000,
    "categories": [1, 2, 3],
    "website": "https://techcorp.com",
    "linkedin": "https://linkedin.com/company/techcorp"
}
```

**Response (201):**
```json
{
    "success": true,
    "message": "Startup profile created successfully",
    "data": {
        "id": 1,
        "company_name": "TechCorp Inc",
        "description": "AI-powered solutions",
        "business_model": "B2B SaaS",
        "funding_stage": "Series A",
        "funding_amount_sought": 1000000,
        "esg_score": null,
        "esg_completed": false,
        "categories": [
            {"id": 1, "name": "Technology"},
            {"id": 2, "name": "AI/ML"}
        ]
    }
}
```

### Get Startup Profile
**Endpoint:** `GET /api/investment/startup-profiles`
**Authentication:** Required (Startup role)

**Response (200):**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "company_name": "TechCorp Inc",
        "description": "AI-powered solutions",
        "esg_score": 85.5,
        "esg_completed": true,
        "categories": [
            {"id": 1, "name": "Technology"}
        ]
    }
}
```

---

## Investor Profile Management

### Create/Update Investor Profile
**Endpoint:** `POST /api/investment/investor-profiles`
**Authentication:** Required (Investor role)

**Request Body:**
```json
{
    "firm_name": "Venture Capital LLC",
    "description": "Early stage investor",
    "investment_min": 50000,
    "investment_max": 2000000,
    "risk_tolerance": "medium",
    "focus_regions": [1, 2],
    "investment_focus": [3, 4],
    "website": "https://vc.com",
    "linkedin": "https://linkedin.com/company/vc"
}
```

**Response (201):**
```json
{
    "success": true,
    "message": "Investor profile created successfully",
    "data": {
        "id": 1,
        "firm_name": "Venture Capital LLC",
        "description": "Early stage investor",
        "investment_min": 50000,
        "investment_max": 2000000,
        "risk_tolerance": "medium"
    }
}
```

---

## ESG System

### Get ESG Questions
**Endpoint:** `GET /api/investment/esg/questions`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "question_text": "Does your company have environmental policies?",
            "category": "environmental",
            "type": "yes_no",
            "weight": 10,
            "is_required": true
        }
    ]
}
```

### Submit ESG Responses
**Endpoint:** `POST /api/investment/esg/responses`
**Authentication:** Required (Startup role)

**Request Body:**
```json
{
    "responses": [
        {
            "question_id": 1,
            "response_value": "yes"
        },
        {
            "question_id": 2,
            "response_value": "4"
        }
    ]
}
```

**Response (201):**
```json
{
    "success": true,
    "message": "ESG questionnaire submitted successfully",
    "data": {
        "esg_score": 85.5,
        "esg_breakdown": {
            "environmental": 88.0,
            "social": 82.5,
            "governance": 86.0
        }
    }
}
```

---

## Interest Request Management

### Create Interest Request
**Endpoint:** `POST /api/investment/interest-requests`
**Authentication:** Required

**Request Body:**
```json
{
    "target_user_id": 5,
    "request_type": "investment_interest",
    "message": "Interested in your startup"
}
```

**Response (201):**
```json
{
    "success": true,
    "message": "Interest request sent successfully",
    "data": {
        "id": 1,
        "requester_id": 1,
        "target_user_id": 5,
        "request_type": "investment_interest",
        "status": "pending",
        "message": "Interested in your startup"
    }
}
```

### List Interest Requests
**Endpoint:** `GET /api/investment/interest-requests`
**Authentication:** Required

**Query Parameters:**
- `status` (optional): pending, approved, rejected
- `type` (optional): sent, received

**Response (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "requester": {
                    "id": 1,
                    "name": "John Doe",
                    "email": "<EMAIL>"
                },
                "target": {
                    "id": 5,
                    "name": "Jane Smith",
                    "email": "<EMAIL>"
                },
                "request_type": "investment_interest",
                "status": "pending",
                "created_at": "2025-07-15T10:00:00.000000Z"
            }
        ],
        "current_page": 1,
        "total": 1
    }
}
```

---

## Discovery System

### Discover Startups (for Investors)
**Endpoint:** `GET /api/investment/discovery/startups`
**Authentication:** Required (Investor role)

**Query Parameters:**
- `categories[]` (optional): Array of category IDs
- `funding_stage` (optional): seed, series-a, series-b, etc.
- `min_funding` (optional): Minimum funding amount
- `max_funding` (optional): Maximum funding amount
- `esg_min_score` (optional): Minimum ESG score

**Response (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "user": {
                    "name": "TechCorp Inc",
                    "email": "<EMAIL>"
                },
                "company_name": "TechCorp Inc",
                "description": "AI-powered solutions",
                "funding_stage": "Series A",
                "funding_amount_sought": 1000000,
                "esg_score": 85.5,
                "categories": [
                    {"id": 1, "name": "Technology"}
                ]
            }
        ],
        "current_page": 1,
        "total": 1
    }
}
```

### Discover Investors (for Startups)
**Endpoint:** `GET /api/investment/discovery/investors`
**Authentication:** Required (Startup role)

**Query Parameters:**
- `focus_regions[]` (optional): Array of region IDs
- `investment_focus[]` (optional): Array of focus area IDs
- `min_investment` (optional): Minimum investment amount
- `max_investment` (optional): Maximum investment amount

**Response (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "user": {
                    "name": "Venture Capital LLC",
                    "email": "<EMAIL>"
                },
                "firm_name": "Venture Capital LLC",
                "description": "Early stage investor",
                "investment_min": 50000,
                "investment_max": 2000000,
                "risk_tolerance": "medium"
            }
        ],
        "current_page": 1,
        "total": 1
    }
}
```

---

## Error Handling

### Standard Error Response Format
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Validation error message"]
    }
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Authentication Errors
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

### Validation Errors
```json
{
    "success": false,
    "message": "The given data was invalid",
    "errors": {
        "email": ["The email field is required"],
        "password": ["The password must be at least 8 characters"]
    }
}
```

---

## Rate Limiting

Most endpoints are rate limited to prevent abuse:
- Authentication endpoints: 6 requests per minute
- General API endpoints: 60 requests per minute
- File upload endpoints: 10 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1642694400
```
