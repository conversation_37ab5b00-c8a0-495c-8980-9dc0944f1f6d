# Payment and Subscription API Documentation

## Overview

This document provides comprehensive documentation for payment methods, subscription management, and Stripe integration in the Laravel + React investment platform.

## Base URL
```
https://impactintels.test/api
```

## Authentication

All endpoints require Laravel Sanctum authentication:
```
Authorization: Bearer {your-token}
```

---

## Subscription Products (Package Products)

### Get Public Subscription Products
**Endpoint:** `GET /api/subscription-products/public`
**Authentication:** Not required

**Description:** Retrieve all available subscription plans for pricing pages.

**Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Startup Package",
            "description": "Perfect for early-stage startups",
            "price": 12.00,
            "currency": "USD",
            "billing_period": "monthly",
            "features": [
                "Access to investor network",
                "Profile creation",
                "Basic analytics"
            ],
            "is_active": true,
            "stripe_price_id": "price_1234567890"
        },
        {
            "id": 2,
            "name": "Investor Package",
            "description": "For active investors",
            "price": 25.00,
            "currency": "USD",
            "billing_period": "monthly",
            "features": [
                "Access to startup database",
                "Advanced filtering",
                "ESG analytics"
            ],
            "is_active": true,
            "stripe_price_id": "price_0987654321"
        }
    ]
}
```

### Get Subscription Products (Authenticated)
**Endpoint:** `GET /api/subscription-products`
**Authentication:** Required

**Description:** Get subscription products with user-specific information.

**Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Startup Package",
            "price": 12.00,
            "currency": "USD",
            "billing_period": "monthly",
            "is_current_plan": false,
            "can_subscribe": true,
            "stripe_price_id": "price_1234567890"
        }
    ]
}
```

---

## Subscription Management

### Get User Subscriptions
**Endpoint:** `GET /api/subscriptions`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "subscription_product_id": 1,
            "status": "active",
            "current_period_start": "2025-07-01T00:00:00.000000Z",
            "current_period_end": "2025-08-01T00:00:00.000000Z",
            "stripe_subscription_id": "sub_1234567890",
            "subscription_product": {
                "id": 1,
                "name": "Startup Package",
                "price": 12.00,
                "currency": "USD"
            }
        }
    ]
}
```

### Create Subscription
**Endpoint:** `POST /api/subscriptions`
**Authentication:** Required

**Request Body:**
```json
{
    "subscription_product_id": 1,
    "payment_method_id": 2
}
```

**Response (201) - With Existing Payment Method:**
```json
{
    "success": true,
    "message": "Subscription created successfully",
    "data": {
        "subscription": {
            "id": 1,
            "status": "active",
            "stripe_subscription_id": "sub_1234567890"
        },
        "invoice": {
            "id": 1,
            "amount": 12.00,
            "status": "paid"
        }
    }
}
```

**Response (200) - Checkout Session Required:**
```json
{
    "success": true,
    "message": "Checkout session created",
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_1234567890",
        "session_id": "cs_test_1234567890"
    }
}
```

### Create Direct Subscription (Recommended)
**Endpoint:** `POST /api/subscriptions/direct`
**Authentication:** Required

**Description:** Create a subscription directly using Stripe Checkout without requiring pre-saved payment methods. This is the recommended approach for new subscriptions as it provides a streamlined user experience.

**Request Body:**
```json
{
    "product_id": 1,
    "success_url": "http://localhost:5175/subscription/success?session_id={CHECKOUT_SESSION_ID}",
    "cancel_url": "http://localhost:5175/subscription/cancel"
}
```

**Parameters:**
- `product_id` (required): ID of the subscription product
- `success_url` (optional): URL to redirect after successful payment. Defaults to frontend success page
- `cancel_url` (optional): URL to redirect if payment is cancelled. Defaults to frontend cancel page

**Response (200):**
```json
{
    "success": true,
    "message": "Checkout session created successfully",
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_1234567890",
        "session_id": "cs_test_1234567890",
        "requires_redirect": true,
        "product": {
            "id": 1,
            "name": "Premium Plan",
            "price": "29.99",
            "billing_cycle": "monthly"
        }
    }
}
```

**Error Responses:**

**409 - User Already Has Active Subscription:**
```json
{
    "success": false,
    "message": "User already has an active subscription"
}
```

**404 - Product Not Found:**
```json
{
    "success": false,
    "message": "The selected subscription product is not available."
}
```

**Usage Flow:**
1. Call the API endpoint with the desired `product_id`
2. Redirect user to the returned `checkout_url`
3. User completes payment on Stripe's hosted checkout page
4. User is automatically redirected back to your `success_url`
5. Subscription is created automatically via Stripe webhook
6. User receives email confirmation and invoice

**Integration Example (JavaScript):**
```javascript
// Create direct subscription
const response = await fetch('/api/subscriptions/direct', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
        product_id: 1
    })
});

const data = await response.json();

if (data.success && data.data.checkout_url) {
    // Redirect to Stripe Checkout
    window.location.href = data.data.checkout_url;
}
```

### Update Subscription (Upgrade/Downgrade)
**Endpoint:** `PUT /api/subscriptions/{subscription_id}`
**Authentication:** Required

**Request Body:**
```json
{
    "subscription_product_id": 2
}
```

**Response (200):**
```json
{
    "success": true,
    "message": "Subscription updated successfully",
    "data": {
        "subscription": {
            "id": 1,
            "status": "active",
            "subscription_product_id": 2
        },
        "proration": {
            "refund_amount": 6.00,
            "charge_amount": 25.00,
            "net_amount": 19.00
        }
    }
}
```

### Cancel Subscription
**Endpoint:** `DELETE /api/subscriptions/{subscription_id}`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "message": "Subscription cancelled successfully",
    "data": {
        "subscription": {
            "id": 1,
            "status": "cancelled",
            "cancelled_at": "2025-07-15T10:00:00.000000Z",
            "ends_at": "2025-08-01T00:00:00.000000Z"
        }
    }
}
```

---

## Payment Method Management

### Get Payment Methods
**Endpoint:** `GET /api/payment-methods`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "stripe_payment_method_id": "pm_1234567890",
            "type": "card",
            "card_brand": "visa",
            "card_last_four": "4242",
            "card_exp_month": 12,
            "card_exp_year": 2025,
            "is_default": true,
            "created_at": "2025-07-15T10:00:00.000000Z"
        }
    ]
}
```

### Create Checkout Session for Adding Payment Method
**Endpoint:** `POST /api/payment-methods/checkout-session`
**Authentication:** Required

**Request Body:**
```json
{
    "success_url": "http://localhost:5175/app/payment-methods?success=true",
    "cancel_url": "http://localhost:5175/app/payment-methods?cancelled=true"
}
```

**Response (200):**
```json
{
    "success": true,
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_1234567890",
        "session_id": "cs_test_1234567890"
    }
}
```

### Process Checkout Success
**Endpoint:** `POST /api/payment-methods/process-checkout-success`
**Authentication:** Required

**Request Body:**
```json
{
    "session_id": "cs_test_1234567890"
}
```

**Response (200):**
```json
{
    "success": true,
    "message": "Payment method added successfully",
    "data": {
        "payment_method": {
            "id": 2,
            "stripe_payment_method_id": "pm_0987654321",
            "type": "card",
            "card_brand": "mastercard",
            "card_last_four": "5555",
            "is_default": false
        }
    }
}
```

### Set Default Payment Method
**Endpoint:** `POST /api/payment-methods/{payment_method_id}/set-default`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "message": "Default payment method updated successfully",
    "data": {
        "payment_method": {
            "id": 2,
            "is_default": true
        }
    }
}
```

### Delete Payment Method
**Endpoint:** `DELETE /api/payment-methods/{payment_method_id}`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "message": "Payment method deleted successfully"
}
```

---

## Payment History

### Get Payment History
**Endpoint:** `GET /api/payment-history`
**Authentication:** Required

**Query Parameters:**
- `status` (optional): succeeded, failed, pending
- `type` (optional): subscription, one_time
- `from_date` (optional): YYYY-MM-DD
- `to_date` (optional): YYYY-MM-DD
- `page` (optional): Page number
- `per_page` (optional): Items per page (default: 20)

**Response (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "amount": 12.00,
                "currency": "USD",
                "status": "succeeded",
                "type": "subscription",
                "description": "Startup Package subscription",
                "created_at": "2025-07-15T10:00:00.000000Z",
                "subscription_product": {
                    "name": "Startup Package"
                }
            }
        ],
        "current_page": 1,
        "total": 1,
        "per_page": 20
    }
}
```

### Get Payment Statistics
**Endpoint:** `GET /api/payment-history/statistics`
**Authentication:** Required

**Response (200):**
```json
{
    "success": true,
    "data": {
        "total_payments": 5,
        "total_amount": 60.00,
        "successful_payments": 4,
        "failed_payments": 1,
        "average_payment": 12.00,
        "monthly_spending": [
            {
                "month": "2025-07",
                "amount": 24.00,
                "count": 2
            }
        ]
    }
}
```

---

## Invoice Management

### Get Invoices
**Endpoint:** `GET /api/invoices`
**Authentication:** Required

**Query Parameters:**
- `status` (optional): paid, pending, failed
- `page` (optional): Page number

**Response (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "invoice_number": "INV-2025-001",
                "amount": 12.00,
                "currency": "USD",
                "status": "paid",
                "due_date": "2025-07-15T00:00:00.000000Z",
                "paid_at": "2025-07-15T10:00:00.000000Z",
                "subscription_product": {
                    "name": "Startup Package"
                }
            }
        ],
        "current_page": 1,
        "total": 1
    }
}
```

### Download Invoice
**Endpoint:** `GET /api/invoices/{invoice_id}/download`
**Authentication:** Required

**Response:** PDF file download

### Pay Invoice
**Endpoint:** `POST /api/invoices/{invoice_id}/pay`
**Authentication:** Required

**Request Body:**
```json
{
    "payment_method_id": 1
}
```

**Response (200):**
```json
{
    "success": true,
    "message": "Invoice paid successfully",
    "data": {
        "invoice": {
            "id": 1,
            "status": "paid",
            "paid_at": "2025-07-15T10:00:00.000000Z"
        },
        "payment": {
            "id": 5,
            "amount": 12.00,
            "status": "succeeded"
        }
    }
}
```

---

## Stripe Webhook Handling

### Webhook Endpoint
**Endpoint:** `POST /api/stripe/webhook`
**Authentication:** Stripe signature verification

**Supported Events:**
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`

---

## React Frontend Integration Examples

### 1. Fetching Subscription Products

```javascript
// React component for displaying subscription plans
import { useState, useEffect } from 'react';
import axios from 'axios';

const SubscriptionPlans = () => {
    const [plans, setPlans] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchPlans = async () => {
            try {
                const response = await axios.get('/api/subscription-products/public');
                setPlans(response.data.data);
            } catch (error) {
                console.error('Error fetching plans:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchPlans();
    }, []);

    if (loading) return <div>Loading...</div>;

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {plans.map(plan => (
                <div key={plan.id} className="border rounded-lg p-6">
                    <h3 className="text-xl font-bold">{plan.name}</h3>
                    <p className="text-gray-600">{plan.description}</p>
                    <div className="text-2xl font-bold mt-4">
                        ${plan.price}/{plan.billing_period}
                    </div>
                    <ul className="mt-4">
                        {plan.features.map((feature, index) => (
                            <li key={index} className="flex items-center">
                                <span className="text-green-500 mr-2">✓</span>
                                {feature}
                            </li>
                        ))}
                    </ul>
                    <button 
                        onClick={() => handleSubscribe(plan.id)}
                        className="w-full mt-6 bg-blue-600 text-white py-2 rounded"
                    >
                        Subscribe
                    </button>
                </div>
            ))}
        </div>
    );
};
```

### 2. Creating a Subscription

```javascript
// Subscription creation with error handling
const handleSubscribe = async (productId) => {
    try {
        const token = localStorage.getItem('auth_token');
        const response = await axios.post('/api/subscriptions', {
            subscription_product_id: productId
        }, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.data.data.checkout_url) {
            // Redirect to Stripe Checkout
            window.location.href = response.data.data.checkout_url;
        } else {
            // Subscription created successfully
            alert('Subscription created successfully!');
            // Refresh subscription data
            fetchUserSubscriptions();
        }
    } catch (error) {
        if (error.response?.status === 409) {
            alert('You already have an active subscription');
        } else {
            alert('Error creating subscription');
        }
    }
};
```

### 3. Payment Method Management

```javascript
// Payment methods component
const PaymentMethods = () => {
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchPaymentMethods = async () => {
        try {
            const token = localStorage.getItem('auth_token');
            const response = await axios.get('/api/payment-methods', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            setPaymentMethods(response.data.data);
        } catch (error) {
            console.error('Error fetching payment methods:', error);
        } finally {
            setLoading(false);
        }
    };

    const addPaymentMethod = async () => {
        try {
            const token = localStorage.getItem('auth_token');
            const response = await axios.post('/api/payment-methods/checkout-session', {
                success_url: `${window.location.origin}/app/payment-methods?success=true`,
                cancel_url: `${window.location.origin}/app/payment-methods?cancelled=true`
            }, {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            // Redirect to Stripe Checkout
            window.location.href = response.data.data.checkout_url;
        } catch (error) {
            alert('Error creating checkout session');
        }
    };

    const setDefaultPaymentMethod = async (paymentMethodId) => {
        try {
            const token = localStorage.getItem('auth_token');
            await axios.post(`/api/payment-methods/${paymentMethodId}/set-default`, {}, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            fetchPaymentMethods(); // Refresh list
        } catch (error) {
            alert('Error setting default payment method');
        }
    };

    useEffect(() => {
        fetchPaymentMethods();
    }, []);

    return (
        <div>
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">Payment Methods</h2>
                <button 
                    onClick={addPaymentMethod}
                    className="bg-blue-600 text-white px-4 py-2 rounded"
                >
                    Add Payment Method
                </button>
            </div>

            {loading ? (
                <div>Loading...</div>
            ) : (
                <div className="space-y-4">
                    {paymentMethods.map(method => (
                        <div key={method.id} className="border rounded-lg p-4 flex justify-between items-center">
                            <div>
                                <div className="font-medium">
                                    {method.card_brand.toUpperCase()} •••• {method.card_last_four}
                                </div>
                                <div className="text-sm text-gray-600">
                                    Expires {method.card_exp_month}/{method.card_exp_year}
                                </div>
                                {method.is_default && (
                                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                        Default
                                    </span>
                                )}
                            </div>
                            <div className="space-x-2">
                                {!method.is_default && (
                                    <button 
                                        onClick={() => setDefaultPaymentMethod(method.id)}
                                        className="text-blue-600 hover:underline"
                                    >
                                        Set Default
                                    </button>
                                )}
                                <button 
                                    onClick={() => deletePaymentMethod(method.id)}
                                    className="text-red-600 hover:underline"
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};
```

### 4. Handling Stripe Checkout Success

```javascript
// Handle successful payment method addition
useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    const success = urlParams.get('success');

    if (success && sessionId) {
        // Process the successful checkout
        const processCheckoutSuccess = async () => {
            try {
                const token = localStorage.getItem('auth_token');
                await axios.post('/api/payment-methods/process-checkout-success', {
                    session_id: sessionId
                }, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                // Clean up URL and refresh payment methods
                window.history.replaceState({}, document.title, window.location.pathname);
                fetchPaymentMethods();
                alert('Payment method added successfully!');
            } catch (error) {
                alert('Error processing payment method');
            }
        };

        processCheckoutSuccess();
    }
}, []);
```

---

## Error Handling

### Common Error Responses

**Insufficient Funds:**
```json
{
    "success": false,
    "message": "Your card was declined",
    "error_code": "card_declined"
}
```

**Invalid Payment Method:**
```json
{
    "success": false,
    "message": "The payment method is invalid or expired",
    "error_code": "payment_method_invalid"
}
```

**Subscription Conflict:**
```json
{
    "success": false,
    "message": "User already has an active subscription",
    "error_code": "subscription_exists"
}
```

---

## Testing with Stripe Test Cards

Use these test card numbers in Stripe's test environment:

- **Successful payment:** 4242 4242 4242 4242
- **Declined payment:** 4000 0000 0000 0002
- **Insufficient funds:** 4000 0000 0000 9995
- **3D Secure required:** 4000 0025 0000 3155

**Test Details:**
- Any future expiry date (e.g., 12/25)
- Any 3-digit CVC
- Any billing postal code
