# Subscription System User Guide

## Overview

The Subscription System provides comprehensive billing automation with Stripe integration, supporting multiple subscription plans, payment processing, refunds, and account management.

## Features

### For Users
- **Subscription Management**: Subscribe, upgrade, downgrade, and cancel subscriptions
- **Payment Processing**: Secure payment handling with 3D Secure support
- **Invoice Management**: View payment history and download invoices
- **Refund Requests**: Request refunds for eligible payments
- **Account Status**: Real-time account status based on payment status

### For Administrators
- **User Management**: Manage user accounts and subscription status
- **Financial Overview**: Comprehensive revenue analytics and reporting
- **Refund Processing**: Process full, partial, and pro-rated refunds
- **Account Control**: Lock/unlock accounts based on payment status
- **Product Management**: Manage subscription products and pricing

## Getting Started

### User Registration and Authentication

1. **Register**: Create an account with email and password
2. **Login**: Authenticate using Laravel Sanctum tokens
3. **Profile**: Manage profile information and payment methods

### Subscription Management

#### Subscribing to a Plan

There are two ways to subscribe to a plan:

**Method 1: Direct Subscription (Recommended)**
1. Browse available subscription products
2. Select desired plan and billing frequency
3. Click "Subscribe Now" to redirect to Stripe Checkout
4. Add payment method and complete payment in one step
5. Automatic redirect back to the application
6. Receive confirmation and invoice

**Method 2: Traditional Subscription (With Saved Payment Methods)**
1. Browse available subscription products
2. Add payment method to your account first
3. Select desired plan and billing frequency
4. Complete subscription using saved payment method
5. Complete 3D Secure authentication if required
6. Receive confirmation and invoice

#### Managing Active Subscriptions

- **View Details**: Check current plan, billing cycle, and next payment
- **Upgrade/Downgrade**: Change to different plan with pro-rated billing
- **Cancel**: Cancel subscription with optional pro-rated refund
- **Payment Methods**: Add, update, or remove payment methods

### Payment and Billing

#### Payment Processing

- **Automatic Billing**: Payments processed automatically on billing cycle
- **3D Secure**: Enhanced security for European payments
- **Retry Logic**: Automatic retry for failed payments with exponential backoff
- **Payment Methods**: Support for multiple credit/debit cards

#### Invoice Management

- **Invoice Generation**: Automatic invoice creation for all payments
- **Payment History**: View complete payment and invoice history
- **Download Invoices**: PDF download for accounting purposes
- **Payment Status**: Real-time status updates

### Refund System

#### Requesting Refunds

Users can request refunds for:
- Duplicate charges
- Service issues
- Subscription cancellations
- Billing errors

#### Refund Types

1. **Full Refund**: Complete refund of payment amount
2. **Partial Refund**: Refund of specific amount
3. **Pro-rated Refund**: Calculated refund based on unused service time

#### Pro-rated Refund Calculation

For subscription cancellations:
- **Service Period**: Current billing cycle dates
- **Usage Calculation**: Days used vs. total days in period
- **Refund Amount**: (Unused days / Total days) × Payment amount
- **Processing**: Automatic calculation and Stripe processing

### Account Status Management

#### Account States

- **Active**: Normal account with current subscription
- **Locked**: Account locked due to payment failures
- **Suspended**: Account suspended by administrator
- **Inactive**: Account deactivated

#### Automatic Account Locking

Accounts are automatically locked when:
- Payment fails after maximum retry attempts
- Invoice becomes overdue beyond grace period
- Fraudulent activity detected

#### Account Recovery

- **Payment Update**: Update payment method to resolve issues
- **Manual Unlock**: Contact support for manual account unlock
- **Automatic Unlock**: Account unlocks after successful payment

## Admin Dashboard

### User Management

#### User Overview
- View all users with subscription status
- Filter by account status, subscription type, payment status
- Search by name, email, or user ID

#### User Actions
- **View Details**: Complete user profile and subscription history
- **Lock/Unlock**: Manual account status management
- **Subscription Management**: Modify user subscriptions
- **Payment History**: View complete payment and refund history

### Financial Management

#### Revenue Analytics
- **Monthly Revenue**: Track recurring revenue trends
- **Payment Status**: Monitor successful vs. failed payments
- **Refund Analytics**: Track refund rates and amounts
- **Subscription Metrics**: Active subscriptions and churn rates

#### Payment Processing
- **Failed Payments**: Monitor and retry failed payments
- **Overdue Invoices**: Track and manage overdue accounts
- **Payment Methods**: View and manage user payment methods

### Refund Management

#### Processing Refunds
1. **Review Request**: Evaluate refund request details
2. **Calculate Amount**: Use pro-rated calculator for cancellations
3. **Process Refund**: Execute refund through Stripe
4. **Update Records**: Maintain audit trail and user notifications

#### Refund Analytics
- **Refund Statistics**: Track refund rates and amounts
- **Reason Analysis**: Monitor refund reasons for product improvement
- **Processing Time**: Track refund processing efficiency

### Product Management

#### Subscription Products
- **Create Products**: Define new subscription plans
- **Pricing Management**: Set prices and billing frequencies
- **Feature Configuration**: Define plan features and limits
- **Product Analytics**: Track product performance and adoption

## API Integration

### Authentication
All API endpoints require authentication using Laravel Sanctum tokens.

### Direct Subscription API

#### Create Direct Subscription
**Endpoint:** `POST /api/subscriptions/direct`
**Authentication:** Required (Laravel Sanctum)

**Description:** Create a subscription directly using Stripe Checkout without requiring pre-saved payment methods.

**Request Body:**
```json
{
    "product_id": 1,
    "success_url": "http://localhost:5175/subscription/success?session_id={CHECKOUT_SESSION_ID}",
    "cancel_url": "http://localhost:5175/subscription/cancel"
}
```

**Response (Success):**
```json
{
    "success": true,
    "message": "Checkout session created successfully",
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_...",
        "session_id": "cs_test_...",
        "requires_redirect": true,
        "product": {
            "id": 1,
            "name": "Premium Plan",
            "price": "29.99",
            "billing_cycle": "monthly"
        }
    }
}
```

**Usage Flow:**
1. Call the API endpoint with the desired product_id
2. Redirect user to the returned checkout_url
3. User completes payment on Stripe's hosted page
4. User is redirected back to your success_url
5. Subscription is automatically created via webhook

### Rate Limiting
API endpoints are rate-limited to prevent abuse:
- User endpoints: 60 requests per minute
- Admin endpoints: 120 requests per minute

### Webhooks
Stripe webhooks handle real-time payment events:
- Payment success/failure
- Subscription updates
- Refund processing
- 3D Secure authentication

## Security Features

### Payment Security
- **PCI Compliance**: Stripe handles all sensitive payment data
- **3D Secure**: Enhanced authentication for European payments
- **Encryption**: All data encrypted in transit and at rest
- **Tokenization**: Payment methods stored as secure tokens

### Access Control
- **Role-based Permissions**: Admin vs. user access levels
- **API Authentication**: Secure token-based authentication
- **Account Locking**: Automatic protection against suspicious activity

## Troubleshooting

### Common Issues

#### Payment Failures
- **Insufficient Funds**: User needs to update payment method
- **Expired Card**: Automatic email notification for card updates
- **3D Secure**: User needs to complete authentication

#### Account Locked
- **Payment Issues**: Resolve payment method problems
- **Contact Support**: Manual unlock may be required
- **Automatic Unlock**: Successful payment unlocks account

#### Refund Issues
- **Processing Time**: Refunds typically process within 5-10 business days
- **Partial Refunds**: May require admin approval
- **Stripe Limits**: Some refunds may be limited by Stripe policies

### Support Contacts

For technical issues or account problems:
- **Email Support**: <EMAIL>
- **Admin Dashboard**: Internal ticket system
- **Documentation**: Complete API and user documentation

## Best Practices

### For Users
- Keep payment methods current
- Monitor account status regularly
- Contact support for billing questions
- Review invoices and payment history

### For Administrators
- Monitor failed payments daily
- Process refunds promptly
- Review account locking policies
- Maintain audit trails for all actions

## System Requirements

### Technical Requirements
- Laravel 11.x
- PHP 8.2+
- MySQL 8.0+
- Stripe Account
- SSL Certificate

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
