# Direct Subscription Implementation - Usage Examples

## Overview

The direct subscription feature allows users to subscribe to products without requiring them to add payment methods first. Users are redirected to Stripe Checkout where they can add their payment method and complete the subscription in one seamless flow.

## API Endpoint

**Endpoint:** `POST /api/subscriptions/direct`
**Authentication:** Required (Laravel Sanctum)

## React Frontend Integration

### 1. Using the Subscription Service

```javascript
import { subscriptionService } from '../services/subscriptionService';

// Create direct subscription
const handleDirectSubscription = async (productId) => {
    try {
        const result = await subscriptionService.createDirectSubscription(productId);
        
        if (result.success && result.checkout_url) {
            // Redirect to Stripe Checkout
            window.location.href = result.checkout_url;
        } else {
            console.error('Failed to create subscription:', result.error);
            // Handle error (show toast, etc.)
        }
    } catch (error) {
        console.error('Subscription error:', error);
    }
};
```

### 2. Complete Component Example

```jsx
import React, { useState } from 'react';
import { subscriptionService } from '../services/subscriptionService';

const SubscriptionCard = ({ product }) => {
    const [loading, setLoading] = useState(false);

    const handleSubscribe = async () => {
        setLoading(true);
        
        try {
            const result = await subscriptionService.createDirectSubscription(product.id);
            
            if (result.success && result.checkout_url) {
                // Redirect to Stripe Checkout
                window.location.href = result.checkout_url;
            } else {
                alert('Failed to create subscription: ' + result.error);
            }
        } catch (error) {
            console.error('Subscription error:', error);
            alert('An error occurred while creating subscription');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
            <p className="text-gray-600 mb-4">{product.description}</p>
            <div className="text-2xl font-bold mb-4">
                ${product.price}/{product.billing_cycle}
            </div>
            
            <button
                onClick={handleSubscribe}
                disabled={loading}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
                {loading ? 'Creating Subscription...' : 'Subscribe Now'}
            </button>
        </div>
    );
};

export default SubscriptionCard;
```

### 3. Success/Cancel Page Handling

```jsx
// SubscriptionSuccess.jsx
import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { subscriptionService } from '../services/subscriptionService';

const SubscriptionSuccess = () => {
    const [searchParams] = useSearchParams();
    const [loading, setLoading] = useState(true);
    const [subscription, setSubscription] = useState(null);
    
    useEffect(() => {
        const sessionId = searchParams.get('session_id');
        
        if (sessionId) {
            // Handle successful subscription
            subscriptionService.handleCheckoutSuccess(sessionId)
                .then(result => {
                    setSubscription(result.subscription);
                    setLoading(false);
                })
                .catch(error => {
                    console.error('Error handling success:', error);
                    setLoading(false);
                });
        } else {
            setLoading(false);
        }
    }, [searchParams]);

    if (loading) {
        return <div>Processing your subscription...</div>;
    }

    return (
        <div className="max-w-md mx-auto mt-8 p-6 bg-green-50 rounded-lg">
            <h2 className="text-2xl font-bold text-green-800 mb-4">
                Subscription Successful!
            </h2>
            <p className="text-green-700 mb-4">
                Thank you for subscribing. Your subscription is now active.
            </p>
            {subscription && (
                <div className="bg-white p-4 rounded border">
                    <h3 className="font-semibold">Subscription Details:</h3>
                    <p>Plan: {subscription.product?.name}</p>
                    <p>Status: {subscription.status}</p>
                    <p>Next billing: {subscription.current_period_end}</p>
                </div>
            )}
        </div>
    );
};

export default SubscriptionSuccess;
```

## Laravel Backend Usage

### 1. Controller Method

The `createDirectSubscription` method in `SubscriptionController` handles the direct subscription creation:

```php
public function createDirectSubscription(DirectSubscribeRequest $request): JsonResponse
{
    $user = Auth::user();
    $product = $request->getSubscriptionProduct();

    // Check if user already has an active subscription
    if ($user->hasActiveSubscription()) {
        return $this->responseWithError(
            'User already has an active subscription',
            Response::HTTP_CONFLICT
        );
    }

    // Create Stripe customer and checkout session
    $customer = $this->stripeService->createOrGetCustomer($user);
    $session = $this->stripeService->createSubscriptionCheckoutSession(
        $customer->id,
        $product->stripe_price_id,
        $request->getSuccessUrl(),
        $request->getCancelUrl(),
        [
            'user_id' => $user->id,
            'subscription_product_id' => $product->id,
            'created_via' => 'direct_subscription',
        ]
    );

    return $this->responseWithSuccess('Checkout session created successfully', [
        'checkout_url' => $session->url,
        'session_id' => $session->id,
        'requires_redirect' => true,
        'product' => [
            'id' => $product->id,
            'name' => $product->name,
            'price' => $product->price,
            'billing_cycle' => $product->billing_cycle,
        ],
    ]);
}
```

### 2. Request Validation

The `DirectSubscribeRequest` validates the incoming request:

```php
public function rules(): array
{
    return [
        'product_id' => [
            'required',
            'integer',
            Rule::exists('subscription_products', 'id')->where(function ($query) {
                $query->where('is_active', true);
            }),
        ],
        'success_url' => ['nullable', 'url', 'max:2048'],
        'cancel_url' => ['nullable', 'url', 'max:2048'],
    ];
}
```

## Testing with Stripe Sandbox

### Test Card Numbers

Use these test card numbers in Stripe Checkout:

- **Success:** 4242 4242 4242 4242
- **Decline:** 4000 0000 0000 0002
- **3D Secure:** 4000 0000 0000 3220

### Test Flow

1. Create a subscription product in your admin dashboard
2. Use the React frontend to initiate a direct subscription
3. Complete payment using test card 4242 4242 4242 4242
4. Verify the subscription is created in your database
5. Check that webhooks properly handle the subscription creation

## Benefits of Direct Subscription

1. **Simplified User Experience:** Users don't need to add payment methods separately
2. **Higher Conversion Rates:** Fewer steps in the subscription process
3. **Stripe Hosted Security:** Payment data is handled entirely by Stripe
4. **Automatic Webhook Handling:** Subscription creation is handled automatically
5. **Mobile Optimized:** Stripe Checkout works seamlessly on mobile devices

## Error Handling

The implementation includes comprehensive error handling for:

- Invalid or inactive products
- Users with existing active subscriptions
- Stripe API errors
- Network connectivity issues
- Invalid URLs

## Next Steps

1. Integrate the direct subscription button into your pricing pages
2. Set up proper success/cancel pages in your React app
3. Test the complete flow with Stripe test cards
4. Monitor webhook events in your Stripe dashboard
5. Implement analytics to track subscription conversion rates
