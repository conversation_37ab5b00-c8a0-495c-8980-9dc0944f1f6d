# Package Management API - Quick Reference

## Base URL
```
https://impactintels.test/api
```

## Authentication
```javascript
headers: {
    'Authorization': 'Bearer {token}',
    'Accept': 'application/json'
}
```

## Quick API Reference

### 📋 List Packages
```javascript
GET /api/packages?category=Analytics&target_role=investor&page=1&per_page=20
```

### 🔍 Get Package Details
```javascript
GET /api/packages/{id}
```

### 💳 Purchase Package
```javascript
POST /api/packages/{id}/purchase
{
    "success_url": "http://localhost:5175/app/packages/success?session_id={CHECKOUT_SESSION_ID}",
    "cancel_url": "http://localhost:5175/app/packages?cancelled=true"
}
```

### 📊 Purchase History
```javascript
GET /api/package-purchases?status=completed&page=1&per_page=20
```

## Quick Implementation

### 1. Service Listing
```javascript
const { data } = await axios.get('/api/services', {
    headers: { 'Authorization': `Bearer ${token}` }
});
const services = data.data.data; // Array of services
```

### 2. Purchase Flow
```javascript
// Step 1: Initiate purchase
const response = await axios.post(`/api/services/${serviceId}/purchase`, {
    success_url: `${window.location.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${window.location.origin}/services`
}, {
    headers: { 'Authorization': `Bearer ${token}` }
});

// Step 2: Redirect to Stripe
window.location.href = response.data.data.checkout_url;

// Step 3: Handle success (on success page)
const sessionId = new URLSearchParams(window.location.search).get('session_id');
// Verify purchase or show success message
```

### 3. Error Handling
```javascript
try {
    // API call
} catch (error) {
    if (error.response?.status === 401) {
        // Redirect to login
    } else if (error.response?.status === 403) {
        // Show "not available for your role" message
    } else if (error.response?.status === 409) {
        // Show "already purchased" message
    } else {
        // Show generic error
    }
}
```

## Common Query Parameters

### Services Listing
- `q` - Search term
- `target_role` - Filter by role (investor, startup, analyst, all)
- `min_price` / `max_price` - Price range
- `page` / `per_page` - Pagination
- `sort_by` - Sort field (name, price, created_at)
- `sort_direction` - Sort direction (asc, desc)

### Purchase History
- `status` - Filter by status (pending, completed, failed, cancelled)
- `service_id` - Filter by specific service
- `from_date` / `to_date` - Date range (YYYY-MM-DD)
- `min_amount` / `max_amount` - Amount range

## Response Structures

### Package Object
```javascript
{
    "id": 1,
    "name": "Package Name",
    "description": "Package description",

    "price": "299.99",
    "currency": "USD",
    "target_role": "investor",
    "features": ["Feature 1", "Feature 2"],
    "is_active": true,
    "created_at": "2025-07-15T10:00:00.000000Z"
}
```

### Purchase Object
```javascript
{
    "id": 123,
    "user_id": 456,
    "package_id": 1,
    "amount": "299.99",
    "currency": "USD",
    "status": "completed",
    "stripe_session_id": "cs_test_...",
    "purchased_at": "2025-07-15T10:30:00.000000Z",
    "package": {
        "id": 1,
        "name": "Package Name",

    }
}
```

### Pagination Structure
```javascript
{
    "data": [...], // Array of items
    "current_page": 1,
    "last_page": 3,
    "per_page": 20,
    "total": 50,
    "from": 1,
    "to": 20,
    "links": {
        "first": "...",
        "last": "...",
        "prev": null,
        "next": "..."
    }
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized (need to login)
- `403` - Forbidden (role restriction)
- `404` - Not Found
- `409` - Conflict (already purchased)
- `422` - Validation Error
- `500` - Server Error

## Test Cards (Stripe Test Mode)

- **Success:** `4242 4242 4242 4242`
- **Declined:** `4000 0000 0000 0002`
- **Insufficient Funds:** `4000 0000 0000 9995`
- **3D Secure:** `4000 0025 0000 3155`

**Test Details:**
- Expiry: Any future date (e.g., 12/25)
- CVC: Any 3 digits (e.g., 123)
- ZIP: Any valid postal code

## Environment Variables

```javascript
// React .env file
REACT_APP_API_BASE_URL=https://impactintels.test/api
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

## Useful Utilities

### API Client Setup
```javascript
import axios from 'axios';

const apiClient = axios.create({
    baseURL: process.env.REACT_APP_API_BASE_URL,
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
});

// Add auth token to requests
apiClient.interceptors.request.use(config => {
    const token = localStorage.getItem('auth_token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Handle auth errors
apiClient.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 401) {
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

export default apiClient;
```

### Service Purchase Hook
```javascript
import { useState } from 'react';
import apiClient from './apiClient';

export const useServicePurchase = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const purchaseService = async (serviceId, successUrl, cancelUrl) => {
        try {
            setLoading(true);
            setError(null);

            const response = await apiClient.post(`/services/${serviceId}/purchase`, {
                success_url: successUrl,
                cancel_url: cancelUrl
            });

            if (response.data.success) {
                window.location.href = response.data.data.checkout_url;
            }
        } catch (err) {
            setError(err.response?.data?.message || 'Purchase failed');
        } finally {
            setLoading(false);
        }
    };

    return { purchaseService, loading, error };
};
```

### Format Currency
```javascript
export const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(parseFloat(amount));
};

// Usage: formatCurrency('299.99') => "$299.99"
```

### Format Date
```javascript
export const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Usage: formatDate('2025-07-15T10:00:00.000000Z') => "Jul 15, 2025"
```

## Component Examples

### Service Card
```javascript
const ServiceCard = ({ service, onPurchase }) => (
    <div className="border rounded-lg p-6">
        <h3 className="text-lg font-semibold">{service.name}</h3>
        <p className="text-gray-600 text-sm">{service.description}</p>
        <div className="text-2xl font-bold">${service.price}</div>
        <button 
            onClick={() => onPurchase(service.id)}
            className="w-full mt-4 bg-blue-600 text-white py-2 rounded"
        >
            Purchase
        </button>
    </div>
);
```

### Loading Spinner
```javascript
const LoadingSpinner = () => (
    <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
);
```

### Error Message
```javascript
const ErrorMessage = ({ message, onRetry }) => (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="text-red-800">{message}</div>
        {onRetry && (
            <button 
                onClick={onRetry}
                className="mt-2 text-red-600 hover:underline"
            >
                Try Again
            </button>
        )}
    </div>
);
```

## Need Help?

- **Full Documentation:** `/docs/api/services.md`
- **Backend Team:** For API issues or new endpoints
- **DevOps Team:** For environment setup or deployment issues
- **Test Data:** Use admin panel at `/admin/services` to create test services
