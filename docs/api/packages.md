# Package Management API Documentation

This document provides comprehensive documentation for the package management system endpoints in the Laravel + React investment platform. The package system enables one-time purchases of specialized packages alongside the existing subscription model.

## Table of Contents

1. [Authentication](#authentication)
2. [Package Listing](#package-listing)
3. [Package Details](#package-details)
4. [Package Purchases](#package-purchases)
5. [Purchase History](#purchase-history)
6. [<PERSON><PERSON><PERSON>ling](#error-handling)
7. [Integration Examples](#integration-examples)

## Authentication

All authenticated endpoints require Laravel Sanctum authentication:

```
Authorization: Bearer {your-api-token}
```

For web-based requests, include CSRF token:
```
X-CSRF-TOKEN: {csrf-token}
```

## Base URL
```
https://impactintels.test/api
```

---

## Package Listing

### GET `/api/packages`

Retrieve a paginated list of available packages with filtering and search capabilities.

**Authentication:** Required

**Query Parameters:**
- `q` (optional): Search term for service name or description
- `target_role` (optional): Filter by target role (investor, startup, analyst, all)
- `min_price` (optional): Minimum price filter (decimal)
- `max_price` (optional): Maximum price filter (decimal)
- `is_active` (optional): Filter by active status (true/false)
- `sort_by` (optional): Sort field (name, price, created_at) - default: sort_order
- `sort_direction` (optional): Sort direction (asc, desc) - default: asc
- `page` (optional): Page number for pagination - default: 1
- `per_page` (optional): Items per page (max: 100) - default: 20

**Example Request:**
```
GET /api/services?target_role=investor&min_price=50&max_price=500&page=1&per_page=10
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "name": "Advanced ESG Analytics Report",
                "description": "Comprehensive ESG scoring and analytics for investment decision making",
                "price": "299.99",
                "currency": "USD",
                "target_role": "investor",
                "features": [
                    "Detailed ESG scoring methodology",
                    "Industry benchmarking",
                    "Risk assessment matrix",
                    "Regulatory compliance check"
                ],
                "is_active": true,
                "sort_order": 1,
                "created_at": "2025-07-15T10:00:00.000000Z",
                "updated_at": "2025-07-15T10:00:00.000000Z"
            },
            {
                "id": 2,
                "name": "Startup Due Diligence Package",
                "description": "Complete due diligence service for startup evaluation",
                "price": "1999.99",
                "currency": "USD",
                "target_role": "investor",
                "features": [
                    "Financial analysis",
                    "Market research",
                    "Team background checks",
                    "Technology assessment"
                ],
                "is_active": true,
                "sort_order": 2,
                "created_at": "2025-07-15T11:00:00.000000Z",
                "updated_at": "2025-07-15T11:00:00.000000Z"
            }
        ],
        "current_page": 1,
        "last_page": 3,
        "per_page": 10,
        "total": 25,
        "from": 1,
        "to": 10,
        "links": {
            "first": "https://impactintels.test/api/services?page=1",
            "last": "https://impactintels.test/api/services?page=3",
            "prev": null,
            "next": "https://impactintels.test/api/services?page=2"
        }
    }
}
```

**Error Response (401):**
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

---

## Service Details

### GET `/api/services/{id}`

Retrieve detailed information about a specific service.

**Authentication:** Required

**Path Parameters:**
- `id` (required): Service ID

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Advanced ESG Analytics Report",
        "description": "Comprehensive ESG scoring and analytics for investment decision making. Our expert analysts provide detailed insights into environmental, social, and governance factors that impact investment performance.",
        "price": "299.99",
        "currency": "USD",
        "target_role": "investor",
        "features": [
            "Detailed ESG scoring methodology",
            "Industry benchmarking against 500+ companies",
            "Risk assessment matrix with probability scoring",
            "Regulatory compliance check across 15 jurisdictions",
            "Executive summary with actionable insights"
        ],
        "is_active": true,
        "sort_order": 1,
        "created_at": "2025-07-15T10:00:00.000000Z",
        "updated_at": "2025-07-15T10:00:00.000000Z",
        "can_purchase": true,
        "purchase_restrictions": {
            "role_eligible": true,
            "already_purchased": false,
            "service_active": true
        }
    }
}
```

**Error Response (404):**
```json
{
    "success": false,
    "message": "Service not found"
}
```

**Error Response (403):**
```json
{
    "success": false,
    "message": "This service is not available for your role"
}
```

---

---

## Service Purchases

### POST `/api/services/{id}/purchase`

Initiate a service purchase using Stripe Checkout Session.

**Authentication:** Required

**Path Parameters:**
- `id` (required): Service ID to purchase

**Request Body:**
```json
{
    "success_url": "http://localhost:5175/app/services/purchase-success?session_id={CHECKOUT_SESSION_ID}",
    "cancel_url": "http://localhost:5175/app/services?cancelled=true"
}
```

**Field Descriptions:**
- `success_url` (optional): URL to redirect after successful payment. Defaults to platform success page. Use `{CHECKOUT_SESSION_ID}` placeholder for session ID.
- `cancel_url` (optional): URL to redirect if payment is cancelled. Defaults to services listing page.

**Success Response (200):**
```json
{
    "success": true,
    "message": "Checkout session created successfully",
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_a1b2c3d4e5f6g7h8i9j0",
        "session_id": "cs_test_a1b2c3d4e5f6g7h8i9j0",
        "service_purchase_id": 123,
        "service": {
            "id": 1,
            "name": "Advanced ESG Analytics Report",
            "price": "299.99",
            "currency": "USD"
        },
        "expires_at": "2025-07-15T11:00:00.000000Z"
    }
}
```

**Error Response (400):**
```json
{
    "success": false,
    "message": "Invalid URL format",
    "errors": {
        "success_url": ["The success URL must be a valid URL"]
    }
}
```

**Error Response (403):**
```json
{
    "success": false,
    "message": "This service is not available for your role"
}
```

**Error Response (404):**
```json
{
    "success": false,
    "message": "Service not found or not available"
}
```

**Error Response (409):**
```json
{
    "success": false,
    "message": "You have already purchased this service"
}
```

**Error Response (500):**
```json
{
    "success": false,
    "message": "Payment processing error. Please try again later."
}
```

---

## Purchase History

### GET `/api/service-purchases`

Retrieve the authenticated user's service purchase history with filtering options.

**Authentication:** Required

**Query Parameters:**
- `status` (optional): Filter by purchase status (pending, completed, failed, cancelled)
- `service_id` (optional): Filter by specific service ID
- `from_date` (optional): Filter purchases from date (YYYY-MM-DD)
- `to_date` (optional): Filter purchases to date (YYYY-MM-DD)
- `min_amount` (optional): Minimum purchase amount filter
- `max_amount` (optional): Maximum purchase amount filter
- `sort_by` (optional): Sort field (created_at, amount, status) - default: created_at
- `sort_direction` (optional): Sort direction (asc, desc) - default: desc
- `page` (optional): Page number for pagination - default: 1
- `per_page` (optional): Items per page (max: 100) - default: 20

**Example Request:**
```
GET /api/service-purchases?status=completed&from_date=2025-07-01&to_date=2025-07-31&page=1&per_page=10
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 123,
                "user_id": 456,
                "service_id": 1,
                "amount": "299.99",
                "currency": "USD",
                "status": "completed",
                "stripe_session_id": "cs_test_a1b2c3d4e5f6g7h8i9j0",
                "stripe_payment_intent_id": "pi_1234567890abcdef",
                "purchased_at": "2025-07-15T10:30:00.000000Z",
                "created_at": "2025-07-15T10:00:00.000000Z",
                "updated_at": "2025-07-15T10:30:00.000000Z",
                "service": {
                    "id": 1,
                    "name": "Advanced ESG Analytics Report",
                    "description": "Comprehensive ESG scoring and analytics for investment decision making"
                }
            },
            {
                "id": 124,
                "user_id": 456,
                "service_id": 2,
                "amount": "1999.99",
                "currency": "USD",
                "status": "pending",
                "stripe_session_id": "cs_test_b2c3d4e5f6g7h8i9j0k1",
                "stripe_payment_intent_id": null,
                "purchased_at": null,
                "created_at": "2025-07-15T11:00:00.000000Z",
                "updated_at": "2025-07-15T11:00:00.000000Z",
                "service": {
                    "id": 2,
                    "name": "Startup Due Diligence Package",
                    "description": "Complete due diligence service for startup evaluation"
                }
            }
        ],
        "current_page": 1,
        "last_page": 2,
        "per_page": 10,
        "total": 15,
        "from": 1,
        "to": 10,
        "summary": {
            "total_purchases": 15,
            "total_amount": "4299.85",
            "completed_purchases": 12,
            "pending_purchases": 2,
            "failed_purchases": 1
        },
        "links": {
            "first": "https://impactintels.test/api/service-purchases?page=1",
            "last": "https://impactintels.test/api/service-purchases?page=2",
            "prev": null,
            "next": "https://impactintels.test/api/service-purchases?page=2"
        }
    }
}
```

---

## Error Handling

### Common HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Access denied (role restrictions)
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict (e.g., already purchased)
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server error

### Error Response Format

All error responses follow this structure:
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Specific validation error"]
    }
}
```

### Common Error Scenarios

**Validation Errors (422):**
```json
{
    "success": false,
    "message": "The given data was invalid",
    "errors": {
        "success_url": ["The success URL must be a valid URL"],
        "cancel_url": ["The cancel URL must be a valid URL"]
    }
}
```

**Role Restriction (403):**
```json
{
    "success": false,
    "message": "This service is only available for investor accounts"
}
```

**Service Unavailable (404):**
```json
{
    "success": false,
    "message": "Service is currently unavailable or has been discontinued"
}
```

**Stripe Configuration Error (500):**
```json
{
    "success": false,
    "message": "Payment processing is temporarily unavailable. Please try again later."
}
```

---

## Integration Examples

### 1. Service Listing Component

```javascript
import { useState, useEffect } from 'react';
import axios from 'axios';

const ServiceListing = () => {
    const [services, setServices] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState({
        target_role: '',
        min_price: '',
        max_price: '',
        search: ''
    });
    const [pagination, setPagination] = useState({
        current_page: 1,
        per_page: 20,
        total: 0
    });

    const fetchServices = async (page = 1) => {
        try {
            setLoading(true);
            const token = localStorage.getItem('auth_token');
            
            // Build query parameters
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: pagination.per_page.toString()
            });
            
            // Add filters
            Object.entries(filters).forEach(([key, value]) => {
                if (value) {
                    if (key === 'search') {
                        params.append('q', value);
                    } else {
                        params.append(key, value);
                    }
                }
            });

            const response = await axios.get(`/api/services?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            });

            if (response.data.success) {
                setServices(response.data.data.data);
                setPagination({
                    current_page: response.data.data.current_page,
                    per_page: response.data.data.per_page,
                    total: response.data.data.total,
                    last_page: response.data.data.last_page
                });
            }
        } catch (error) {
            console.error('Error fetching services:', error);
            if (error.response?.status === 401) {
                // Handle authentication error
                localStorage.removeItem('auth_token');
                window.location.href = '/login';
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchServices();
    }, []);

    useEffect(() => {
        fetchServices(1); // Reset to first page when filters change
    }, [filters]);

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handlePageChange = (page) => {
        fetchServices(page);
    };

    if (loading && services.length === 0) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold mb-4">Filter Services</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Search */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Search
                        </label>
                        <input
                            type="text"
                            value={filters.search}
                            onChange={(e) => handleFilterChange('search', e.target.value)}
                            placeholder="Search services..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    {/* Target Role Filter */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Target Role
                        </label>
                        <select
                            value={filters.target_role}
                            onChange={(e) => handleFilterChange('target_role', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Roles</option>
                            <option value="investor">Investor</option>
                            <option value="startup">Startup</option>
                            <option value="analyst">Analyst</option>
                            <option value="all">All Users</option>
                        </select>
                    </div>

                    {/* Price Range */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Min Price ($)
                        </label>
                        <input
                            type="number"
                            value={filters.min_price}
                            onChange={(e) => handleFilterChange('min_price', e.target.value)}
                            placeholder="0"
                            min="0"
                            step="0.01"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Max Price ($)
                        </label>
                        <input
                            type="number"
                            value={filters.max_price}
                            onChange={(e) => handleFilterChange('max_price', e.target.value)}
                            placeholder="No limit"
                            min="0"
                            step="0.01"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {services.map(service => (
                    <ServiceCard 
                        key={service.id} 
                        service={service} 
                        onPurchase={() => handlePurchase(service.id)}
                    />
                ))}
            </div>

            {/* Pagination */}
            {pagination.total > pagination.per_page && (
                <div className="flex justify-center items-center space-x-2">
                    <button
                        onClick={() => handlePageChange(pagination.current_page - 1)}
                        disabled={pagination.current_page === 1}
                        className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        Previous
                    </button>
                    
                    <span className="px-4 py-2 text-sm text-gray-700">
                        Page {pagination.current_page} of {pagination.last_page}
                    </span>
                    
                    <button
                        onClick={() => handlePageChange(pagination.current_page + 1)}
                        disabled={pagination.current_page === pagination.last_page}
                        className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        Next
                    </button>
                </div>
            )}

            {/* No Results */}
            {!loading && services.length === 0 && (
                <div className="text-center py-12">
                    <div className="text-gray-500 text-lg">No services found</div>
                    <p className="text-gray-400 mt-2">Try adjusting your filters or search terms</p>
                </div>
            )}
        </div>
    );
};

// Service Card Component
const ServiceCard = ({ service, onPurchase }) => {
    return (
        <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
            <div className="p-6">
                <div className="mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                        {service.name}
                    </h3>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {service.description}
                </p>

                <div className="space-y-2 mb-4">
                    {service.features.slice(0, 3).map((feature, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {feature}
                        </div>
                    ))}
                    {service.features.length > 3 && (
                        <div className="text-xs text-gray-500">
                            +{service.features.length - 3} more features
                        </div>
                    )}
                </div>

                <div className="flex justify-between items-center">
                    <div className="text-2xl font-bold text-gray-900">
                        ${service.price}
                    </div>
                    <button
                        onClick={() => onPurchase(service.id)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                        Purchase
                    </button>
                </div>
            </div>
        </div>
    );
};
```

### 2. Service Purchase Flow

```javascript
const handlePurchase = async (serviceId) => {
    try {
        const token = localStorage.getItem('auth_token');

        // Show loading state
        setLoading(true);

        const response = await axios.post(`/api/services/${serviceId}/purchase`, {
            success_url: `${window.location.origin}/app/services/purchase-success?session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: `${window.location.origin}/app/services?cancelled=true`
        }, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        if (response.data.success && response.data.data.checkout_url) {
            // Store purchase info for success page
            localStorage.setItem('pending_purchase', JSON.stringify({
                service_purchase_id: response.data.data.service_purchase_id,
                service: response.data.data.service,
                session_id: response.data.data.session_id
            }));

            // Redirect to Stripe Checkout
            window.location.href = response.data.data.checkout_url;
        }
    } catch (error) {
        console.error('Purchase error:', error);

        // Handle specific error cases
        if (error.response?.status === 403) {
            alert('This service is not available for your account type.');
        } else if (error.response?.status === 409) {
            alert('You have already purchased this service.');
        } else if (error.response?.status === 404) {
            alert('This service is no longer available.');
        } else {
            alert('Unable to process purchase. Please try again later.');
        }
    } finally {
        setLoading(false);
    }
};
```

### 3. Purchase Success Handling

```javascript
// Purchase Success Page Component
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const PurchaseSuccess = () => {
    const [searchParams] = useSearchParams();
    const [purchaseInfo, setPurchaseInfo] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const sessionId = searchParams.get('session_id');

        if (sessionId) {
            // Get stored purchase info
            const storedInfo = localStorage.getItem('pending_purchase');
            if (storedInfo) {
                const purchaseData = JSON.parse(storedInfo);
                if (purchaseData.session_id === sessionId) {
                    setPurchaseInfo(purchaseData);
                    // Clean up stored data
                    localStorage.removeItem('pending_purchase');
                }
            }

            // Optionally verify purchase status with backend
            verifyPurchase(sessionId);
        }

        setLoading(false);
    }, [searchParams]);

    const verifyPurchase = async (sessionId) => {
        try {
            const token = localStorage.getItem('auth_token');
            const response = await axios.get(`/api/service-purchases?session_id=${sessionId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            });

            if (response.data.success && response.data.data.data.length > 0) {
                const purchase = response.data.data.data[0];
                if (purchase.status === 'completed') {
                    // Purchase confirmed
                    console.log('Purchase verified:', purchase);
                }
            }
        } catch (error) {
            console.error('Error verifying purchase:', error);
        }
    };

    if (loading) {
        return <div className="text-center py-12">Verifying purchase...</div>;
    }

    return (
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm border p-6 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Purchase Successful!
            </h2>

            {purchaseInfo && (
                <div className="text-left bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 className="font-semibold text-gray-900 mb-2">
                        {purchaseInfo.service.name}
                    </h3>
                    <div className="text-sm text-gray-600 space-y-1">
                        <div>Amount: ${purchaseInfo.service.price}</div>
                        <div>Purchase ID: #{purchaseInfo.service_purchase_id}</div>
                    </div>
                </div>
            )}

            <p className="text-gray-600 mb-6">
                Thank you for your purchase! You will receive a confirmation email shortly.
            </p>

            <div className="space-y-3">
                <button
                    onClick={() => window.location.href = '/app/services/purchases'}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
                >
                    View Purchase History
                </button>
                <button
                    onClick={() => window.location.href = '/app/services'}
                    className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300"
                >
                    Browse More Services
                </button>
            </div>
        </div>
    );
};
```

### 4. Purchase History Component

```javascript
const PurchaseHistory = () => {
    const [purchases, setPurchases] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState({
        status: '',
        from_date: '',
        to_date: ''
    });
    const [pagination, setPagination] = useState({
        current_page: 1,
        per_page: 20,
        total: 0
    });

    const fetchPurchases = async (page = 1) => {
        try {
            setLoading(true);
            const token = localStorage.getItem('auth_token');

            const params = new URLSearchParams({
                page: page.toString(),
                per_page: pagination.per_page.toString()
            });

            // Add filters
            Object.entries(filters).forEach(([key, value]) => {
                if (value) {
                    params.append(key, value);
                }
            });

            const response = await axios.get(`/api/service-purchases?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            });

            if (response.data.success) {
                setPurchases(response.data.data.data);
                setPagination({
                    current_page: response.data.data.current_page,
                    per_page: response.data.data.per_page,
                    total: response.data.data.total,
                    last_page: response.data.data.last_page
                });
            }
        } catch (error) {
            console.error('Error fetching purchases:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPurchases();
    }, []);

    useEffect(() => {
        fetchPurchases(1);
    }, [filters]);

    const getStatusBadge = (status) => {
        const statusConfig = {
            completed: { bg: 'bg-green-100', text: 'text-green-800', label: 'Completed' },
            pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
            failed: { bg: 'bg-red-100', text: 'text-red-800', label: 'Failed' },
            cancelled: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Cancelled' }
        };

        const config = statusConfig[status] || statusConfig.pending;

        return (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.bg} ${config.text}`}>
                {config.label}
            </span>
        );
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold text-gray-900">Purchase History</h1>
            </div>

            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Status
                        </label>
                        <select
                            value={filters.status}
                            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Statuses</option>
                            <option value="completed">Completed</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            From Date
                        </label>
                        <input
                            type="date"
                            value={filters.from_date}
                            onChange={(e) => setFilters(prev => ({ ...prev, from_date: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            To Date
                        </label>
                        <input
                            type="date"
                            value={filters.to_date}
                            onChange={(e) => setFilters(prev => ({ ...prev, to_date: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div className="flex items-end">
                        <button
                            onClick={() => setFilters({ status: '', from_date: '', to_date: '' })}
                            className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            Clear Filters
                        </button>
                    </div>
                </div>
            </div>

            {/* Purchase List */}
            <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
                {loading ? (
                    <div className="p-8 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    </div>
                ) : purchases.length > 0 ? (
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Service
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {purchases.map(purchase => (
                                    <tr key={purchase.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    {purchase.service.name}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {purchase.service.category}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                ${purchase.amount}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            {getStatusBadge(purchase.status)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {new Date(purchase.created_at).toLocaleDateString()}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                ) : (
                    <div className="p-8 text-center">
                        <div className="text-gray-500">No purchases found</div>
                        <p className="text-gray-400 mt-2">You haven't purchased any services yet</p>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {pagination.total > pagination.per_page && (
                <div className="flex justify-center">
                    <nav className="flex space-x-2">
                        <button
                            onClick={() => fetchPurchases(pagination.current_page - 1)}
                            disabled={pagination.current_page === 1}
                            className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                        >
                            Previous
                        </button>

                        <span className="px-4 py-2 text-sm text-gray-700">
                            Page {pagination.current_page} of {pagination.last_page}
                        </span>

                        <button
                            onClick={() => fetchPurchases(pagination.current_page + 1)}
                            disabled={pagination.current_page === pagination.last_page}
                            className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                        >
                            Next
                        </button>
                    </nav>
                </div>
            )}
        </div>
    );
};
```

### 5. Error Handling Utilities

```javascript
// API Error Handler Utility
export const handleApiError = (error, defaultMessage = 'An error occurred') => {
    if (error.response) {
        const { status, data } = error.response;

        switch (status) {
            case 401:
                // Handle authentication errors
                localStorage.removeItem('auth_token');
                window.location.href = '/login';
                return 'Please log in to continue';

            case 403:
                return data.message || 'Access denied';

            case 404:
                return data.message || 'Resource not found';

            case 409:
                return data.message || 'Conflict occurred';

            case 422:
                // Handle validation errors
                if (data.errors) {
                    const errorMessages = Object.values(data.errors).flat();
                    return errorMessages.join(', ');
                }
                return data.message || 'Validation failed';

            case 500:
                return 'Server error. Please try again later.';

            default:
                return data.message || defaultMessage;
        }
    } else if (error.request) {
        return 'Network error. Please check your connection.';
    } else {
        return defaultMessage;
    }
};

// Usage in components
const handlePurchaseWithErrorHandling = async (serviceId) => {
    try {
        // ... purchase logic
    } catch (error) {
        const errorMessage = handleApiError(error, 'Failed to process purchase');
        setErrorMessage(errorMessage);
        // Show error to user (toast, alert, etc.)
    }
};
```

### 6. Custom Hooks for Service Management

```javascript
// useServices Hook
import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

export const useServices = (initialFilters = {}) => {
    const [services, setServices] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current_page: 1,
        per_page: 20,
        total: 0,
        last_page: 1
    });
    const [filters, setFilters] = useState(initialFilters);

    const fetchServices = useCallback(async (page = 1) => {
        try {
            setLoading(true);
            setError(null);

            const token = localStorage.getItem('auth_token');
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: pagination.per_page.toString(),
                ...filters
            });

            const response = await axios.get(`/api/services?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            });

            if (response.data.success) {
                setServices(response.data.data.data);
                setPagination({
                    current_page: response.data.data.current_page,
                    per_page: response.data.data.per_page,
                    total: response.data.data.total,
                    last_page: response.data.data.last_page
                });
            }
        } catch (err) {
            setError(handleApiError(err, 'Failed to fetch services'));
        } finally {
            setLoading(false);
        }
    }, [filters, pagination.per_page]);

    const updateFilters = useCallback((newFilters) => {
        setFilters(prev => ({ ...prev, ...newFilters }));
    }, []);

    const resetFilters = useCallback(() => {
        setFilters(initialFilters);
    }, [initialFilters]);

    useEffect(() => {
        fetchServices(1);
    }, [filters]);

    return {
        services,
        loading,
        error,
        pagination,
        filters,
        fetchServices,
        updateFilters,
        resetFilters,
        refetch: () => fetchServices(pagination.current_page)
    };
};

// usePurchaseHistory Hook
export const usePurchaseHistory = (initialFilters = {}) => {
    const [purchases, setPurchases] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current_page: 1,
        per_page: 20,
        total: 0,
        last_page: 1
    });
    const [filters, setFilters] = useState(initialFilters);

    const fetchPurchases = useCallback(async (page = 1) => {
        try {
            setLoading(true);
            setError(null);

            const token = localStorage.getItem('auth_token');
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: pagination.per_page.toString(),
                ...filters
            });

            const response = await axios.get(`/api/service-purchases?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            });

            if (response.data.success) {
                setPurchases(response.data.data.data);
                setPagination({
                    current_page: response.data.data.current_page,
                    per_page: response.data.data.per_page,
                    total: response.data.data.total,
                    last_page: response.data.data.last_page
                });
            }
        } catch (err) {
            setError(handleApiError(err, 'Failed to fetch purchase history'));
        } finally {
            setLoading(false);
        }
    }, [filters, pagination.per_page]);

    const updateFilters = useCallback((newFilters) => {
        setFilters(prev => ({ ...prev, ...newFilters }));
    }, []);

    useEffect(() => {
        fetchPurchases(1);
    }, [filters]);

    return {
        purchases,
        loading,
        error,
        pagination,
        filters,
        fetchPurchases,
        updateFilters,
        refetch: () => fetchPurchases(pagination.current_page)
    };
};
```

---

## Testing with Stripe

### Test Environment Setup

The service system uses Stripe's test environment. Use these test credentials:

**Test Card Numbers:**
- **Successful payment:** `4242 4242 4242 4242`
- **Declined payment:** `4000 0000 0000 0002`
- **Insufficient funds:** `4000 0000 0000 9995`
- **3D Secure authentication:** `4000 0025 0000 3155`

**Test Details:**
- **Expiry:** Any future date (e.g., `12/25`)
- **CVC:** Any 3-digit number (e.g., `123`)
- **ZIP:** Any valid postal code

### Testing Workflow

1. **Create a test service** in the admin panel
2. **Browse services** using the React frontend
3. **Initiate purchase** - should redirect to Stripe Checkout
4. **Complete payment** using test card numbers
5. **Verify success** - should redirect back to success page
6. **Check purchase history** - purchase should appear with "completed" status

---

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authenticated requests:** 60 requests per minute per user
- **Service purchase requests:** 10 requests per minute per user
- **Search requests:** 30 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1642694400
```

---

## Webhooks

The system handles Stripe webhooks automatically for purchase completion:

**Webhook Endpoint:** `POST /api/stripe/webhook`

**Supported Events:**
- `checkout.session.completed` - Updates purchase status to "completed"
- `payment_intent.succeeded` - Confirms successful payment
- `payment_intent.payment_failed` - Marks purchase as "failed"

**Webhook Security:**
- Stripe signature verification is enforced
- Idempotency handling prevents duplicate processing
- Failed webhooks are logged for manual review

---

## Best Practices

### 1. Authentication Management
```javascript
// Always check token validity before API calls
const isTokenValid = () => {
    const token = localStorage.getItem('auth_token');
    if (!token) return false;

    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.exp > Date.now() / 1000;
    } catch {
        return false;
    }
};
```

### 2. Loading States
```javascript
// Provide clear loading feedback
const [loading, setLoading] = useState(false);

return (
    <button disabled={loading} className="...">
        {loading ? (
            <>
                <svg className="animate-spin h-4 w-4 mr-2" />
                Processing...
            </>
        ) : (
            'Purchase Service'
        )}
    </button>
);
```

### 3. Error Boundaries
```javascript
// Wrap service components in error boundaries
class ServiceErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Service component error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="text-center py-8">
                    <h2>Something went wrong</h2>
                    <button onClick={() => window.location.reload()}>
                        Reload Page
                    </button>
                </div>
            );
        }

        return this.props.children;
    }
}
```

### 4. Caching Strategies
```javascript
// Cache service categories to reduce API calls
const useServiceCategories = () => {
    const [categories, setCategories] = useState([]);

    useEffect(() => {
        const cached = localStorage.getItem('service_categories');
        const cacheTime = localStorage.getItem('service_categories_time');

        // Use cache if less than 1 hour old
        if (cached && cacheTime && Date.now() - parseInt(cacheTime) < 3600000) {
            setCategories(JSON.parse(cached));
            return;
        }

        // Fetch fresh data
        fetchCategories().then(data => {
            setCategories(data);
            localStorage.setItem('service_categories', JSON.stringify(data));
            localStorage.setItem('service_categories_time', Date.now().toString());
        });
    }, []);

    return categories;
};
```

---

## Support and Troubleshooting

### Common Issues

**1. Checkout Session Expires**
- Sessions expire after 24 hours
- Create new session if user returns to expired checkout

**2. Payment Method Declined**
- Show clear error message from Stripe
- Suggest alternative payment methods
- Provide customer support contact

**3. Network Timeouts**
- Implement retry logic with exponential backoff
- Show offline indicators when network is unavailable
- Cache critical data for offline viewing

### Debug Mode

Enable debug mode in development:
```javascript
// Add to your API client
const apiClient = axios.create({
    baseURL: '/api',
    headers: {
        'X-Debug-Mode': process.env.NODE_ENV === 'development'
    }
});
```

This will include additional debugging information in API responses during development.
```
