# Investor Registration API Documentation

This document provides comprehensive documentation for the investor registration and management API endpoints in the Laravel + React investment platform.

## Table of Contents

1. [Authentication](#authentication)
2. [Investor Registration](#investor-registration)
3. [Focus Regions Management](#focus-regions-management)
4. [Investment Focus Management](#investment-focus-management)
5. [Investor Profile Management](#investor-profile-management)
6. [<PERSON>rro<PERSON>](#error-handling)
7. [Integration Examples](#integration-examples)

## Authentication

All authenticated endpoints require a Bearer token in the Authorization header:

```
Authorization: Bearer {your-api-token}
```

## Investor Registration

### POST `/api/investor/register`

Register a new investor with comprehensive profile information.

**Description:** Register a new investor user with enhanced profile creation including social media integration, taxonomy relationships, and investment preferences.

**Request Body:**
```json
{
  "company_name": "Venture Capital Partners",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "password_confirmation": "SecurePass123!",
  "phone_number": "+****************",
  "country": "United States",
  "website_url": "https://vcpartners.com",
  "linkedin_url": "https://linkedin.com/company/vcpartners",
  "description": "A leading venture capital firm focused on early-stage technology startups with a strong emphasis on AI and fintech innovations.",
  "focus_regions": ["North America", "Europe", "Asia Pacific"],
  "investment_focus": ["Artificial Intelligence", "Fintech", "Healthcare", "SaaS"],
  "investment_check_min": 100000,
  "investment_check_max": 5000000,
  "investment_stages": ["Seed", "Series A", "Series B"],
  "founded_date": "2015-01-01",
  "number_of_investments": 25
}
```

**Field Descriptions:**
- `company_name` (required): Name of the investment firm or company
- `email` (required): Unique email address for the investor account
- `password` (required): Secure password (min 8 chars with mixed case, numbers, symbols in production)
- `password_confirmation` (required): Password confirmation
- `phone_number` (optional): Contact phone number
- `country` (optional): Country of operation
- `website_url` (optional): Company website URL
- `linkedin_url` (optional): LinkedIn company profile URL
- `description` (optional): Company description and investment philosophy
- `focus_regions` (optional): Array of geographic focus regions
- `investment_focus` (optional): Array of industry/technology focus areas
- `investment_check_min` (optional): Minimum investment amount in USD
- `investment_check_max` (optional): Maximum investment amount in USD
- `investment_stages` (optional): Array of preferred funding stages
- `founded_date` (optional): Company founding date (YYYY-MM-DD)
- `number_of_investments` (optional): Total number of investments made

**Success Response (201):**
```json
{
  "success": true,
  "message": "Investor registration successful. Please verify your email address.",
  "data": {
    "user": {
      "id": 123,
      "name": "Venture Capital Partners",
      "email": "<EMAIL>",
      "role": "investor",
      "email_verified_at": null,
      "phone": "+****************",
      "country": "United States"
    },
    "investor_profile": {
      "id": 456,
      "company_name": "Venture Capital Partners",
      "bio": "A leading venture capital firm...",
      "investment_budget_min": 100000,
      "investment_budget_max": 5000000,
      "founded_date": "2015-01-01",
      "number_of_investments": 25,
      "profile_completed": true
    },
    "token": "1|abc123def456...",
    "next_steps": {
      "email_verification": true,
      "profile_completion": false,
      "subscription_required": true,
      "discovery_access": true
    }
  }
}
```

**Error Response (422):**
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "email": ["The email has already been taken."],
    "password": ["The password confirmation does not match."],
    "investment_check_max": ["The investment check max must be greater than or equal to investment check min."]
  }
}
```

## Focus Regions Management

### GET `/api/focus-regions`

Get all available focus regions for investor selection.

**Description:** Retrieve a list of all geographic focus regions available for investor profile configuration.

**Query Parameters:**
- `search` (optional): Search term to filter regions
- `limit` (optional): Maximum number of results (default: 50, max: 100)

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "North America",
        "slug": "north-america",
        "description": "United States, Canada, and Mexico",
        "type": "focus_region"
      },
      {
        "id": 2,
        "name": "Europe",
        "slug": "europe",
        "description": "European Union and surrounding countries",
        "type": "focus_region"
      }
    ],
    "meta": {
      "total": 10,
      "per_page": 50,
      "current_page": 1
    }
  }
}
```

### GET `/api/investor/focus-regions` (Authenticated)

Get investor's selected focus regions.

**Success Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "North America",
      "slug": "north-america",
      "description": "United States, Canada, and Mexico",
      "type": "focus_region"
    }
  ]
}
```

### POST `/api/investor/focus-regions/attach` (Authenticated)

Attach focus regions to investor profile.

**Request Body:**
```json
{
  "taxonomy_ids": [1, 2, 3]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Focus_region selection updated successfully.",
  "data": [
    {
      "id": 1,
      "name": "North America",
      "slug": "north-america",
      "description": "United States, Canada, and Mexico",
      "type": "focus_region"
    }
  ],
  "meta": {
    "type": "focus_region",
    "count": 3,
    "limit": 10
  }
}
```

### POST `/api/investor/focus-regions/detach` (Authenticated)

Remove focus regions from investor profile.

**Request Body:**
```json
{
  "taxonomy_ids": [1, 2]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Taxonomies removed successfully."
}
```

## Investment Focus Management

### GET `/api/investment-focus`

Get all available investment focus areas.

**Description:** Retrieve a list of all industry and technology focus areas available for investor profile configuration.

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 11,
        "name": "Artificial Intelligence",
        "slug": "artificial-intelligence",
        "description": "AI, Machine Learning, and Deep Learning technologies",
        "type": "investment_focus"
      },
      {
        "id": 12,
        "name": "Fintech",
        "slug": "fintech",
        "description": "Financial Technology and Digital Banking Solutions",
        "type": "investment_focus"
      }
    ]
  }
}
```

### GET `/api/investor/investment-focus` (Authenticated)

Get investor's selected investment focus areas.

### POST `/api/investor/investment-focus/attach` (Authenticated)

Attach investment focus areas to investor profile.

### POST `/api/investor/investment-focus/detach` (Authenticated)

Remove investment focus areas from investor profile.

*Note: These endpoints follow the same patterns as focus regions management.*

## Investor Profile Management

### GET `/api/investor/registration-progress` (Authenticated)

Get investor registration and profile completion progress.

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "progress": {
      "user_created": true,
      "email_verified": true,
      "basic_profile": true,
      "profile_completed": true,
      "social_media_links": true,
      "focus_regions_selected": true,
      "investment_focus_selected": true,
      "subscription_active": false
    },
    "progress_percentage": 87.5,
    "next_steps": [
      "Subscribe to access discovery features"
    ],
    "profile": {
      "id": 456,
      "company_name": "Venture Capital Partners",
      "bio": "A leading venture capital firm...",
      "investment_budget_min": 100000,
      "investment_budget_max": 5000000,
      "founded_date": "2015-01-01",
      "number_of_investments": 25,
      "profile_completed": true
    }
  }
}
```

## Error Handling

### Common Error Responses

**401 Unauthorized:**
```json
{
  "success": false,
  "message": "Unauthenticated."
}
```

**403 Forbidden:**
```json
{
  "success": false,
  "message": "Access denied. Investor role required."
}
```

**404 Not Found:**
```json
{
  "success": false,
  "message": "Investor profile not found. Please create your profile first."
}
```

**422 Validation Error:**
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "field_name": ["Specific validation error message"]
  }
}
```

**500 Server Error:**
```json
{
  "success": false,
  "message": "Registration failed. Please try again.",
  "errors": {
    "general": ["An error occurred during registration."]
  }
}
```

## Integration Examples

### React Frontend Integration

```javascript
// Investor Registration
const registerInvestor = async (formData) => {
  try {
    const response = await fetch('/api/investor/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(formData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Store token
      localStorage.setItem('token', result.data.token);
      // Redirect to dashboard or email verification
      return result;
    } else {
      // Handle validation errors
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Registration failed:', error);
    throw error;
  }
};

// Get Focus Regions
const getFocusRegions = async () => {
  const response = await fetch('/api/focus-regions');
  return await response.json();
};

// Attach Focus Regions (Authenticated)
const attachFocusRegions = async (taxonomyIds, token) => {
  const response = await fetch('/api/investor/focus-regions/attach', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ taxonomy_ids: taxonomyIds })
  });
  return await response.json();
};
```

### Laravel Backend Usage

```php
// Create investor programmatically
$investor = User::create([
    'name' => 'Investment Firm',
    'email' => '<EMAIL>',
    'password' => Hash::make('password'),
    'role' => 'investor',
]);

// Create investor profile
$profile = InvestorProfile::create([
    'user_id' => $investor->id,
    'company_name' => 'Investment Firm',
    'bio' => 'Description...',
    'investment_budget_min' => 100000,
    'investment_budget_max' => 1000000,
]);

// Attach taxonomies
$focusRegions = Taxonomy::where('type', 'focus_region')
    ->whereIn('name', ['North America', 'Europe'])
    ->pluck('id');
$profile->attachTaxonomies($focusRegions);
```

## Rate Limiting

- Registration endpoint: 5 attempts per minute per IP
- Authenticated endpoints: 60 requests per minute per user
- Public taxonomy endpoints: 100 requests per minute per IP

## Versioning

Current API version: v1
Base URL: `/api/`

For questions or support, please contact the development team.
