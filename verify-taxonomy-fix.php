<?php

/**
 * Verification script for taxonomy fix
 * Run this with: php verify-taxonomy-fix.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

echo "=== Taxonomy Fix Verification ===\n\n";

// Test 1: Create a simple taxonomy
echo "Test 1: Creating a simple taxonomy...\n";
try {
    $taxonomy = Taxonomy::create([
        'name' => 'Test Category',
        'type' => 'category',
        'description' => 'This is a test category',
    ]);
    echo "✅ SUCCESS: Taxonomy created with ID: {$taxonomy->id}\n";
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

// Test 2: Create a taxonomy with parent
echo "\nTest 2: Creating a taxonomy with parent...\n";
try {
    $parent = Taxonomy::create([
        'name' => 'Parent Category',
        'type' => 'category',
        'description' => 'Parent category',
    ]);
    
    $child = Taxonomy::create([
        'name' => 'Child Category',
        'type' => 'category',
        'description' => 'Child category',
        'parent_id' => $parent->id,
    ]);
    echo "✅ SUCCESS: Parent taxonomy created with ID: {$parent->id}\n";
    echo "✅ SUCCESS: Child taxonomy created with ID: {$child->id}\n";
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

// Test 3: Create different taxonomy types
echo "\nTest 3: Creating different taxonomy types...\n";
$types = ['keyword', 'focus_region', 'investment_focus', 'industry', 'technology'];

foreach ($types as $type) {
    try {
        $taxonomy = Taxonomy::create([
            'name' => ucfirst($type) . ' Test',
            'type' => $type,
            'description' => "Test {$type} taxonomy",
        ]);
        echo "✅ SUCCESS: {$type} taxonomy created with ID: {$taxonomy->id}\n";
    } catch (Exception $e) {
        echo "❌ ERROR creating {$type}: " . $e->getMessage() . "\n";
    }
}

// Test 4: Verify methods exist
echo "\nTest 4: Verifying taxonomy methods...\n";
try {
    $taxonomy = Taxonomy::first();
    if ($taxonomy) {
        // Test methods that should exist
        $methods = ['save', 'create', 'update', 'delete'];
        foreach ($methods as $method) {
            if (method_exists($taxonomy, $method)) {
                echo "✅ Method '{$method}' exists\n";
            } else {
                echo "❌ Method '{$method}' does not exist\n";
            }
        }
        
        // Test methods that should NOT exist (the problematic ones)
        $badMethods = ['saveAsRoot', 'appendNode', 'appendToNode'];
        foreach ($badMethods as $method) {
            if (method_exists($taxonomy, $method)) {
                echo "⚠️  Method '{$method}' still exists (this might be okay if it's from the package)\n";
            } else {
                echo "✅ Method '{$method}' does not exist (good, we're not using it)\n";
            }
        }
    }
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== Verification Complete ===\n";
echo "If you see mostly ✅ SUCCESS messages above, the taxonomy fix is working correctly.\n";
echo "The fix replaces the non-existent saveAsRoot() method with the standard create() method.\n";
