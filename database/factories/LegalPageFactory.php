<?php

namespace Database\Factories;

use App\Models\LegalPage;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LegalPage>
 */
class LegalPageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LegalPage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['terms-and-conditions', 'privacy-policy'];
        $type = $this->faker->randomElement($types);

        $titles = [
            'terms-and-conditions' => 'Terms and Conditions',
            'privacy-policy' => 'Privacy Policy',
        ];

        return [
            'type' => $type,
            'title' => $titles[$type],
            'content' => $this->generateContent($type),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'last_updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'updated_by' => User::factory(),
        ];
    }

    /**
     * Generate content based on type
     */
    private function generateContent(string $type): string
    {
        if ($type === 'terms-and-conditions') {
            return '<h1>Terms and Conditions</h1>
                    <h2>1. Acceptance of Terms</h2>
                    <p>' . $this->faker->paragraph(3) . '</p>
                    <h2>2. Use of Service</h2>
                    <p>' . $this->faker->paragraph(4) . '</p>
                    <h2>3. User Responsibilities</h2>
                    <p>' . $this->faker->paragraph(3) . '</p>
                    <h2>4. Privacy Policy</h2>
                    <p>' . $this->faker->paragraph(2) . '</p>';
        }

        return '<h1>Privacy Policy</h1>
                <h2>Information We Collect</h2>
                <p>' . $this->faker->paragraph(3) . '</p>
                <h2>How We Use Information</h2>
                <p>' . $this->faker->paragraph(4) . '</p>
                <h2>Information Sharing</h2>
                <p>' . $this->faker->paragraph(3) . '</p>
                <h2>Contact Us</h2>
                <p>' . $this->faker->paragraph(2) . '</p>';
    }

    /**
     * Indicate that the legal page is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the legal page is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create terms and conditions page.
     */
    public function termsAndConditions(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
        ]);
    }

    /**
     * Create privacy policy page.
     */
    public function privacyPolicy(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'privacy-policy',
            'title' => 'Privacy Policy',
        ]);
    }
}
