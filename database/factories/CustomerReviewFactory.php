<?php

namespace Database\Factories;

use App\Models\CustomerReview;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerReview>
 */
class CustomerReviewFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerReview::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $designations = ['CEO', 'CTO', 'Founder', 'Co-Founder', 'VP of Engineering', 'Product Manager', 'Director', 'Manager'];
        $companies = ['Tech Corp', 'Innovation Labs', 'StartupCo', 'Digital Solutions', 'Future Systems', 'NextGen Tech', 'Smart Ventures'];

        return [
            'customer_name' => $this->faker->name(),
            'designation' => $this->faker->optional(0.8)->randomElement($designations), // 80% chance of having designation
            'company' => $this->faker->optional(0.7)->randomElement($companies), // 70% chance of having company
            'message' => $this->generateReviewMessage(),
            'rating' => $this->faker->optional(0.8)->numberBetween(1, 5), // 80% chance of having rating
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'sort_order' => $this->faker->numberBetween(0, 100),
        ];
    }

    /**
     * Generate a realistic review message
     */
    private function generateReviewMessage(): string
    {
        $positiveStarters = [
            'Excellent service!',
            'Outstanding experience!',
            'Highly recommend!',
            'Amazing platform!',
            'Great team to work with!',
            'Fantastic results!',
        ];

        $positiveEnders = [
            'Will definitely use again.',
            'Exceeded our expectations.',
            'Highly professional team.',
            'Great value for money.',
            'Perfect solution for our needs.',
            'Outstanding customer support.',
        ];

        $starter = $this->faker->randomElement($positiveStarters);
        $middle = $this->faker->paragraph(2);
        $ender = $this->faker->randomElement($positiveEnders);

        return "{$starter} {$middle} {$ender}";
    }

    /**
     * Indicate that the review is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the review is not featured.
     */
    public function notFeatured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => false,
        ]);
    }

    /**
     * Indicate that the review is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the review is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create review with specific rating.
     */
    public function withRating(int $rating): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $rating,
        ]);
    }

    /**
     * Create review without rating.
     */
    public function withoutRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => null,
        ]);
    }

    /**
     * Create review with specific sort order.
     */
    public function withSortOrder(int $sortOrder): static
    {
        return $this->state(fn (array $attributes) => [
            'sort_order' => $sortOrder,
        ]);
    }

    /**
     * Create review with full company information.
     */
    public function withFullCompanyInfo(): static
    {
        return $this->state(fn (array $attributes) => [
            'designation' => $this->faker->randomElement(['CEO', 'CTO', 'Founder']),
            'company' => $this->faker->company(),
        ]);
    }
}
