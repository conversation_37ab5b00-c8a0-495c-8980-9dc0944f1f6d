<?php

namespace Database\Factories;

use App\Models\ContactSubmission;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContactSubmission>
 */
class ContactSubmissionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ContactSubmission::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $statuses = ['new', 'read', 'replied', 'archived'];
        $status = $this->faker->randomElement($statuses);

        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->safeEmail(),
            'phone' => $this->faker->optional(0.7)->phoneNumber(), // 70% chance of having phone
            'subject' => $this->faker->sentence(6),
            'message' => $this->faker->paragraphs(3, true),
            'status' => $status,
            'read_at' => $status !== 'new' ? $this->faker->dateTimeBetween('-1 week', 'now') : null,
            'read_by' => $status !== 'new' ? User::factory() : null,
            'admin_notes' => $this->faker->optional(0.3)->paragraph(), // 30% chance of having notes
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
        ];
    }

    /**
     * Indicate that the contact submission is new.
     */
    public function new(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'new',
            'read_at' => null,
            'read_by' => null,
        ]);
    }

    /**
     * Indicate that the contact submission has been read.
     */
    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'read',
            'read_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'read_by' => User::factory(),
        ]);
    }

    /**
     * Indicate that the contact submission has been replied to.
     */
    public function replied(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'replied',
            'read_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'read_by' => User::factory(),
            'admin_notes' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Indicate that the contact submission is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'archived',
            'read_at' => $this->faker->dateTimeBetween('-1 month', '-1 week'),
            'read_by' => User::factory(),
        ]);
    }

    /**
     * Create contact submission with admin notes.
     */
    public function withNotes(): static
    {
        return $this->state(fn (array $attributes) => [
            'admin_notes' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Create contact submission without phone number.
     */
    public function withoutPhone(): static
    {
        return $this->state(fn (array $attributes) => [
            'phone' => null,
        ]);
    }
}
