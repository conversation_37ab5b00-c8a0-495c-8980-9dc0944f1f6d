<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add referral_source field to track how users found the platform
            $table->string('referral_source', 100)->nullable()->after('country');

            // Add index for analytics and reporting
            $table->index(['referral_source']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop index first
            $table->dropIndex(['referral_source']);

            // Drop column
            $table->dropColumn('referral_source');
        });
    }
};
