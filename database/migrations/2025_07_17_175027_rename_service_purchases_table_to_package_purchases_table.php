<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename service_purchases table to package_purchases
        Schema::rename('service_purchases', 'package_purchases');

        // Rename the foreign key column from service_id to package_id
        Schema::table('package_purchases', function (Blueprint $table) {
            $table->renameColumn('service_id', 'package_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rename the foreign key column back from package_id to service_id
        Schema::table('package_purchases', function (Blueprint $table) {
            $table->renameColumn('package_id', 'service_id');
        });

        // Rename package_purchases table back to service_purchases
        Schema::rename('package_purchases', 'service_purchases');
    }
};
