<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_settings', function (Blueprint $table) {
            $table->id();

            // Logo settings
            $table->string('header_logo')->nullable();
            $table->string('footer_logo')->nullable();
            $table->string('header_logo_dark')->nullable();
            $table->string('footer_logo_dark')->nullable();

            // Favicon settings
            $table->string('favicon_ico')->nullable();
            $table->string('favicon_png_16')->nullable();
            $table->string('favicon_png_32')->nullable();
            $table->string('favicon_png_192')->nullable();
            $table->string('favicon_svg')->nullable();

            // Email settings
            $table->string('mail_driver')->default('smtp');
            $table->string('mail_host')->nullable();
            $table->integer('mail_port')->nullable();
            $table->string('mail_username')->nullable();
            $table->string('mail_password')->nullable();
            $table->string('mail_encryption')->nullable();
            $table->string('mail_from_address')->nullable();
            $table->string('mail_from_name')->nullable();

            // Additional website settings
            $table->string('site_name')->nullable();
            $table->text('site_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('google_analytics_id')->nullable();
            $table->string('facebook_pixel_id')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('website_settings');
    }
};
