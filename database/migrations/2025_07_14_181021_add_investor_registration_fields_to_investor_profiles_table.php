<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('investor_profiles', function (Blueprint $table) {
            // Company information
            $table->string('company_name')->nullable()->after('user_id');

            // Investment details
            $table->date('founded_date')->nullable()->after('bio');
            $table->integer('number_of_investments')->nullable()->after('founded_date');

            // Add indexes for better query performance
            $table->index(['company_name']);
            $table->index(['founded_date']);
            $table->index(['number_of_investments']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('investor_profiles', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['company_name']);
            $table->dropIndex(['founded_date']);
            $table->dropIndex(['number_of_investments']);

            // Drop columns
            $table->dropColumn([
                'company_name',
                'founded_date',
                'number_of_investments'
            ]);
        });
    }
};
