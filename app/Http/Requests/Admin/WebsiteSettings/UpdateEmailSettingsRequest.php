<?php

namespace App\Http\Requests\Admin\WebsiteSettings;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmailSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user() && in_array(auth()->user()->role, ['admin', 'super-admin']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'mail_driver' => [
                'required',
                'string',
                Rule::in(['smtp', 'sendmail', 'mailgun', 'ses', 'postmark', 'log', 'array'])
            ],
            'mail_host' => [
                'required_if:mail_driver,smtp',
                'nullable',
                'string',
                'max:255'
            ],
            'mail_port' => [
                'required_if:mail_driver,smtp',
                'nullable',
                'integer',
                'min:1',
                'max:65535'
            ],
            'mail_username' => [
                'nullable',
                'string',
                'max:255'
            ],
            'mail_password' => [
                'nullable',
                'string',
                'max:255'
            ],
            'mail_encryption' => [
                'nullable',
                'string',
                Rule::in(['tls', 'ssl', null])
            ],
            'mail_from_address' => [
                'required',
                'email',
                'max:255'
            ],
            'mail_from_name' => [
                'required',
                'string',
                'max:255'
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'mail_driver.required' => 'Mail driver is required.',
            'mail_driver.in' => 'Invalid mail driver selected.',
            
            'mail_host.required_if' => 'Mail host is required when using SMTP driver.',
            'mail_host.string' => 'Mail host must be a valid string.',
            'mail_host.max' => 'Mail host must not exceed 255 characters.',
            
            'mail_port.required_if' => 'Mail port is required when using SMTP driver.',
            'mail_port.integer' => 'Mail port must be a valid integer.',
            'mail_port.min' => 'Mail port must be at least 1.',
            'mail_port.max' => 'Mail port must not exceed 65535.',
            
            'mail_username.string' => 'Mail username must be a valid string.',
            'mail_username.max' => 'Mail username must not exceed 255 characters.',
            
            'mail_password.string' => 'Mail password must be a valid string.',
            'mail_password.max' => 'Mail password must not exceed 255 characters.',
            
            'mail_encryption.in' => 'Mail encryption must be either TLS or SSL.',
            
            'mail_from_address.required' => 'From email address is required.',
            'mail_from_address.email' => 'From email address must be a valid email.',
            'mail_from_address.max' => 'From email address must not exceed 255 characters.',
            
            'mail_from_name.required' => 'From name is required.',
            'mail_from_name.string' => 'From name must be a valid string.',
            'mail_from_name.max' => 'From name must not exceed 255 characters.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'mail_driver' => 'mail driver',
            'mail_host' => 'mail host',
            'mail_port' => 'mail port',
            'mail_username' => 'mail username',
            'mail_password' => 'mail password',
            'mail_encryption' => 'mail encryption',
            'mail_from_address' => 'from email address',
            'mail_from_name' => 'from name',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation for SMTP configuration
            if ($this->input('mail_driver') === 'smtp') {
                // Validate common SMTP ports
                $port = $this->input('mail_port');
                $commonPorts = [25, 465, 587, 2525];
                
                if ($port && !in_array($port, $commonPorts)) {
                    $validator->errors()->add('mail_port', 'Warning: Port ' . $port . ' is not a common SMTP port. Common ports are: ' . implode(', ', $commonPorts));
                }

                // Validate encryption based on port
                $encryption = $this->input('mail_encryption');
                if ($port == 465 && $encryption !== 'ssl') {
                    $validator->errors()->add('mail_encryption', 'Port 465 typically requires SSL encryption.');
                }
                
                if (in_array($port, [587, 2525]) && $encryption !== 'tls') {
                    $validator->errors()->add('mail_encryption', 'Ports 587 and 2525 typically require TLS encryption.');
                }
            }

            // Validate email provider specific settings
            $host = $this->input('mail_host');
            if ($host) {
                $knownProviders = [
                    'smtp.gmail.com' => ['port' => 587, 'encryption' => 'tls'],
                    'smtp.sendgrid.net' => ['port' => 587, 'encryption' => 'tls'],
                    'smtp.mailgun.org' => ['port' => 587, 'encryption' => 'tls'],
                    'smtp.postmarkapp.com' => ['port' => 587, 'encryption' => 'tls'],
                ];

                if (isset($knownProviders[$host])) {
                    $recommended = $knownProviders[$host];
                    $currentPort = $this->input('mail_port');
                    $currentEncryption = $this->input('mail_encryption');

                    if ($currentPort != $recommended['port']) {
                        $validator->errors()->add('mail_port', 'For ' . $host . ', the recommended port is ' . $recommended['port']);
                    }

                    if ($currentEncryption != $recommended['encryption']) {
                        $validator->errors()->add('mail_encryption', 'For ' . $host . ', the recommended encryption is ' . strtoupper($recommended['encryption']));
                    }
                }
            }

            // Validate from address domain for certain providers
            $fromAddress = $this->input('mail_from_address');
            if ($fromAddress && $host) {
                if (strpos($host, 'gmail.com') !== false && !str_ends_with($fromAddress, '@gmail.com')) {
                    $validator->errors()->add('mail_from_address', 'When using Gmail SMTP, the from address should typically be a Gmail address.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert port to integer if provided
        if ($this->has('mail_port') && is_string($this->mail_port)) {
            $this->merge([
                'mail_port' => (int) $this->mail_port
            ]);
        }

        // Trim whitespace from string fields
        $stringFields = ['mail_host', 'mail_username', 'mail_from_address', 'mail_from_name'];
        $trimmed = [];
        
        foreach ($stringFields as $field) {
            if ($this->has($field)) {
                $trimmed[$field] = trim($this->input($field));
            }
        }
        
        if (!empty($trimmed)) {
            $this->merge($trimmed);
        }
    }
}
