<?php

namespace App\Http\Requests\Admin\WebsiteSettings;

use Illuminate\Foundation\Http\FormRequest;

class UpdateLogoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user() && in_array(auth()->user()->role, ['admin', 'super-admin']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'header_logo' => [
                'nullable',
                'image',
                'mimes:jpeg,jpg,png,svg',
                'max:5120', // 5MB
                'dimensions:max_width=1200,max_height=400'
            ],
            'footer_logo' => [
                'nullable',
                'image',
                'mimes:jpeg,jpg,png,svg',
                'max:2048', // 2MB
                'dimensions:max_width=600,max_height=200'
            ],
            'header_logo_dark' => [
                'nullable',
                'image',
                'mimes:jpeg,jpg,png,svg',
                'max:5120', // 5MB
                'dimensions:max_width=1200,max_height=400'
            ],
            'footer_logo_dark' => [
                'nullable',
                'image',
                'mimes:jpeg,jpg,png,svg',
                'max:2048', // 2MB
                'dimensions:max_width=600,max_height=200'
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'header_logo.image' => 'Header logo must be a valid image file.',
            'header_logo.mimes' => 'Header logo must be a JPEG, JPG, PNG, or SVG file.',
            'header_logo.max' => 'Header logo file size must not exceed 5MB.',
            'header_logo.dimensions' => 'Header logo dimensions must not exceed 1200x400 pixels.',
            
            'footer_logo.image' => 'Footer logo must be a valid image file.',
            'footer_logo.mimes' => 'Footer logo must be a JPEG, JPG, PNG, or SVG file.',
            'footer_logo.max' => 'Footer logo file size must not exceed 2MB.',
            'footer_logo.dimensions' => 'Footer logo dimensions must not exceed 600x200 pixels.',
            
            'header_logo_dark.image' => 'Header dark logo must be a valid image file.',
            'header_logo_dark.mimes' => 'Header dark logo must be a JPEG, JPG, PNG, or SVG file.',
            'header_logo_dark.max' => 'Header dark logo file size must not exceed 5MB.',
            'header_logo_dark.dimensions' => 'Header dark logo dimensions must not exceed 1200x400 pixels.',
            
            'footer_logo_dark.image' => 'Footer dark logo must be a valid image file.',
            'footer_logo_dark.mimes' => 'Footer dark logo must be a JPEG, JPG, PNG, or SVG file.',
            'footer_logo_dark.max' => 'Footer dark logo file size must not exceed 2MB.',
            'footer_logo_dark.dimensions' => 'Footer dark logo dimensions must not exceed 600x200 pixels.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'header_logo' => 'header logo',
            'footer_logo' => 'footer logo',
            'header_logo_dark' => 'header dark logo',
            'footer_logo_dark' => 'footer dark logo',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here
            
            // Check if at least one logo is being uploaded
            $hasAnyLogo = $this->hasFile('header_logo') || 
                         $this->hasFile('footer_logo') || 
                         $this->hasFile('header_logo_dark') || 
                         $this->hasFile('footer_logo_dark');
            
            if (!$hasAnyLogo) {
                $validator->errors()->add('general', 'Please select at least one logo file to upload.');
            }

            // Validate image dimensions more precisely if needed
            foreach (['header_logo', 'header_logo_dark'] as $field) {
                if ($this->hasFile($field)) {
                    $file = $this->file($field);
                    if ($file->isValid()) {
                        $imageSize = getimagesize($file->getPathname());
                        if ($imageSize && ($imageSize[0] < 100 || $imageSize[1] < 30)) {
                            $validator->errors()->add($field, 'The ' . str_replace('_', ' ', $field) . ' is too small. Minimum dimensions: 100x30 pixels.');
                        }
                    }
                }
            }

            foreach (['footer_logo', 'footer_logo_dark'] as $field) {
                if ($this->hasFile($field)) {
                    $file = $this->file($field);
                    if ($file->isValid()) {
                        $imageSize = getimagesize($file->getPathname());
                        if ($imageSize && ($imageSize[0] < 50 || $imageSize[1] < 20)) {
                            $validator->errors()->add($field, 'The ' . str_replace('_', ' ', $field) . ' is too small. Minimum dimensions: 50x20 pixels.');
                        }
                    }
                }
            }
        });
    }
}
