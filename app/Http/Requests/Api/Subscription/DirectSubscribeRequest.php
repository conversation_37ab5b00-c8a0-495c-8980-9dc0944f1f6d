<?php

namespace App\Http\Requests\Api\Subscription;

use App\Models\SubscriptionProduct;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DirectSubscribeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'product_id' => [
                'required',
                'integer',
                Rule::exists('subscription_products', 'id')->where(function ($query) {
                    $query->where('is_active', true);
                }),
            ],
            'success_url' => [
                'nullable',
                'url',
                'max:2048',
            ],
            'cancel_url' => [
                'nullable',
                'url',
                'max:2048',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'A subscription product must be selected.',
            'product_id.exists' => 'The selected subscription product is not available.',
            'success_url.url' => 'The success URL must be a valid URL.',
            'cancel_url.url' => 'The cancel URL must be a valid URL.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'subscription product',
            'success_url' => 'success URL',
            'cancel_url' => 'cancel URL',
        ];
    }

    /**
     * Get the subscription product instance.
     */
    public function getSubscriptionProduct(): SubscriptionProduct
    {
        return SubscriptionProduct::findOrFail($this->validated('product_id'));
    }

    /**
     * Get the success URL or default.
     */
    public function getSuccessUrl(): string
    {
        return $this->validated('success_url') ?? config('app.frontend_url', 'http://localhost:5175') . '/subscription/success?session_id={CHECKOUT_SESSION_ID}';
    }

    /**
     * Get the cancel URL or default.
     */
    public function getCancelUrl(): string
    {
        return $this->validated('cancel_url') ?? config('app.frontend_url', 'http://localhost:5175') . '/subscription/cancel';
    }
}
