<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\WebsiteSettings\UpdateLogoRequest;
use App\Http\Requests\Admin\WebsiteSettings\UpdateEmailSettingsRequest;
use App\Models\WebsiteSetting;
use App\Services\FileUploadService;
use App\Services\EmailConfigService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;

class AdminWebsiteSettingsController extends Controller
{
    protected FileUploadService $fileUploadService;
    protected EmailConfigService $emailConfigService;

    public function __construct(FileUploadService $fileUploadService, EmailConfigService $emailConfigService)
    {
        $this->fileUploadService = $fileUploadService;
        $this->emailConfigService = $emailConfigService;
    }

    /**
     * Display website settings dashboard
     */
    public function index(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Website Settings',
                'url' => route('admin.website-settings.index'),
                'active' => true
            ],
        ];

        $settings = WebsiteSetting::first() ?? new WebsiteSetting();

        return view('admin.website-settings.index', [
            'pageTitle' => 'Website Settings',
            'breadcrumbItems' => $breadcrumbsItems,
            'settings' => $settings
        ]);
    }

    /**
     * Show logo management page
     */
    public function logoManagement(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Website Settings',
                'url' => route('admin.website-settings.index'),
                'active' => false
            ],
            [
                'name' => 'Logo Management',
                'url' => route('admin.website-settings.logo-management'),
                'active' => true
            ],
        ];

        $settings = WebsiteSetting::first() ?? new WebsiteSetting();

        return view('admin.website-settings.logo-management', [
            'pageTitle' => 'Logo Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'settings' => $settings
        ]);
    }

    /**
     * Update logo settings
     */
    public function updateLogos(UpdateLogoRequest $request): RedirectResponse
    {
        try {
            $settings = WebsiteSetting::firstOrCreate([]);
            $validated = $request->validated();

            // Handle logo uploads
            if ($request->hasFile('header_logo')) {
                $headerLogoPath = $this->fileUploadService->uploadImage(
                    $request->file('header_logo'),
                    'logos',
                    'header_logo',
                    [800, 200] // max dimensions
                );
                $settings->header_logo = $headerLogoPath;
            }

            if ($request->hasFile('footer_logo')) {
                $footerLogoPath = $this->fileUploadService->uploadImage(
                    $request->file('footer_logo'),
                    'logos',
                    'footer_logo',
                    [400, 100]
                );
                $settings->footer_logo = $footerLogoPath;
            }

            if ($request->hasFile('header_logo_dark')) {
                $headerLogoDarkPath = $this->fileUploadService->uploadImage(
                    $request->file('header_logo_dark'),
                    'logos',
                    'header_logo_dark',
                    [800, 200]
                );
                $settings->header_logo_dark = $headerLogoDarkPath;
            }

            if ($request->hasFile('footer_logo_dark')) {
                $footerLogoDarkPath = $this->fileUploadService->uploadImage(
                    $request->file('footer_logo_dark'),
                    'logos',
                    'footer_logo_dark',
                    [400, 100]
                );
                $settings->footer_logo_dark = $footerLogoDarkPath;
            }

            $settings->save();

            return redirect()->back()->with([
                'message' => 'Logo settings updated successfully.',
                'type' => 'success'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update logo settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with([
                'message' => 'Failed to update logo settings. Please try again.',
                'type' => 'error'
            ])->withInput();
        }
    }

    /**
     * Show favicon management page
     */
    public function faviconManagement(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Website Settings',
                'url' => route('admin.website-settings.index'),
                'active' => false
            ],
            [
                'name' => 'Favicon Management',
                'url' => route('admin.website-settings.favicon-management'),
                'active' => true
            ],
        ];

        $settings = WebsiteSetting::first() ?? new WebsiteSetting();

        return view('admin.website-settings.favicon-management', [
            'pageTitle' => 'Favicon Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'settings' => $settings
        ]);
    }

    /**
     * Update favicon settings
     */
    public function updateFavicons(Request $request): RedirectResponse
    {
        $request->validate([
            'favicon_ico' => 'nullable|file|mimes:ico|max:1024',
            'favicon_png_16' => 'nullable|image|mimes:png|max:512',
            'favicon_png_32' => 'nullable|image|mimes:png|max:512',
            'favicon_png_192' => 'nullable|image|mimes:png|max:2048',
            'favicon_svg' => 'nullable|file|mimes:svg|max:1024',
        ]);

        try {
            $settings = WebsiteSetting::firstOrCreate([]);

            // Handle favicon uploads with automatic resizing
            if ($request->hasFile('favicon_ico')) {
                $faviconIcoPath = $this->fileUploadService->uploadFile(
                    $request->file('favicon_ico'),
                    'favicons'
                );
                $settings->favicon_ico = $faviconIcoPath;
            }

            if ($request->hasFile('favicon_png_16')) {
                $faviconPng16Path = $this->generateFaviconSize(
                    $request->file('favicon_png_16'),
                    16,
                    'favicon_16x16.png'
                );
                $settings->favicon_png_16 = $faviconPng16Path;
            }

            if ($request->hasFile('favicon_png_32')) {
                $faviconPng32Path = $this->generateFaviconSize(
                    $request->file('favicon_png_32'),
                    32,
                    'favicon_32x32.png'
                );
                $settings->favicon_png_32 = $faviconPng32Path;
            }

            if ($request->hasFile('favicon_png_192')) {
                $faviconPng192Path = $this->generateFaviconSize(
                    $request->file('favicon_png_192'),
                    192,
                    'favicon_192x192.png'
                );
                $settings->favicon_png_192 = $faviconPng192Path;
            }

            if ($request->hasFile('favicon_svg')) {
                $faviconSvgPath = $this->fileUploadService->uploadFile(
                    $request->file('favicon_svg'),
                    'favicons'
                );
                $settings->favicon_svg = $faviconSvgPath;
            }

            $settings->save();

            return redirect()->back()->with([
                'message' => 'Favicon settings updated successfully.',
                'type' => 'success'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update favicon settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with([
                'message' => 'Failed to update favicon settings. Please try again.',
                'type' => 'error'
            ])->withInput();
        }
    }

    /**
     * Show email settings page
     */
    public function emailSettings(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Website Settings',
                'url' => route('admin.website-settings.index'),
                'active' => false
            ],
            [
                'name' => 'Email Settings',
                'url' => route('admin.website-settings.email-settings'),
                'active' => true
            ],
        ];

        $settings = WebsiteSetting::first() ?? new WebsiteSetting();
        $emailProviders = $this->emailConfigService->getEmailProviders();

        return view('admin.website-settings.email-settings', [
            'pageTitle' => 'Email Settings',
            'breadcrumbItems' => $breadcrumbsItems,
            'settings' => $settings,
            'emailProviders' => $emailProviders
        ]);
    }

    /**
     * Update email settings
     */
    public function updateEmailSettings(UpdateEmailSettingsRequest $request): RedirectResponse
    {
        try {
            $settings = WebsiteSetting::firstOrCreate([]);
            $validated = $request->validated();

            // Update email configuration
            $settings->update([
                'mail_driver' => $validated['mail_driver'],
                'mail_host' => $validated['mail_host'],
                'mail_port' => $validated['mail_port'],
                'mail_username' => $validated['mail_username'],
                'mail_password' => $validated['mail_password'] ?? $settings->mail_password,
                'mail_encryption' => $validated['mail_encryption'],
                'mail_from_address' => $validated['mail_from_address'],
                'mail_from_name' => $validated['mail_from_name'],
            ]);

            // Update environment variables
            $this->emailConfigService->updateEmailConfig($validated);

            return redirect()->back()->with([
                'message' => 'Email settings updated successfully.',
                'type' => 'success'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update email settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with([
                'message' => 'Failed to update email settings. Please try again.',
                'type' => 'error'
            ])->withInput();
        }
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request): RedirectResponse
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            $testEmail = $request->input('test_email');

            Mail::raw('This is a test email from your Laravel application. If you receive this, your email configuration is working correctly.', function ($message) use ($testEmail) {
                $message->to($testEmail)
                        ->subject('Test Email - Email Configuration Working');
            });

            return redirect()->back()->with([
                'message' => 'Test email sent successfully to ' . $testEmail,
                'type' => 'success'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send test email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with([
                'message' => 'Failed to send test email: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

    /**
     * Generate favicon with specific size
     */
    private function generateFaviconSize($file, int $size, string $filename): string
    {
        $image = Image::make($file);
        $image->resize($size, $size);

        $path = 'favicons/' . $filename;
        Storage::disk('public')->put($path, $image->encode('png'));

        return $path;
    }
}
