<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CustomerReview;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Spatie\QueryBuilder\QueryBuilder;

class AdminCustomerReviewController extends Controller
{
    /**
     * Display a listing of customer reviews
     */
    public function index(Request $request): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Customer Reviews',
                'url' => route('admin.reviews.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $featured = $request->get('featured');
        $active = $request->get('active');
        $perPage = $request->get('per_page', 10);

        $reviews = QueryBuilder::for(CustomerReview::class)
            ->when($q, function ($query) use ($q) {
                $query->where(function ($subQuery) use ($q) {
                    $subQuery->where('customer_name', 'like', "%{$q}%")
                        ->orWhere('designation', 'like', "%{$q}%")
                        ->orWhere('company', 'like', "%{$q}%")
                        ->orWhere('message', 'like', "%{$q}%");
                });
            })
            ->when($featured !== null, function ($query) use ($featured) {
                $query->where('is_featured', $featured === '1');
            })
            ->when($active !== null, function ($query) use ($active) {
                $query->where('is_active', $active === '1');
            })
            ->ordered()
            ->paginate($perPage);

        return view('admin.reviews.index', [
            'pageTitle' => 'Customer Reviews Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'reviews' => $reviews,
            'filters' => [
                'q' => $q,
                'featured' => $featured,
                'active' => $active,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Show the form for creating a new review
     */
    public function create(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Customer Reviews',
                'url' => route('admin.reviews.index'),
                'active' => false
            ],
            [
                'name' => 'Create Review',
                'url' => route('admin.reviews.create'),
                'active' => true
            ],
        ];

        return view('admin.reviews.create', [
            'pageTitle' => 'Create Customer Review',
            'breadcrumbItems' => $breadcrumbsItems,
        ]);
    }

    /**
     * Store a newly created review
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'designation' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'message' => 'required|string',
            'rating' => 'nullable|integer|min:1|max:5',
            'sort_order' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'avatar' => 'nullable|image|mimes:jpeg,png,webp|max:2048',
        ]);

        try {
            $review = CustomerReview::create([
                'customer_name' => $request->customer_name,
                'designation' => $request->designation,
                'company' => $request->company,
                'message' => $request->message,
                'rating' => $request->rating,
                'sort_order' => $request->sort_order ?? 0,
                'is_featured' => $request->boolean('is_featured'),
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Handle avatar upload
            if ($request->hasFile('avatar')) {
                $review->addMediaFromRequest('avatar')
                    ->toMediaCollection('avatar');
            }

            return redirect()->route('admin.reviews.index')
                ->with('message', 'Customer review created successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create customer review: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified review
     */
    public function edit(CustomerReview $review): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Customer Reviews',
                'url' => route('admin.reviews.index'),
                'active' => false
            ],
            [
                'name' => 'Edit Review',
                'url' => route('admin.reviews.edit', $review),
                'active' => true
            ],
        ];

        return view('admin.reviews.edit', [
            'pageTitle' => 'Edit Customer Review',
            'breadcrumbItems' => $breadcrumbsItems,
            'review' => $review,
        ]);
    }

    /**
     * Update the specified review
     */
    public function update(Request $request, CustomerReview $review): RedirectResponse
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'designation' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'message' => 'required|string',
            'rating' => 'nullable|integer|min:1|max:5',
            'sort_order' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'avatar' => 'nullable|image|mimes:jpeg,png,webp|max:2048',
        ]);

        try {
            $review->update([
                'customer_name' => $request->customer_name,
                'designation' => $request->designation,
                'company' => $request->company,
                'message' => $request->message,
                'rating' => $request->rating,
                'sort_order' => $request->sort_order ?? 0,
                'is_featured' => $request->boolean('is_featured'),
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Handle avatar upload
            if ($request->hasFile('avatar')) {
                $review->clearMediaCollection('avatar');
                $review->addMediaFromRequest('avatar')
                    ->toMediaCollection('avatar');
            }

            return redirect()->route('admin.reviews.index')
                ->with('message', 'Customer review updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update customer review: ' . $e->getMessage());
        }
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(CustomerReview $review): RedirectResponse
    {
        try {
            $review->toggleFeatured();

            $status = $review->is_featured ? 'featured' : 'unfeatured';
            return redirect()->back()
                ->with('message', "Review {$status} successfully.");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update review status: ' . $e->getMessage());
        }
    }

    /**
     * Toggle active status
     */
    public function toggleActive(CustomerReview $review): RedirectResponse
    {
        try {
            $review->toggleActive();

            $status = $review->is_active ? 'activated' : 'deactivated';
            return redirect()->back()
                ->with('message', "Review {$status} successfully.");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update review status: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified review
     */
    public function destroy(CustomerReview $review): RedirectResponse
    {
        try {
            $review->delete();

            return redirect()->route('admin.reviews.index')
                ->with('message', 'Customer review deleted successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete customer review: ' . $e->getMessage());
        }
    }
}
