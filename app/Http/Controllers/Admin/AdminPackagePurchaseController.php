<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PackagePurchase;
use App\Models\Package;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Spatie\QueryBuilder\QueryBuilder;

class AdminPackagePurchaseController extends Controller
{
    /**
     * Display a listing of package purchases
     */
    public function index(Request $request): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Package Purchases',
                'url' => route('admin.package-purchases.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $package = $request->get('package');
        $perPage = $request->get('per_page', 10);

        $purchases = QueryBuilder::for(PackagePurchase::class)
            ->allowedFilters(['status'])
            ->with(['user', 'package'])
            ->when($q, function ($query) use ($q) {
                $query->whereHas('user', function ($userQuery) use ($q) {
                    $userQuery->where('name', 'like', "%{$q}%")
                        ->orWhere('email', 'like', "%{$q}%");
                })->orWhereHas('package', function ($packageQuery) use ($q) {
                    $packageQuery->where('name', 'like', "%{$q}%");
                });
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->when($package, function ($query) use ($package) {
                $query->where('package_id', $package);
            })
            ->latest()
            ->paginate($perPage);

        $packages = Package::active()->orderBy('name')->get();
        $statusOptions = ['pending', 'completed', 'failed', 'refunded'];

        return view('admin.package-purchases.index', [
            'pageTitle' => 'Package Purchase Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'purchases' => $purchases,
            'packages' => $packages,
            'statusOptions' => $statusOptions,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'package' => $package,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Display the specified package purchase
     */
    public function show(PackagePurchase $packagePurchase): View
    {
        $packagePurchase->load(['user', 'package']);

        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Package Purchases',
                'url' => route('admin.package-purchases.index'),
                'active' => false
            ],
            [
                'name' => 'Purchase Details',
                'url' => route('admin.package-purchases.show', $packagePurchase),
                'active' => true
            ],
        ];

        return view('admin.package-purchases.show', [
            'pageTitle' => 'Package Purchase Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'purchase' => $packagePurchase,
        ]);
    }

    /**
     * Update the specified package purchase status
     */
    public function update(Request $request, PackagePurchase $packagePurchase): RedirectResponse
    {
        $request->validate([
            'status' => 'required|in:pending,completed,failed,refunded',
        ]);

        try {
            $oldStatus = $packagePurchase->status;
            $newStatus = $request->status;

            // Update the purchase status
            $packagePurchase->update([
                'status' => $newStatus,
                'purchased_at' => $newStatus === 'completed' ? now() : $packagePurchase->purchased_at,
            ]);

            return redirect()->back()
                ->with('message', "Package purchase status updated from {$oldStatus} to {$newStatus}.");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update package purchase status: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified package purchase
     */
    public function destroy(PackagePurchase $packagePurchase): RedirectResponse
    {
        try {
            // Only allow deletion of failed or pending purchases
            if (in_array($packagePurchase->status, ['completed', 'refunded'])) {
                return redirect()->back()
                    ->with('error', 'Cannot delete completed or refunded package purchases.');
            }

            $packagePurchase->delete();

            return redirect()->route('admin.package-purchases.index')
                ->with('message', 'Package purchase deleted successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete package purchase: ' . $e->getMessage());
        }
    }
}
