<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Spatie\QueryBuilder\QueryBuilder;

class AdminContactController extends Controller
{
    /**
     * Display a listing of contact submissions
     */
    public function index(Request $request): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Contact Submissions',
                'url' => route('admin.contacts.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $perPage = $request->get('per_page', 10);

        $contacts = QueryBuilder::for(ContactSubmission::class)
            ->allowedFilters(['status'])
            ->with(['readBy'])
            ->when($q, function ($query) use ($q) {
                $query->where(function ($subQuery) use ($q) {
                    $subQuery->where('name', 'like', "%{$q}%")
                        ->orWhere('email', 'like', "%{$q}%")
                        ->orWhere('subject', 'like', "%{$q}%")
                        ->orWhere('message', 'like', "%{$q}%");
                });
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->latest()
            ->paginate($perPage);

        $statusOptions = ['new', 'read', 'replied', 'archived'];

        return view('admin.contacts.index', [
            'pageTitle' => 'Contact Submissions Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'contacts' => $contacts,
            'statusOptions' => $statusOptions,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Display the specified contact submission
     */
    public function show(ContactSubmission $contact): View
    {
        // Mark as read if it's new
        if ($contact->isNew()) {
            $contact->markAsRead();
        }

        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Contact Submissions',
                'url' => route('admin.contacts.index'),
                'active' => false
            ],
            [
                'name' => 'Contact Details',
                'url' => route('admin.contacts.show', $contact),
                'active' => true
            ],
        ];

        return view('admin.contacts.show', [
            'pageTitle' => 'Contact Submission Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'contact' => $contact->load('readBy'),
        ]);
    }

    /**
     * Update the specified contact submission
     */
    public function update(Request $request, ContactSubmission $contact): RedirectResponse
    {
        $request->validate([
            'status' => 'required|in:new,read,replied,archived',
            'admin_notes' => 'nullable|string',
        ]);

        try {
            $contact->update([
                'status' => $request->status,
                'admin_notes' => $request->admin_notes,
            ]);

            // Update read status if changing to read or replied
            if (in_array($request->status, ['read', 'replied']) && !$contact->read_at) {
                $contact->update([
                    'read_at' => now(),
                    'read_by' => auth()->id(),
                ]);
            }

            return redirect()->back()
                ->with('message', 'Contact submission updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update contact submission: ' . $e->getMessage());
        }
    }

    /**
     * Mark contact as replied
     */
    public function markAsReplied(ContactSubmission $contact): RedirectResponse
    {
        try {
            $contact->markAsReplied();

            return redirect()->back()
                ->with('message', 'Contact submission marked as replied.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update contact submission: ' . $e->getMessage());
        }
    }

    /**
     * Archive contact submission
     */
    public function archive(ContactSubmission $contact): RedirectResponse
    {
        try {
            $contact->archive();

            return redirect()->back()
                ->with('message', 'Contact submission archived successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to archive contact submission: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified contact submission
     */
    public function destroy(ContactSubmission $contact): RedirectResponse
    {
        try {
            $contact->delete();

            return redirect()->route('admin.contacts.index')
                ->with('message', 'Contact submission deleted successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete contact submission: ' . $e->getMessage());
        }
    }
}
