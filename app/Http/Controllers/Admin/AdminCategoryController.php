<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class AdminCategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|super-admin']);

        // Bind the category parameter to only resolve taxonomies of type 'category'
        $this->middleware(function ($request, $next) {
            if ($request->route('category')) {
                $taxonomy = Taxonomy::where('type', 'category')
                    ->where('id', $request->route('category'))
                    ->firstOrFail();
                $request->route()->setParameter('category', $taxonomy);
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of categories
     */
    public function index(Request $request): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => null,
                'active' => true,
            ],
        ];

        $query = Taxonomy::where('type', 'category');

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by parent category for hierarchical view
        if ($request->filled('parent_id')) {
            if ($request->parent_id === 'root') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->parent_id);
            }
        }

        // Order by hierarchical structure (nested set model)
        $categories = $query->orderBy('lft')->paginate(15);

        // Get all categories for parent selection
        $allCategories = Taxonomy::where('type', 'category')->orderBy('lft')->get();

        // Get category types for filtering
        $categoryTypes = ['category', 'industry', 'technology', 'keyword'];

        // Calculate hierarchical stats
        $stats = [
            'total' => Taxonomy::where('type', 'category')->count(),
            'parent_categories' => Taxonomy::where('type', 'category')->whereNull('parent_id')->count(),
            'child_categories' => Taxonomy::where('type', 'category')->whereNotNull('parent_id')->count(),
            'max_depth' => Taxonomy::where('type', 'category')->max('depth') ?? 0,
        ];

        return view('admin.categories.index', [
            'pageTitle' => 'Categories Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'categories' => $categories,
            'allCategories' => $allCategories,
            'categoryTypes' => $categoryTypes,
            'currentParentId' => $request->parent_id,
            'stats' => $stats,
            'filters' => $request->only(['search', 'type', 'parent_id']),
        ]);
    }

    /**
     * Get the full path of a category (e.g., "Technology > Software > SaaS")
     */
    private function getCategoryPath(Taxonomy $category): string
    {
        $path = [];
        $current = $category;

        while ($current) {
            array_unshift($path, $current->name);
            $current = $current->parent;
        }

        return implode(' > ', $path);
    }

    /**
     * Show the form for creating a new category
     */
    public function create(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => route('admin.categories.index'),
                'active' => false,
            ],
            [
                'name' => 'Create Category',
                'url' => null,
                'active' => true,
            ],
        ];

        // Get all categories for parent selection
        $parentCategories = Taxonomy::where('type', 'category')->orderBy('name')->get();
        $categoryTypes = ['category', 'industry', 'technology', 'keyword'];

        return view('admin.categories.create', [
            'pageTitle' => 'Create Category',
            'breadcrumbItems' => $breadcrumbsItems,
            'parentCategories' => $parentCategories,
            'categoryTypes' => $categoryTypes,
        ]);
    }

    /**
     * Store a newly created category
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:taxonomies,name',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:category,industry,technology,keyword',
            'parent_id' => 'nullable|exists:taxonomies,id',
        ]);

        // Prevent circular references
        if ($validated['parent_id']) {
            $parent = Taxonomy::find($validated['parent_id']);
            if (!$parent || $parent->type !== 'category') {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['parent_id' => 'Invalid parent category selected.']);
            }
        }

        // Create the taxonomy with parent relationship
        $taxonomy = Taxonomy::create($validated);

        return redirect()->route('admin.categories.index')
            ->with('message', 'Category created successfully.');
    }

    /**
     * Display the specified category
     */
    public function show(Taxonomy $category): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => route('admin.categories.index'),
                'active' => false,
            ],
            [
                'name' => $category->name,
                'url' => null,
                'active' => true,
            ],
        ];

        // Get users associated with this category (through the taxonomable relationship)
        $users = $category->taxonomables()
            ->where('taxonomable_type', 'App\\Models\\User')
            ->with(['taxonomable.investorProfile', 'taxonomable.startupProfile'])
            ->paginate(10);

        return view('admin.categories.show', [
            'pageTitle' => 'Category Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'category' => $category,
            'users' => $users,
        ]);
    }

    /**
     * Show the form for editing the specified category
     */
    public function edit(Taxonomy $category): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false,
            ],
            [
                'name' => 'Categories',
                'url' => route('admin.categories.index'),
                'active' => false,
            ],
            [
                'name' => 'Edit ' . $category->name,
                'url' => null,
                'active' => true,
            ],
        ];

        // Get all categories for parent selection (excluding current category)
        $parentCategories = Taxonomy::where('type', 'category')
            ->where('id', '!=', $category->id)
            ->orderBy('name')
            ->get();

        $categoryTypes = ['category', 'industry', 'technology', 'keyword'];

        return view('admin.categories.edit', [
            'pageTitle' => 'Edit Category',
            'breadcrumbItems' => $breadcrumbsItems,
            'category' => $category,
            'parentCategories' => $parentCategories,
            'categoryTypes' => $categoryTypes,
        ]);
    }

    /**
     * Update the specified category
     */
    public function update(Request $request, Taxonomy $category): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:taxonomies,name,' . $category->id,
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:category,industry,technology,keyword',
            'parent_id' => 'nullable|exists:taxonomies,id',
        ]);

        // Prevent circular references and self-parenting
        if ($validated['parent_id']) {
            if ($validated['parent_id'] == $category->id) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['parent_id' => 'A category cannot be its own parent.']);
            }

            $parent = Taxonomy::find($validated['parent_id']);
            if (!$parent || $parent->type !== 'category') {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['parent_id' => 'Invalid parent category selected.']);
            }

            // Check if the selected parent is a descendant of the current category
            if ($parent->isDescendantOf($category)) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['parent_id' => 'Cannot move category under its own descendant.']);
            }
        }

        // Update basic fields
        $category->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'type' => $validated['type'],
        ]);

        // Handle parent relationship changes
        if ($validated['parent_id'] != $category->parent_id) {
            $category->parent_id = $validated['parent_id'];
            $category->save();
        }

        return redirect()->route('admin.categories.index')
            ->with('message', 'Category updated successfully.');
    }

    /**
     * Remove the specified category
     */
    public function destroy(Taxonomy $category): RedirectResponse
    {
        // Check if category has associated users
        $associatedCount = $category->taxonomables()
            ->where('taxonomable_type', 'App\\Models\\User')
            ->count();

        if ($associatedCount > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'Cannot delete category that has associated users.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('message', 'Category deleted successfully.');
    }

    /**
     * Toggle category status (Note: Taxonomy model doesn't have is_active by default)
     */
    public function toggleStatus(Taxonomy $category): RedirectResponse
    {
        // Since the taxonomy model doesn't have is_active field by default,
        // we'll just return a message for now
        return redirect()->route('admin.categories.index')
            ->with('message', "Category status toggle not available for taxonomy model.");
    }
}
