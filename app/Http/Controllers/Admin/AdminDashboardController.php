<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Models\Refund;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard with key metrics and analytics
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => true
            ],
        ];

        // Key Metrics for KPI Cards
        $totalUsers = User::count();
        $activeSubscriptions = UserSubscription::where('status', 'active')->count();

        // User Growth Rate calculation
        $userGrowthRate = $this->calculateGrowthRate('users');

        // Churn Rate (cancelled subscriptions this month vs total active last month)
        $cancelledThisMonth = UserSubscription::where('status', 'cancelled')
            ->whereMonth('updated_at', Carbon::now()->month)
            ->count();

        $activeLastMonth = UserSubscription::where('status', 'active')
            ->whereMonth('created_at', '<=', Carbon::now()->subMonth()->endOfMonth())
            ->count();

        $churnRate = $activeLastMonth > 0 ? ($cancelledThisMonth / $activeLastMonth) * 100 : 0;

        // Recent registrations (last 24 hours)
        $recentRegistrations = User::where('created_at', '>=', Carbon::now()->subDay())
            ->count();

        // Role-based KPI Metrics - Investment platform specific metrics
        $totalInvestors = User::where('role', 'investor')->count();
        $totalStartups = User::where('role', 'startup')->count();
        $totalConsultants = User::where('role', 'analyst')->count(); // Using analyst as consultant
        // Check if Interest model exists, otherwise use placeholder values
        try {
            $totalInterests = \App\Models\Interest::count();
            $activeInterests = \App\Models\Interest::where('status', 'pending')->count();
        } catch (\Exception $e) {
            $totalInterests = 0;
            $activeInterests = 0;
        }

        // ESG Assessment completion rate (placeholder for future implementation)
        $usersWithESG = 0; // User::whereHas('esgAssessment')->count();
        $esgCompletionRate = $totalUsers > 0 ? ($usersWithESG / $totalUsers) * 100 : 0;

        // Document upload statistics (placeholder for future implementation)
        $usersWithDocuments = 0; // User::whereHas('documents')->count();
        $documentUploadRate = $totalUsers > 0 ? ($usersWithDocuments / $totalUsers) * 100 : 0;

        // Investor Metrics
        $activeInvestors = User::where('role', 'investor')
            ->whereHas('subscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyInvestors = User::where('role', 'investor')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyInvestors = User::where('role', 'investor')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Startup Metrics
        $activeStartups = User::where('role', 'startup')
            ->whereHas('subscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyStartups = User::where('role', 'startup')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyStartups = User::where('role', 'startup')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Consultant/Analyst Metrics
        $activeConsultants = User::where('role', 'analyst')
            ->whereHas('subscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyConsultants = User::where('role', 'analyst')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyConsultants = User::where('role', 'analyst')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Quick action items that need attention
        $pendingRefunds = Refund::where('status', 'pending')->count();
        $recentFailedPayments = Payment::where('status', 'failed')
            ->where('created_at', '>=', Carbon::now()->subWeek())
            ->count();

        $quickActions = [
            'pending_verifications' => User::where('email_verified_at', null)->count(),
            'locked_accounts' => User::whereHas('accountStatus', fn($q) => $q->where('is_locked', true))->count(),
            'pending_refunds' => $pendingRefunds,
            'failed_payments_week' => $recentFailedPayments,
            'inactive_subscriptions' => UserSubscription::where('status', 'past_due')->count(),
        ];

        // Platform activity metrics
        $platformActivity = [
            'new_registrations_today' => User::whereDate('created_at', Carbon::today())->count(),
            'new_subscriptions_today' => UserSubscription::whereDate('created_at', Carbon::today())->count(),
            'payments_today' => Payment::where('status', 'succeeded')->whereDate('created_at', Carbon::today())->count(),
            'interests_today' => class_exists('\App\Models\Interest') ? \App\Models\Interest::whereDate('created_at', Carbon::today())->count() : 0,
        ];

        return view('admin.dashboard.index', [
            'pageTitle' => 'Admin Dashboard',
            'breadcrumbItems' => $breadcrumbsItems,
            'metrics' => [
                // KPI Card metrics
                'totalUsers' => $totalUsers,
                'activeSubscriptions' => $activeSubscriptions,
                'userGrowthRate' => round($userGrowthRate, 2),
                'churnRate' => round($churnRate, 2),
                'recentRegistrations' => $recentRegistrations,
                // Investment Platform Overview metrics
                'totalInvestors' => $totalInvestors,
                'totalStartups' => $totalStartups,
                'totalConsultants' => $totalConsultants,
                'totalInterests' => $totalInterests,
                'activeInterests' => $activeInterests,
                'esgCompletionRate' => round($esgCompletionRate, 1),
                'documentUploadRate' => round($documentUploadRate, 1),
                // Role-based KPI metrics
                'activeInvestors' => $activeInvestors,
                'yearlyInvestors' => $yearlyInvestors,
                'monthlyInvestors' => $monthlyInvestors,
                'activeStartups' => $activeStartups,
                'yearlyStartups' => $yearlyStartups,
                'monthlyStartups' => $monthlyStartups,
                'activeConsultants' => $activeConsultants,
                'yearlyConsultants' => $yearlyConsultants,
                'monthlyConsultants' => $monthlyConsultants,
            ],
            'quickActions' => $quickActions,
            'platformActivity' => $platformActivity
        ]);
    }

    /**
     * Calculate growth rate for different metrics
     */
    private function calculateGrowthRate($type)
    {
        try {
            $currentMonth = Carbon::now();
            $previousMonth = Carbon::now()->subMonth();

            switch ($type) {
                case 'users':
                    $current = User::whereMonth('created_at', $currentMonth->month)
                        ->whereYear('created_at', $currentMonth->year)
                        ->count();
                    $previous = User::whereMonth('created_at', $previousMonth->month)
                        ->whereYear('created_at', $previousMonth->year)
                        ->count();
                    break;

                default:
                    return 0;
            }

            if ($previous == 0) {
                return $current > 0 ? 100 : 0;
            }

            return (($current - $previous) / $previous) * 100;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
