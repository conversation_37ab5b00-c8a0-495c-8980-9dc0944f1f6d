<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Models\SubscriptionProduct;
use App\Models\Invoice;
use App\Models\Refund;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard with key metrics and analytics
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => true
            ],
        ];

        // Key Metrics
        $totalUsers = User::count();
        $activeSubscriptions = UserSubscription::where('status', 'active')->count();

        // Recent Activity - Enhanced
        $recentUsers = User::latest()->take(5)->get();
        $recentSubscriptions = UserSubscription::with(['user', 'subscriptionProduct'])
            ->latest()
            ->take(5)
            ->get();
        $recentPayments = Payment::with(['user', 'userSubscription.subscriptionProduct'])
            ->where('status', 'succeeded')
            ->latest()
            ->take(5)
            ->get();

        // Recent registrations (last 24 hours)
        $recentRegistrations = User::where('created_at', '>=', Carbon::now()->subDay())
            ->count();

        // Recent subscription changes
        $recentSubscriptionChanges = UserSubscription::where('updated_at', '>=', Carbon::now()->subDay())
            ->with(['user', 'subscriptionProduct'])
            ->latest()
            ->take(10)
            ->get();

        // Failed payments in last 7 days
        $recentFailedPayments = Payment::where('status', 'failed')
            ->where('created_at', '>=', Carbon::now()->subWeek())
            ->with('user')
            ->latest()
            ->take(5)
            ->get();

        // Chart Data - Monthly Subscriptions for the last 12 months
        $monthlySubscriptionData = [];
        $monthLabels = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabels[] = $date->format('M Y');
            $subscriptions = UserSubscription::whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->count();
            $monthlySubscriptionData[] = $subscriptions;
        }

        // Enhanced Chart Data for Dashboard
        // Monthly Subscription Revenue (TK) for last 12 months
        $monthlyRevenueData = [];
        $monthlyRevenueLabels = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthlyRevenueLabels[] = $date->format('M Y');
            $revenue = Payment::where('status', 'succeeded')
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('amount');
            // Convert from cents to TK (assuming 1 USD = 110 TK approximately)
            $monthlyRevenueData[] = round(($revenue / 100) * 110, 2);
        }

        // Yearly Subscription Revenue (TK) for last 5 years
        $yearlyRevenueData = [];
        $yearlyRevenueLabels = [];
        for ($i = 4; $i >= 0; $i--) {
            $year = Carbon::now()->subYears($i)->year;
            $yearlyRevenueLabels[] = $year;
            $revenue = Payment::where('status', 'succeeded')
                ->whereYear('created_at', $year)
                ->sum('amount');
            // Convert from cents to TK
            $yearlyRevenueData[] = round(($revenue / 100) * 110, 2);
        }

        // Monthly Package Distribution by Role
        $currentMonth = Carbon::now();
        $monthlyPackageData = [
            'investor' => UserSubscription::whereHas('user', function($q) {
                    $q->where('role', 'investor');
                })
                ->whereMonth('created_at', $currentMonth->month)
                ->whereYear('created_at', $currentMonth->year)
                ->count(),
            'startup' => UserSubscription::whereHas('user', function($q) {
                    $q->where('role', 'startup');
                })
                ->whereMonth('created_at', $currentMonth->month)
                ->whereYear('created_at', $currentMonth->year)
                ->count(),
            'consultant' => UserSubscription::whereHas('user', function($q) {
                    $q->where('role', 'analyst');
                })
                ->whereMonth('created_at', $currentMonth->month)
                ->whereYear('created_at', $currentMonth->year)
                ->count(),
        ];

        // Yearly Package Distribution by Role
        $currentYear = Carbon::now()->year;
        $yearlyPackageData = [
            'investor' => UserSubscription::whereHas('user', function($q) {
                    $q->where('role', 'investor');
                })
                ->whereYear('created_at', $currentYear)
                ->count(),
            'startup' => UserSubscription::whereHas('user', function($q) {
                    $q->where('role', 'startup');
                })
                ->whereYear('created_at', $currentYear)
                ->count(),
            'consultant' => UserSubscription::whereHas('user', function($q) {
                    $q->where('role', 'analyst');
                })
                ->whereYear('created_at', $currentYear)
                ->count(),
        ];

        // Subscription Status Distribution
        $subscriptionStats = UserSubscription::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Prepare subscription status data for ApexCharts radial bar
        $subscriptionStatusLabels = [];
        $subscriptionStatusData = [];
        foreach ($subscriptionStats as $status => $count) {
            $subscriptionStatusLabels[] = ucfirst($status);
            $subscriptionStatusData[] = $count;
        }

        // Top Performing Products
        $topProducts = SubscriptionProduct::withCount('subscriptions')
            ->orderBy('subscriptions_count', 'desc')
            ->take(5)
            ->get();

        // Failed Payments Count
        $failedPayments = Payment::where('status', 'failed')
            ->whereMonth('created_at', Carbon::now()->month)
            ->count();

        // Churn Rate (cancelled subscriptions this month vs total active last month)
        $cancelledThisMonth = UserSubscription::where('status', 'cancelled')
            ->whereMonth('updated_at', Carbon::now()->month)
            ->count();

        $activeLastMonth = UserSubscription::where('status', 'active')
            ->whereMonth('created_at', '<=', Carbon::now()->subMonth()->endOfMonth())
            ->count();

        $churnRate = $activeLastMonth > 0 ? ($cancelledThisMonth / $activeLastMonth) * 100 : 0;

        // Additional comprehensive metrics
        $pendingRefunds = \App\Models\Refund::where('status', 'pending')->count();
        $totalRefunds = \App\Models\Refund::where('status', 'succeeded')->sum('amount') / 100;

        // User role distribution
        $userRoleStats = User::select('role', DB::raw('count(*) as count'))
            ->groupBy('role')
            ->pluck('count', 'role')
            ->toArray();

        // System health metrics
        $systemHealth = [
            'database_size' => $this->getDatabaseSize(),
            'active_sessions' => $this->getActiveSessions(),
            'server_uptime' => $this->getServerUptime(),
            'error_rate' => $this->getErrorRate(),
        ];

        // Growth metrics
        $userGrowthRate = $this->calculateGrowthRate('users');

        // Investment platform specific metrics
        $totalInvestors = User::where('role', 'investor')->count();
        $totalStartups = User::where('role', 'startup')->count();
        $totalConsultants = User::where('role', 'analyst')->count(); // Using analyst as consultant
        $totalInterests = \App\Models\Interest::count();
        $activeInterests = \App\Models\Interest::where('status', 'pending')->count();

        // Role-based KPI Metrics
        // Investor Metrics
        $activeInvestors = User::where('role', 'investor')
            ->whereHas('userSubscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyInvestors = User::where('role', 'investor')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyInvestors = User::where('role', 'investor')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Startup Metrics
        $activeStartups = User::where('role', 'startup')
            ->whereHas('userSubscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyStartups = User::where('role', 'startup')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyStartups = User::where('role', 'startup')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Consultant/Analyst Metrics
        $activeConsultants = User::where('role', 'analyst')
            ->whereHas('userSubscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyConsultants = User::where('role', 'analyst')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyConsultants = User::where('role', 'analyst')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // ESG Assessment completion rate (placeholder for future implementation)
        $usersWithESG = 0; // User::whereHas('esgAssessment')->count();
        $esgCompletionRate = $totalUsers > 0 ? ($usersWithESG / $totalUsers) * 100 : 0;

        // Document upload statistics (placeholder for future implementation)
        $usersWithDocuments = 0; // User::whereHas('documents')->count();
        $documentUploadRate = $totalUsers > 0 ? ($usersWithDocuments / $totalUsers) * 100 : 0;

        // Quick action items that need attention
        $quickActions = [
            'pending_verifications' => User::where('email_verified_at', null)->count(),
            'locked_accounts' => User::whereHas('accountStatus',fn($q)=>$q->where('is_locked', true))->count(),
            'pending_refunds' => $pendingRefunds,
            'failed_payments_week' => $recentFailedPayments->count(),
            'inactive_subscriptions' => UserSubscription::where('status', 'past_due')->count(),
        ];

        // Platform activity metrics
        $platformActivity = [
            'new_registrations_today' => User::whereDate('created_at', Carbon::today())->count(),
            'new_subscriptions_today' => UserSubscription::whereDate('created_at', Carbon::today())->count(),
            'payments_today' => Payment::where('status', 'succeeded')->whereDate('created_at', Carbon::today())->count(),
            'interests_today' => \App\Models\Interest::whereDate('created_at', Carbon::today())->count(),
        ];

        return view('admin.dashboard.index', [
            'pageTitle' => 'Admin Dashboard',
            'breadcrumbItems' => $breadcrumbsItems,
            'metrics' => [
                'totalUsers' => $totalUsers,
                'activeSubscriptions' => $activeSubscriptions,
                'failedPayments' => $failedPayments,
                'churnRate' => round($churnRate, 2),
                'pendingRefunds' => $pendingRefunds,
                'totalRefunds' => $totalRefunds,
                'userGrowthRate' => round($userGrowthRate, 2),
                'totalInvestors' => $totalInvestors,
                'totalStartups' => $totalStartups,
                'totalConsultants' => $totalConsultants,
                'totalInterests' => $totalInterests,
                'activeInterests' => $activeInterests,
                'esgCompletionRate' => round($esgCompletionRate, 1),
                'documentUploadRate' => round($documentUploadRate, 1),
                'recentRegistrations' => $recentRegistrations,
                // Role-based KPI metrics
                'activeInvestors' => $activeInvestors,
                'yearlyInvestors' => $yearlyInvestors,
                'monthlyInvestors' => $monthlyInvestors,
                'activeStartups' => $activeStartups,
                'yearlyStartups' => $yearlyStartups,
                'monthlyStartups' => $monthlyStartups,
                'activeConsultants' => $activeConsultants,
                'yearlyConsultants' => $yearlyConsultants,
                'monthlyConsultants' => $monthlyConsultants,
            ],
            'chartData' => [
                'monthlySubscriptions' => [
                    'labels' => $monthLabels,
                    'data' => $monthlySubscriptionData
                ],
                'subscriptionStats' => $subscriptionStats,
                'subscriptionStatus' => [
                    'labels' => $subscriptionStatusLabels,
                    'data' => $subscriptionStatusData
                ],
                'userRoleStats' => $userRoleStats,
                // New enhanced chart data
                'monthlyRevenue' => [
                    'labels' => $monthlyRevenueLabels,
                    'data' => $monthlyRevenueData
                ],
                'yearlyRevenue' => [
                    'labels' => $yearlyRevenueLabels,
                    'data' => $yearlyRevenueData
                ],
                'monthlyPackageDistribution' => $monthlyPackageData,
                'yearlyPackageDistribution' => $yearlyPackageData
            ],
            'recentActivity' => [
                'users' => $recentUsers,
                'subscriptions' => $recentSubscriptions,
                'payments' => $recentPayments,
                'subscriptionChanges' => $recentSubscriptionChanges,
                'failedPayments' => $recentFailedPayments
            ],
            'topProducts' => $topProducts,
            'systemHealth' => $systemHealth,
            'quickActions' => $quickActions,
            'platformActivity' => $platformActivity
        ]);
    }

    /**
     * Get database size (simplified)
     */
    private function getDatabaseSize()
    {
        try {
            $result = DB::select("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size' FROM information_schema.tables WHERE table_schema = DATABASE()");
            return $result[0]->size ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get active sessions count (simplified)
     */
    private function getActiveSessions()
    {
        try {
            // This is a simplified version - in production you might use Redis or database sessions
            return rand(10, 50); // Placeholder
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get server uptime (simplified)
     */
    private function getServerUptime()
    {
        try {
            return '99.9%'; // Placeholder - in production you'd calculate actual uptime
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    /**
     * Get error rate (simplified)
     */
    private function getErrorRate()
    {
        try {
            return rand(0, 5) / 10; // Placeholder - in production you'd check logs
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Calculate growth rate for different metrics
     */
    private function calculateGrowthRate($type)
    {
        try {
            $currentMonth = Carbon::now();
            $previousMonth = Carbon::now()->subMonth();

            switch ($type) {
                case 'users':
                    $current = User::whereMonth('created_at', $currentMonth->month)
                        ->whereYear('created_at', $currentMonth->year)
                        ->count();
                    $previous = User::whereMonth('created_at', $previousMonth->month)
                        ->whereYear('created_at', $previousMonth->year)
                        ->count();
                    break;

                default:
                    return 0;
            }

            if ($previous == 0) {
                return $current > 0 ? 100 : 0;
            }

            return (($current - $previous) / $previous) * 100;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
