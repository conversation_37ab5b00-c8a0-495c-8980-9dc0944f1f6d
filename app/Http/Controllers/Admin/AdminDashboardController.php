<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard with key metrics and analytics
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => true
            ],
        ];

        // Role-based KPI Metrics - Investment platform specific metrics
        $totalInvestors = User::where('role', 'investor')->count();
        $totalStartups = User::where('role', 'startup')->count();
        $totalConsultants = User::where('role', 'analyst')->count(); // Using analyst as consultant

        // Investor Metrics
        $activeInvestors = User::where('role', 'investor')
            ->whereHas('subscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyInvestors = User::where('role', 'investor')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyInvestors = User::where('role', 'investor')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Startup Metrics
        $activeStartups = User::where('role', 'startup')
            ->whereHas('subscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyStartups = User::where('role', 'startup')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyStartups = User::where('role', 'startup')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Consultant/Analyst Metrics
        $activeConsultants = User::where('role', 'analyst')
            ->whereHas('subscriptions', function($q) {
                $q->where('status', 'active');
            })->count();
        $yearlyConsultants = User::where('role', 'analyst')
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        $monthlyConsultants = User::where('role', 'analyst')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        return view('admin.dashboard.index', [
            'pageTitle' => 'Admin Dashboard',
            'breadcrumbItems' => $breadcrumbsItems,
            'metrics' => [
                'totalInvestors' => $totalInvestors,
                'totalStartups' => $totalStartups,
                'totalConsultants' => $totalConsultants,
                // Role-based KPI metrics
                'activeInvestors' => $activeInvestors,
                'yearlyInvestors' => $yearlyInvestors,
                'monthlyInvestors' => $monthlyInvestors,
                'activeStartups' => $activeStartups,
                'yearlyStartups' => $yearlyStartups,
                'monthlyStartups' => $monthlyStartups,
                'activeConsultants' => $activeConsultants,
                'yearlyConsultants' => $yearlyConsultants,
                'monthlyConsultants' => $monthlyConsultants,
            ]
        ]);
    }
}
