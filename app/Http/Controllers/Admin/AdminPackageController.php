<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Package;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\QueryBuilder;

class AdminPackageController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Display a listing of packages
     */
    public function index(Request $request): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Packages',
                'url' => route('admin.packages.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $status = $request->get('status');
        $role = $request->get('role');
        $perPage = $request->get('per_page', 10);

        $packages = QueryBuilder::for(Package::class)
            ->allowedFilters(['name', 'target_role'])
            ->withCount('packagePurchases')
            ->when($q, function ($query) use ($q) {
                $query->where('name', 'like', "%{$q}%")
                    ->orWhere('description', 'like', "%{$q}%");
            })
            ->when($status !== null, function ($query) use ($status) {
                $query->where('is_active', $status === 'active');
            })
            ->when($role, function ($query) use ($role) {
                $query->where('target_role', $role);
            })
            ->orderBy('sort_order')
            ->latest()
            ->paginate($perPage);

        return view('admin.packages.index', [
            'pageTitle' => 'Package Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'packages' => $packages,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'role' => $role,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Show the form for creating a new package
     */
    public function create(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Packages',
                'url' => route('admin.packages.index'),
                'active' => false
            ],
            [
                'name' => 'Create Package',
                'url' => route('admin.packages.create'),
                'active' => true
            ],
        ];

        return view('admin.packages.create', [
            'pageTitle' => 'Create Package',
            'breadcrumbItems' => $breadcrumbsItems,
        ]);
    }

    /**
     * Store a newly created package
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'features' => 'nullable|array',
            'target_role' => 'required|in:investor,startup,analyst,all',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        try {
            // Create product in Stripe first
            $stripeProduct = $this->stripeService->createProduct([
                'name' => $request->name,
                'description' => $request->description ?? null,
            ]);

            // Create price in Stripe (one-time payment)
            $stripePrice = $this->stripeService->createPrice([
                'product' => $stripeProduct->id,
                'unit_amount' => $request->price * 100, // Convert to cents
                'currency' => 'usd',
                // No recurring for one-time payments
            ]);

            // Create local package
            Package::create([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'features' => $request->features ?? [],
                'target_role' => $request->target_role,
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->sort_order ?? 0,
                'stripe_product_id' => $stripeProduct->id,
                'stripe_price_id' => $stripePrice->id,
            ]);

            return redirect()->route('admin.packages.index')
                ->with('message', 'Package created successfully.');

        } catch (\Exception $e) {
            Log::error("Failed to create package: " . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create package: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing a package
     */
    public function edit(Package $package): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Packages',
                'url' => route('admin.packages.index'),
                'active' => false
            ],
            [
                'name' => 'Edit Package',
                'url' => route('admin.packages.edit', $package),
                'active' => true
            ],
        ];

        return view('admin.packages.edit', [
            'pageTitle' => 'Edit Package',
            'breadcrumbItems' => $breadcrumbsItems,
            'package' => $package,
        ]);
    }

    /**
     * Update the specified package
     */
    public function update(Request $request, Package $package): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'features' => 'nullable|array',
            'target_role' => 'required|in:investor,startup,analyst,all',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        try {
            // Update Stripe product
            if ($package->stripe_product_id) {
                $this->stripeService->updateProduct($package->stripe_product_id, [
                    'name' => $request->name,
                    'description' => $request->description ?? null,
                ]);
            }

            // If price changed, create new Stripe price
            if ($package->price != $request->price) {
                $stripePrice = $this->stripeService->createPrice([
                    'product' => $package->stripe_product_id,
                    'unit_amount' => $request->price * 100, // Convert to cents
                    'currency' => 'usd',
                ]);

                $package->stripe_price_id = $stripePrice->id;
            }

            // Update local package
            $package->update([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'features' => $request->features ?? [],
                'target_role' => $request->target_role,
                'is_active' => $request->boolean('is_active', $package->is_active),
                'sort_order' => $request->sort_order ?? $package->sort_order,
            ]);

            return redirect()->route('admin.packages.index')
                ->with('message', 'Package updated successfully.');

        } catch (\Exception $e) {
            Log::error("Failed to update package: " . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update package: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified package
     */
    public function destroy(Package $package): RedirectResponse
    {
        try {
            // Check if package has completed purchases
            if ($package->packagePurchases()->where('status', 'completed')->exists()) {
                return redirect()->back()
                    ->with('error', 'Cannot delete package with completed purchases.');
            }

            // Archive product in Stripe instead of deleting
            if ($package->stripe_product_id) {
                $this->stripeService->updateProduct($package->stripe_product_id, [
                    'active' => false
                ]);
            }

            // Soft delete or mark as inactive
            $package->update(['is_active' => false]);

            return redirect()->route('admin.packages.index')
                ->with('message', 'Package deactivated successfully.');

        } catch (\Exception $e) {
            Log::error("Failed to delete package: " . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Failed to delete package: ' . $e->getMessage());
        }
    }

    /**
     * Toggle package status
     */
    public function toggleStatus(Package $package): RedirectResponse
    {
        try {
            $newStatus = !$package->is_active;

            // Update Stripe product status
            if ($package->stripe_product_id) {
                $this->stripeService->updateProduct($package->stripe_product_id, [
                    'active' => $newStatus
                ]);
            }

            $package->update(['is_active' => $newStatus]);

            $statusText = $newStatus ? 'activated' : 'deactivated';
            return redirect()->back()
                ->with('message', "Package {$statusText} successfully.");

        } catch (\Exception $e) {
            Log::error("Failed to toggle package status: " . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Failed to update package status: ' . $e->getMessage());
        }
    }
}
