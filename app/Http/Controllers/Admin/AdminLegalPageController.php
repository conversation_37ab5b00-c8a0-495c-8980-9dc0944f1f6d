<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LegalPage;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class AdminLegalPageController extends Controller
{
    /**
     * Display a listing of legal pages
     */
    public function index(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Legal Pages',
                'url' => route('admin.legal-pages.index'),
                'active' => true
            ],
        ];

        $legalPages = LegalPage::with('updatedBy')
            ->orderBy('type')
            ->get();

        return view('admin.legal-pages.index', [
            'pageTitle' => 'Legal Pages Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'legalPages' => $legalPages,
        ]);
    }

    /**
     * Show the form for creating a new legal page
     */
    public function create(): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Legal Pages',
                'url' => route('admin.legal-pages.index'),
                'active' => false
            ],
            [
                'name' => 'Create Legal Page',
                'url' => route('admin.legal-pages.create'),
                'active' => true
            ],
        ];

        $availableTypes = [
            'terms-and-conditions' => 'Terms & Conditions',
            'privacy-policy' => 'Privacy Policy',
        ];

        // Remove types that already exist
        $existingTypes = LegalPage::pluck('type')->toArray();
        $availableTypes = array_diff_key($availableTypes, array_flip($existingTypes));

        return view('admin.legal-pages.create', [
            'pageTitle' => 'Create Legal Page',
            'breadcrumbItems' => $breadcrumbsItems,
            'availableTypes' => $availableTypes,
        ]);
    }

    /**
     * Store a newly created legal page
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'type' => 'required|string|in:terms-and-conditions,privacy-policy|unique:legal_pages,type',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'is_active' => 'boolean',
        ]);

        try {
            $legalPage = LegalPage::create([
                'type' => $request->type,
                'title' => $request->title,
                'content' => $request->content,
                'is_active' => $request->boolean('is_active', true),
                'last_updated_at' => now(),
                'updated_by' => auth()->id(),
            ]);

            return redirect()->route('admin.legal-pages.index')
                ->with('message', 'Legal page created successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create legal page: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified legal page
     */
    public function edit(LegalPage $legalPage): View
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'Legal Pages',
                'url' => route('admin.legal-pages.index'),
                'active' => false
            ],
            [
                'name' => 'Edit ' . ucwords(str_replace('-', ' ', $legalPage->type)),
                'url' => route('admin.legal-pages.edit', $legalPage),
                'active' => true
            ],
        ];

        return view('admin.legal-pages.edit', [
            'pageTitle' => 'Edit ' . ucwords(str_replace('-', ' ', $legalPage->type)),
            'breadcrumbItems' => $breadcrumbsItems,
            'legalPage' => $legalPage,
        ]);
    }

    /**
     * Update the specified legal page
     */
    public function update(Request $request, LegalPage $legalPage): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'is_active' => 'boolean',
        ]);

        try {
            $legalPage->update([
                'title' => $request->title,
                'content' => $request->content,
                'is_active' => $request->boolean('is_active', true),
                'last_updated_at' => now(),
                'updated_by' => auth()->id(),
            ]);

            return redirect()->route('admin.legal-pages.index')
                ->with('message', 'Legal page updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update legal page: ' . $e->getMessage());
        }
    }

    /**
     * Toggle the active status of the legal page
     */
    public function toggleStatus(LegalPage $legalPage): RedirectResponse
    {
        try {
            $legalPage->update([
                'is_active' => !$legalPage->is_active,
                'last_updated_at' => now(),
                'updated_by' => auth()->id(),
            ]);

            $status = $legalPage->is_active ? 'activated' : 'deactivated';
            return redirect()->back()
                ->with('message', "Legal page {$status} successfully.");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update legal page status: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified legal page
     */
    public function destroy(LegalPage $legalPage): RedirectResponse
    {
        try {
            $legalPage->delete();

            return redirect()->route('admin.legal-pages.index')
                ->with('message', 'Legal page deleted successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete legal page: ' . $e->getMessage());
        }
    }
}
