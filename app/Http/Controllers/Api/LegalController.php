<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LegalPage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class LegalController extends Controller
{
    /**
     * Get terms and conditions
     */
    public function termsAndConditions(): JsonResponse
    {
        try {
            $termsPage = LegalPage::getTermsAndConditions();

            if (!$termsPage) {
                return $this->responseWithError(
                    'Terms and conditions not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->responseWithSuccess(
                'Terms and conditions retrieved successfully',
                [
                    'title' => $termsPage->title,
                    'content' => $termsPage->content,
                    'last_updated' => $termsPage->last_updated_at?->toISOString(),
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve terms and conditions: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve terms and conditions',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get privacy policy
     */
    public function privacyPolicy(): JsonResponse
    {
        try {
            $privacyPage = LegalPage::getPrivacyPolicy();

            if (!$privacyPage) {
                return $this->responseWithError(
                    'Privacy policy not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->responseWithSuccess(
                'Privacy policy retrieved successfully',
                [
                    'title' => $privacyPage->title,
                    'content' => $privacyPage->content,
                    'last_updated' => $privacyPage->last_updated_at?->toISOString(),
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve privacy policy: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve privacy policy',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get all legal pages
     */
    public function index(): JsonResponse
    {
        try {
            $legalPages = LegalPage::active()
                ->orderBy('type')
                ->get()
                ->map(function ($page) {
                    return [
                        'type' => $page->type,
                        'title' => $page->title,
                        'content' => $page->content,
                        'last_updated' => $page->last_updated_at?->toISOString(),
                    ];
                });

            return $this->responseWithSuccess(
                'Legal pages retrieved successfully',
                $legalPages
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve legal pages: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve legal pages',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
