<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;

class ContactController extends Controller
{
    /**
     * Submit contact form
     */
    public function store(Request $request): JsonResponse
    {
        // Rate limiting - 5 submissions per hour per IP
        $key = 'contact-form:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            return $this->responseWithError(
                'Too many contact form submissions. Please try again later.',
                Response::HTTP_TOO_MANY_REQUESTS
            );
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        try {
            // Create contact submission
            $contact = ContactSubmission::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'subject' => $request->subject,
                'message' => $request->message,
                'status' => 'new',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Increment rate limiter
            RateLimiter::hit($key, 3600); // 1 hour

            // Send email notification to admin (optional - implement if needed)
            // $this->sendAdminNotification($contact);

            return $this->responseWithSuccess(
                'Contact form submitted successfully. We will get back to you soon.',
                [
                    'submission_id' => $contact->id,
                    'submitted_at' => $contact->created_at->toISOString(),
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to submit contact form: ' . $e->getMessage(), [
                'request_data' => $request->only(['name', 'email', 'subject']),
                'ip' => $request->ip(),
            ]);

            return $this->responseWithError(
                'Failed to submit contact form. Please try again.',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Send admin notification email (implement if needed)
     */
    private function sendAdminNotification(ContactSubmission $contact): void
    {
        try {
            // Implement email notification to admin
            // Mail::to(config('mail.admin_email'))->send(new ContactFormSubmitted($contact));
        } catch (\Exception $e) {
            Log::warning('Failed to send admin notification for contact form: ' . $e->getMessage());
        }
    }
}
