<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Package;
use App\Models\PackagePurchase;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PackageController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->middleware(['auth:sanctum', 'check.account.locked']);
    }

    /**
     * Display a listing of packages available to the current user's role
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = $request->get('per_page', 10);

            $query = Package::active()
                ->where(function ($q) use ($user) {
                    $q->where('target_role', $user->role)
                      ->orWhere('target_role', 'all');
                })
                ->orderBy('sort_order')
                ->orderBy('name');

            $packages = $query->paginate($perPage);

            return $this->responseWithSuccess(
                'Packages retrieved successfully',
                $packages
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve packages: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve packages',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified package
     */
    public function show(Package $package): JsonResponse
    {
        try {
            $user = Auth::user();

            // Check if package is available for user's role
            if ($package->target_role !== 'all' && $package->target_role !== $user->role) {
                return $this->responseWithError(
                    'Package not available for your role',
                    Response::HTTP_FORBIDDEN
                );
            }

            if (!$package->is_active) {
                return $this->responseWithError(
                    'Package is not available',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Include purchase history for this user
            $package->load(['packagePurchases' => function ($query) use ($user) {
                $query->where('user_id', $user->id)->latest();
            }]);

            return $this->responseWithSuccess(
                'Package retrieved successfully',
                $package
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve package: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve package',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Purchase a package
     */
    public function purchase(Request $request, Package $package): JsonResponse
    {
        $request->validate([
            'success_url' => 'nullable|url',
            'cancel_url' => 'nullable|url',
        ]);

        try {
            $user = Auth::user();

            // Check if package is available for user's role
            if ($package->target_role !== 'all' && $package->target_role !== $user->role) {
                return $this->responseWithError(
                    'Package not available for your role',
                    Response::HTTP_FORBIDDEN
                );
            }

            if (!$package->is_active) {
                return $this->responseWithError(
                    'Package is not available',
                    Response::HTTP_NOT_FOUND
                );
            }

            DB::beginTransaction();

            // Create or get Stripe customer
            $customer = $this->stripeService->createOrGetCustomer($user);

            // Create package purchase record
            $packagePurchase = PackagePurchase::create([
                'user_id' => $user->id,
                'package_id' => $package->id,
                'amount' => $package->price,
                'status' => 'pending',
                'metadata' => [
                    'package_name' => $package->name,
                    'user_role' => $user->role,
                    'created_via' => 'api_purchase',
                ],
            ]);

            // Create Stripe Checkout Session for one-time payment
            $successUrl = $request->get('success_url', config('app.frontend_url', 'http://localhost:5175') . '/app/packages/purchase-success');
            $cancelUrl = $request->get('cancel_url', config('app.frontend_url', 'http://localhost:5175') . '/app/packages');

            $session = $this->stripeService->createOneTimeCheckoutSession(
                $customer->id,
                $package->stripe_price_id,
                $successUrl . '?session_id={CHECKOUT_SESSION_ID}',
                $cancelUrl,
                [
                    'user_id' => $user->id,
                    'package_id' => $package->id,
                    'package_purchase_id' => $packagePurchase->id,
                    'created_via' => 'package_purchase',
                ]
            );

            // Update package purchase with Stripe payment intent ID
            $packagePurchase->update([
                'stripe_payment_intent_id' => $session->payment_intent,
            ]);

            DB::commit();

            return $this->responseWithSuccess(
                'Package purchase initiated successfully',
                [
                    'checkout_url' => $session->url,
                    'session_id' => $session->id,
                    'package_purchase_id' => $packagePurchase->id,
                    'package' => $package,
                    'requires_redirect' => true,
                ]
            );

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to initiate package purchase: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to initiate package purchase: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get user's package purchase history
     */
    public function purchaseHistory(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = $request->get('per_page', 10);
            $status = $request->get('status');

            $query = PackagePurchase::where('user_id', $user->id)
                ->with('package')
                ->latest();

            if ($status) {
                $query->where('status', $status);
            }

            $purchases = $query->paginate($perPage);

            return $this->responseWithSuccess(
                'Purchase history retrieved successfully',
                $purchases
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve purchase history: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve purchase history',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }



    // Response helper methods are inherited from the ApiResponse trait
}
