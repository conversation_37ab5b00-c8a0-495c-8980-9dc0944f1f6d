<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserAccountStatus;
use App\Models\SocialMediaLink;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Password;

class StartupRegistrationController extends Controller
{
    /**
     * Enhanced startup registration with social media links
     */
    public function register(Request $request): JsonResponse
    {
        $validated = $request->validate([
            // Required fields
            'name' => ['required', 'string', 'max:255', 'min:2'],
            'email' => ['required', 'email', 'unique:users,email', 'max:255'],
            'password' => [
                'required',
                app()->isLocal() ? 'min:3' : Password::min(8)->mixedCase()->numbers()->symbols(), 'confirmed'],
            'password_confirmation' => ['required'],

            // Optional social media fields
            'linkedin_url' => ['nullable', 'url', 'max:255', 'regex:/^https?:\/\/(www\.)?linkedin\.com\/.*$/'],
            'twitter_handle' => ['nullable', 'string', 'max:50', 'regex:/^@?[A-Za-z0-9_]+$/'],
            'facebook_url' => ['nullable', 'url', 'max:255', 'regex:/^https?:\/\/(www\.)?facebook\.com\/.*$/'],
            'personal_website' => ['nullable', 'url', 'max:255'],

            // Contact information
            'phone' => ['required', 'string', 'max:20'],
            'city' => ['nullable', 'string', 'max:255'],
            'country' => ['nullable', 'string', 'max:255'],

            // Referral tracking
            'referral_source' => ['nullable', 'string', 'max:100'],
        ]);

        try {
            DB::beginTransaction();

            // Create user
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => 'startup',
                'phone' => $validated['phone'] ?? null,
                'city' => $validated['city'] ?? null,
                'country' => $validated['country'] ?? null,
                'referral_source' => $validated['referral_source'] ?? null,
                'email_verified_at' => app()->isLocal() ? now() : null,
            ]);

            // Create account status record
            UserAccountStatus::create([
                'user_id' => $user->id,
                'status' => 'active',
            ]);

            // Create social media links if provided
            $this->createSocialMediaLinks($user, $validated);

            DB::commit();

            // Send email verification if not in local environment
            if (!app()->isLocal()) {
                $user->sendEmailVerificationNotification();
            }

            // Generate API token
            $token = $user->createToken('startup-registration')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Startup registration successful. Please verify your email address.',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'email_verified_at' => $user->email_verified_at,
                        'phone' => $user->phone,
                        'city' => $user->city,
                        'country' => $user->country,
                        'referral_source' => $user->referral_source,
                    ],
                    'token' => $token,
                    'next_steps' => [
                        'email_verification' => !$user->email_verified_at,
                        'company_information' => true,
                        'funding_information' => true,
                        'category_selection' => true,
                        'subscription_required' => true,
                        'esg_questionnaire' => true,
                    ],
                ],
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Startup registration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $validated
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Registration failed. Please try again.',
                'error' => app()->isLocal() ? $e->getMessage() : 'Internal server error',
                'debug' => app()->isLocal() ? [
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'trace' => $e->getTraceAsString()
                ] : null,
            ], 500);
        }
    }

    /**
     * Get registration progress for authenticated startup user
     */
    public function getRegistrationProgress(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user->role !== 'startup') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Startup role required.',
            ], 403);
        }

        $startupProfile = $user->startupProfile;
        $socialMediaLinks = $user->socialMediaLinks;
        $categories = $startupProfile ? $startupProfile->taxonomies()->where('type', 'category')->count() : 0;
        $esgCompleted = $startupProfile?->esg_completed ?? false;
        $hasActiveSubscription = $user->subscriptions()->active()->exists();

        $progress = [
            'email_verification' => [
                'completed' => (bool) $user->email_verified_at,
                'required' => true,
                'step' => 1,
            ],
            'company_information' => [
                'completed' => $startupProfile && $startupProfile->profile_completed,
                'required' => true,
                'step' => 2,
                'completion_percentage' => $startupProfile?->profile_completion_percentage ?? 0,
            ],
            'funding_information' => [
                'completed' => $startupProfile &&
                    $startupProfile->funding_stage &&
                    $startupProfile->funding_amount_sought,
                'required' => true,
                'step' => 3,
            ],
            'category_selection' => [
                'completed' => $categories >= 3,
                'required' => true,
                'step' => 4,
                'selected_count' => $categories,
                'minimum_required' => 3,
                'maximum_allowed' => 5,
            ],
            'subscription' => [
                'completed' => $hasActiveSubscription,
                'required' => true,
                'step' => 5,
            ],
            'esg_questionnaire' => [
                'completed' => $esgCompleted,
                'required' => true,
                'step' => 6,
                'esg_score' => $startupProfile?->esg_score,
            ],
            'social_media_links' => [
                'completed' => $socialMediaLinks->count() > 0,
                'required' => false,
                'step' => 7,
                'links_count' => $socialMediaLinks->count(),
            ],
        ];

        $overallCompletion = collect($progress)
            ->filter(fn($step) => $step['required'])
            ->avg(fn($step) => $step['completed'] ? 100 : 0);

        return response()->json([
            'success' => true,
            'data' => [
                'overall_completion_percentage' => round($overallCompletion, 2),
                'steps' => $progress,
                'can_access_platform' => $progress['subscription']['completed'] &&
                    $progress['company_information']['completed'] &&
                    $progress['category_selection']['completed'],
            ],
        ]);
    }

    /**
     * Create social media links for user
     */
    private function createSocialMediaLinks(User $user, array $data): void
    {
        $socialMediaMappings = [
            'linkedin_url' => 'linkedin',
            'facebook_url' => 'facebook',
            'personal_website' => 'website',
        ];

        foreach ($socialMediaMappings as $field => $platform) {
            if (!empty($data[$field])) {
                SocialMediaLink::create([
                    'linkable_type' => User::class,
                    'linkable_id' => $user->id,
                    'platform' => $platform,
                    'url' => $data[$field],
                    'is_primary' => true,
                    'is_public' => true,
                ]);
            }
        }

        // Handle Twitter separately to extract username
        if (!empty($data['twitter_handle'])) {
            $username = ltrim($data['twitter_handle'], '@');
            SocialMediaLink::create([
                'linkable_type' => User::class,
                'linkable_id' => $user->id,
                'platform' => 'twitter',
                'url' => "https://twitter.com/{$username}",
                'username' => $username,
                'is_primary' => true,
                'is_public' => true,
            ]);
        }
    }
}
