<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Models\PackagePurchase;
use App\Models\User;
use App\Services\StripeService;
use App\Services\NotificationService;
use App\Services\InvoiceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Symfony\Component\HttpFoundation\Response;

class StripeWebhookController extends Controller
{
    protected StripeService $stripeService;
    protected NotificationService $notificationService;
    protected InvoiceService $invoiceService;

    public function __construct(StripeService $stripeService, NotificationService $notificationService, InvoiceService $invoiceService)
    {
        $this->stripeService = $stripeService;
        $this->notificationService = $notificationService;
        $this->invoiceService = $invoiceService;
    }

    /**
     * Handle Stripe webhook events
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        Log::info('Stripe webhook received:', [
            'request_data' => $request->all()
        ]);

        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook.secret');

        try {
            // Verify webhook signature
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            Log::error('Invalid payload in Stripe webhook', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Invalid payload'], Response::HTTP_BAD_REQUEST);
        } catch (SignatureVerificationException $e) {
            Log::error('Invalid signature in Stripe webhook', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Invalid signature'], Response::HTTP_BAD_REQUEST);
        }

        Log::info('Stripe webhook received', [
            'event_type' => $event->type,
            'event_id' => $event->id
        ]);

        try {
            // Handle the event
            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $this->handlePaymentIntentSucceeded($event->data->object);
                    break;

                case 'payment_intent.payment_failed':
                    $this->handlePaymentIntentFailed($event->data->object);
                    break;

                case 'payment_intent.requires_action':
                    $this->handlePaymentIntentRequiresAction($event->data->object);
                    break;

                case 'invoice.payment_succeeded':
                    $this->handleInvoicePaymentSucceeded($event->data->object);
                    break;

                case 'invoice.payment_failed':
                    $this->handleInvoicePaymentFailed($event->data->object);
                    break;

                case 'customer.subscription.created':
                    $this->handleSubscriptionCreated($event->data->object);
                    break;

                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event->data->object);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event->data->object);
                    break;

                case 'invoice.created':
                    $this->handleInvoiceCreated($event->data->object);
                    break;

                case 'invoice.finalized':
                    $this->handleInvoiceFinalized($event->data->object);
                    break;

                case 'invoice.updated':
                    $this->handleInvoiceUpdated($event->data->object);
                    break;

                default:
                    Log::info('Unhandled Stripe webhook event', ['event_type' => $event->type]);
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::error('Error processing Stripe webhook', [
                'event_type' => $event->type,
                'event_id' => $event->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Handle successful payment intent
     */
    protected function handlePaymentIntentSucceeded($paymentIntent): void
    {
        // Handle regular subscription payments
        $payment = Payment::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->update([
                'status' => 'succeeded',
                'paid_at' => now(),
                'payment_method_type' => $paymentIntent->charges->data[0]->payment_method_details->type ?? null,
                'requires_3d_secure' => false,
                '3d_secure_url' => null,
            ]);

            // Send payment success notification
            $this->notificationService->sendPaymentSucceededNotification($payment->user, $payment);

            Log::info('Payment succeeded', [
                'payment_id' => $payment->id,
                'stripe_payment_intent_id' => $paymentIntent->id
            ]);
        }

        // Handle package purchases
        $packagePurchase = PackagePurchase::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($packagePurchase) {
            $packagePurchase->markAsCompleted();

            Log::info('Package purchase completed', [
                'package_purchase_id' => $packagePurchase->id,
                'stripe_payment_intent_id' => $paymentIntent->id,
                'user_id' => $packagePurchase->user_id,
                'package_id' => $packagePurchase->package_id
            ]);

            // TODO: Send package purchase success notification
            // $this->notificationService->sendPackagePurchaseSucceededNotification($packagePurchase->user, $packagePurchase);
        }
    }

    /**
     * Handle failed payment intent
     */
    protected function handlePaymentIntentFailed($paymentIntent): void
    {
        $payment = Payment::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'failure_code' => $paymentIntent->last_payment_error->code ?? null,
                'failure_message' => $paymentIntent->last_payment_error->message ?? 'Payment failed',
                'requires_3d_secure' => false,
                '3d_secure_url' => null,
            ]);

            // Send payment failed notification
            $failureReason = $payment->failure_message ?? 'Payment failed';
            $this->notificationService->sendPaymentFailedNotification($payment->user, $payment, $failureReason);

            Log::warning('Payment failed', [
                'payment_id' => $payment->id,
                'stripe_payment_intent_id' => $paymentIntent->id,
                'failure_code' => $payment->failure_code,
                'failure_message' => $payment->failure_message
            ]);
        }

        // Handle package purchase failures
        $packagePurchase = PackagePurchase::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($packagePurchase) {
            $packagePurchase->markAsFailed();

            Log::warning('Package purchase failed', [
                'package_purchase_id' => $packagePurchase->id,
                'stripe_payment_intent_id' => $paymentIntent->id,
                'user_id' => $packagePurchase->user_id,
                'package_id' => $packagePurchase->package_id,
                'failure_reason' => $paymentIntent->last_payment_error->message ?? 'Payment failed'
            ]);

            // TODO: Send package purchase failed notification
            // $this->notificationService->sendPackagePurchaseFailedNotification($packagePurchase->user, $packagePurchase);
        }
    }

    /**
     * Handle payment intent requiring action (3D Secure)
     */
    protected function handlePaymentIntentRequiresAction($paymentIntent): void
    {
        $payment = Payment::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->update([
                'status' => 'requires_action',
                'requires_3d_secure' => true,
                '3d_secure_url' => $paymentIntent->next_action->redirect_to_url->url ?? null,
            ]);

            // Send payment requires action notification
            $actionUrl = $payment->{'3d_secure_url'} ?? url('/dashboard/payments/' . $payment->id);
            $this->notificationService->sendPaymentRequiresActionNotification($payment->user, $payment, $actionUrl);

            Log::info('Payment requires 3D Secure authentication', [
                'payment_id' => $payment->id,
                'stripe_payment_intent_id' => $paymentIntent->id
            ]);
        }
    }

    /**
     * Handle invoice created
     */
    protected function handleInvoiceCreated($invoice): void
    {
        if ($invoice->subscription) {
            $subscription = UserSubscription::where('stripe_subscription_id', $invoice->subscription)->first();
            if ($subscription) {
                $this->invoiceService->generateInvoiceForSubscription($subscription);
            }
        }
    }

    /**
     * Handle invoice finalized
     */
    protected function handleInvoiceFinalized($invoice): void
    {
        $this->invoiceService->updateInvoiceFromWebhook($invoice);
    }

    /**
     * Handle invoice updated
     */
    protected function handleInvoiceUpdated($invoice): void
    {
        $this->invoiceService->updateInvoiceFromWebhook($invoice);
    }

    /**
     * Handle successful invoice payment
     */
    protected function handleInvoicePaymentSucceeded($invoice): void
    {
        $subscription = UserSubscription::where('stripe_subscription_id', $invoice->subscription)->first();

        if ($subscription) {
            $updateData = ['status' => 'active'];

            // Safely handle timestamps that might be null
            if ($timestamp = UserSubscription::safeTimestamp($invoice->period_start)) {
                $updateData['current_period_start'] = $timestamp;
            }

            if ($timestamp = UserSubscription::safeTimestamp($invoice->period_end)) {
                $updateData['current_period_end'] = $timestamp;
            }

            $subscription->update($updateData);

            // Create payment record if it doesn't exist
            if ($invoice->payment_intent) {
                $existingPayment = Payment::where('stripe_payment_intent_id', $invoice->payment_intent)->first();

                if (!$existingPayment) {
                    Payment::create([
                        'user_id' => $subscription->user_id,
                        'user_subscription_id' => $subscription->id,
                        'stripe_payment_intent_id' => $invoice->payment_intent,
                        'stripe_invoice_id' => $invoice->id,
                        'amount' => $invoice->amount_paid / 100, // Convert from cents
                        'currency' => $invoice->currency,
                        'status' => 'succeeded',
                        'type' => 'subscription',
                        'paid_at' => now(),
                        'metadata' => [
                            'stripe_invoice_id' => $invoice->id,
                            'billing_reason' => $invoice->billing_reason ?? 'subscription_cycle',
                            'subscription_id' => $subscription->id,
                        ],
                    ]);

                    Log::info('Payment record created from invoice payment', [
                        'subscription_id' => $subscription->id,
                        'payment_intent_id' => $invoice->payment_intent,
                        'invoice_id' => $invoice->id
                    ]);
                }
            }

            Log::info('Subscription invoice payment succeeded', [
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $invoice->subscription
            ]);
        }

        // Update the invoice status
        $this->invoiceService->updateInvoiceFromWebhook($invoice);
    }

    /**
     * Handle failed invoice payment
     */
    protected function handleInvoicePaymentFailed($invoice): void
    {
        $subscription = UserSubscription::where('stripe_subscription_id', $invoice->subscription)->first();

        if ($subscription) {
            $subscription->update([
                'status' => 'past_due',
            ]);

            Log::warning('Subscription invoice payment failed', [
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $invoice->subscription
            ]);
        }

        // Update the invoice status
        $this->invoiceService->updateInvoiceFromWebhook($invoice);
    }

    /**
     * Handle subscription created
     */
    protected function handleSubscriptionCreated($subscription): void
    {
        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();

        if ($userSubscription) {
            $updateData = ['status' => $subscription->status];

            // Safely handle timestamps that might be null
            if ($timestamp = UserSubscription::safeTimestamp($subscription->current_period_start)) {
                $updateData['current_period_start'] = $timestamp;
            }

            if ($timestamp = UserSubscription::safeTimestamp($subscription->current_period_end)) {
                $updateData['current_period_end'] = $timestamp;
            }

            $userSubscription->update($updateData);

            Log::info('Subscription created webhook processed', [
                'subscription_id' => $userSubscription->id,
                'stripe_subscription_id' => $subscription->id
            ]);
        }
    }

    /**
     * Handle subscription updated
     */
    protected function handleSubscriptionUpdated($subscription): void
    {
        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();

        if ($userSubscription) {
            $updateData = ['status' => $subscription->status];

            // Safely handle timestamps that might be null
            if ($timestamp = UserSubscription::safeTimestamp($subscription->current_period_start)) {
                $updateData['current_period_start'] = $timestamp;
            }

            if ($timestamp = UserSubscription::safeTimestamp($subscription->current_period_end)) {
                $updateData['current_period_end'] = $timestamp;
            }

            $userSubscription->update($updateData);

            Log::info('Subscription updated webhook processed', [
                'subscription_id' => $userSubscription->id,
                'stripe_subscription_id' => $subscription->id,
                'new_status' => $subscription->status
            ]);
        }
    }

    /**
     * Handle subscription deleted
     */
    protected function handleSubscriptionDeleted($subscription): void
    {
        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();

        if ($userSubscription) {
            $userSubscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
            ]);

            Log::info('Subscription deleted webhook processed', [
                'subscription_id' => $userSubscription->id,
                'stripe_subscription_id' => $subscription->id
            ]);
        }
    }
}
