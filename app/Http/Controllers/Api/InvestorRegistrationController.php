<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserAccountStatus;
use App\Models\InvestorProfile;
use App\Models\SocialMediaLink;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

class InvestorRegistrationController extends Controller
{
    /**
     * Enhanced investor registration with comprehensive profile creation
     */
    public function register(Request $request): JsonResponse
    {
        $validated = $request->validate([
            // Required fields
            'company_name' => ['required', 'string', 'max:255', 'min:2'],
            'email' => ['required', 'email', 'unique:users', 'max:255'],
            'password' => [
                'required',
                app()->isLocal() ? 'min:3' : Password::min(8)->mixedCase()->numbers()->symbols(),
                'confirmed'
            ],
            'password_confirmation' => ['required'],

            // Optional contact information
            'phone_number' => ['nullable', 'string', 'max:20'],
            'country' => ['nullable', 'string', 'max:255'],

            // Optional social media fields
            'website_url' => ['nullable', 'url', 'max:255'],
            'linkedin_url' => ['nullable', 'url', 'max:255', 'regex:/^https?:\/\/(www\.)?linkedin\.com\/.*$/'],

            // Investment profile fields
            'description' => ['nullable', 'string', 'max:2000'],
            'focus_regions' => ['nullable', 'array', 'max:10'],
            'focus_regions.*' => ['string', 'max:255'],
            'investment_focus' => ['nullable', 'array', 'max:10'],
            'investment_focus.*' => ['string', 'max:255'],
            'investment_check_min' => ['nullable', 'numeric', 'min:0'],
            'investment_check_max' => ['nullable', 'numeric', 'min:0', 'gte:investment_check_min'],
            'investment_stages' => ['nullable', 'array', 'max:10'],
            'investment_stages.*' => ['string', 'max:255'],
            'founded_date' => ['nullable', 'date', 'before_or_equal:today'],
            'number_of_investments' => ['nullable', 'integer', 'min:0'],
        ]);

        try {
            DB::beginTransaction();

            // Create user
            $user = User::create([
                'name' => $validated['company_name'], // Using company name as user name for investors
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => 'investor',
                'phone' => $validated['phone_number'] ?? null,
                'country' => $validated['country'] ?? null,
                'email_verified_at' => app()->isLocal() ? now() : null,
            ]);

            // Create account status record
            UserAccountStatus::create([
                'user_id' => $user->id,
                'status' => 'active',
            ]);

            // Create investor profile
            $profileData = [
                'user_id' => $user->id,
                'company_name' => $validated['company_name'],
                'bio' => $validated['description'] ?? null,
                'investment_budget_min' => $validated['investment_check_min'] ?? null,
                'investment_budget_max' => $validated['investment_check_max'] ?? null,
                'founded_date' => $validated['founded_date'] ?? null,
                'number_of_investments' => $validated['number_of_investments'] ?? null,
                'profile_completed' => $this->isProfileComplete($validated),
            ];

            $investorProfile = InvestorProfile::create($profileData);

            // Create social media links if provided
            $this->createSocialMediaLinks($investorProfile, $validated);

            // Attach taxonomies if provided
            $this->attachTaxonomies($investorProfile, $validated);

            // Send email verification if not in local environment
            if (!app()->isLocal()) {
                $user->sendEmailVerificationNotification();
            }

            DB::commit();

            // Generate API token
            $token = $user->createToken('investor-registration')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Investor registration successful. Please verify your email address.',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'email_verified_at' => $user->email_verified_at,
                        'phone' => $user->phone,
                        'country' => $user->country,
                    ],
                    'investor_profile' => [
                        'id' => $investorProfile->id,
                        'company_name' => $investorProfile->company_name,
                        'bio' => $investorProfile->bio,
                        'investment_budget_min' => $investorProfile->investment_budget_min,
                        'investment_budget_max' => $investorProfile->investment_budget_max,
                        'founded_date' => $investorProfile->founded_date,
                        'number_of_investments' => $investorProfile->number_of_investments,
                        'profile_completed' => $investorProfile->profile_completed,
                    ],
                    'token' => $token,
                    'next_steps' => [
                        'email_verification' => !$user->email_verified_at,
                        'profile_completion' => !$investorProfile->profile_completed,
                        'subscription_required' => true,
                        'discovery_access' => true,
                    ],
                ],
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Investor registration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $validated
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Registration failed. Please try again.',
                'errors' => app()->isLocal() ? ['general' => $e->getMessage()] : ['general' => 'An error occurred during registration.'],
            ], 500);
        }
    }

    /**
     * Create social media links for the investor profile
     */
    private function createSocialMediaLinks(InvestorProfile $profile, array $data): void
    {
        $socialLinks = [];

        if (!empty($data['website_url'])) {
            $socialLinks[] = [
                'platform' => 'website',
                'url' => $data['website_url'],
                'is_primary' => true,
                'is_public' => true,
            ];
        }

        if (!empty($data['linkedin_url'])) {
            $socialLinks[] = [
                'platform' => 'linkedin',
                'url' => $data['linkedin_url'],
                'is_primary' => false,
                'is_public' => true,
            ];
        }

        foreach ($socialLinks as $linkData) {
            $profile->socialMediaLinks()->create($linkData);
        }
    }

    /**
     * Attach taxonomies to the investor profile
     */
    private function attachTaxonomies(InvestorProfile $profile, array $data): void
    {
        $taxonomyMappings = [
            'focus_regions' => 'focus_region',
            'investment_focus' => 'investment_focus',
            'investment_stages' => 'stage',
        ];

        foreach ($taxonomyMappings as $inputKey => $taxonomyType) {
            if (!empty($data[$inputKey])) {
                $taxonomyIds = $this->findOrCreateTaxonomies($data[$inputKey], $taxonomyType);
                if (!empty($taxonomyIds)) {
                    $profile->attachTaxonomies($taxonomyIds);
                }
            }
        }
    }

    /**
     * Find or create taxonomies by name and type
     */
    private function findOrCreateTaxonomies(array $names, string $type): array
    {
        $taxonomyIds = [];

        foreach ($names as $name) {
            $taxonomy = Taxonomy::firstOrCreate(
                [
                    'name' => $name,
                    'type' => $type,
                ],
                [
                    'slug' => \Str::slug($name),
                    'description' => "Auto-created {$type}: {$name}",
                    'sort_order' => 0,
                ]
            );

            $taxonomyIds[] = $taxonomy->id;
        }

        return $taxonomyIds;
    }

    /**
     * Check if profile is complete based on provided data
     */
    private function isProfileComplete(array $data): bool
    {
        $requiredFields = [
            'company_name',
            'description',
            'investment_check_min',
            'investment_check_max',
        ];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }

        // Check if at least one focus region and investment focus is provided
        $hasFocusRegions = !empty($data['focus_regions']) && count($data['focus_regions']) > 0;
        $hasInvestmentFocus = !empty($data['investment_focus']) && count($data['investment_focus']) > 0;

        return $hasFocusRegions && $hasInvestmentFocus;
    }

    /**
     * Get investor registration progress
     */
    public function getRegistrationProgress(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->isInvestor()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Investor role required.',
            ], 403);
        }

        $investorProfile = $user->investorProfile;

        $progress = [
            'user_created' => true,
            'email_verified' => !is_null($user->email_verified_at),
            'basic_profile' => !is_null($investorProfile),
            'profile_completed' => $investorProfile ? $investorProfile->profile_completed : false,
            'social_media_links' => $investorProfile ? $investorProfile->socialMediaLinks()->count() > 0 : false,
            'focus_regions_selected' => $investorProfile ? $investorProfile->focusRegions()->count() > 0 : false,
            'investment_focus_selected' => $investorProfile ? $investorProfile->investmentFocus()->count() > 0 : false,
            'subscription_active' => $user->hasActiveSubscription(),
        ];

        $completedSteps = array_filter($progress);
        $progressPercentage = (count($completedSteps) / count($progress)) * 100;

        $nextSteps = [];
        if (!$progress['email_verified']) {
            $nextSteps[] = 'Verify your email address';
        }
        if (!$progress['basic_profile']) {
            $nextSteps[] = 'Complete your investor profile';
        }
        if (!$progress['profile_completed']) {
            $nextSteps[] = 'Fill in all required profile information';
        }
        if (!$progress['focus_regions_selected']) {
            $nextSteps[] = 'Select your focus regions';
        }
        if (!$progress['investment_focus_selected']) {
            $nextSteps[] = 'Select your investment focus areas';
        }
        if (!$progress['subscription_active']) {
            $nextSteps[] = 'Subscribe to access discovery features';
        }

        return response()->json([
            'success' => true,
            'data' => [
                'progress' => $progress,
                'progress_percentage' => round($progressPercentage, 1),
                'next_steps' => $nextSteps,
                'profile' => $investorProfile ? [
                    'id' => $investorProfile->id,
                    'company_name' => $investorProfile->company_name,
                    'bio' => $investorProfile->bio,
                    'investment_budget_min' => $investorProfile->investment_budget_min,
                    'investment_budget_max' => $investorProfile->investment_budget_max,
                    'founded_date' => $investorProfile->founded_date,
                    'number_of_investments' => $investorProfile->number_of_investments,
                    'profile_completed' => $investorProfile->profile_completed,
                ] : null,
            ],
        ]);
    }
}
