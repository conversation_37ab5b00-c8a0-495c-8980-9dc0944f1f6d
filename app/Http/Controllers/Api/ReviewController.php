<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CustomerReview;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class ReviewController extends Controller
{
    /**
     * Get customer reviews
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 10);
            $featured = $request->boolean('featured');

            $query = CustomerReview::active()->ordered();

            if ($featured) {
                $query->featured();
            }

            $reviews = $query->paginate($perPage);

            $reviewsData = $reviews->getCollection()->map(function ($review) {
                return [
                    'id' => $review->id,
                    'customer_name' => $review->customer_name,
                    'designation' => $review->designation,
                    'company' => $review->company,
                    'formatted_designation' => $review->formatted_designation,
                    'message' => $review->message,
                    'rating' => $review->rating,
                    'rating_stars' => $review->rating_stars,
                    'is_featured' => $review->is_featured,
                    'sort_order' => $review->sort_order,
                    'avatar_url' => $review->avatar_url,
                    'avatar_thumb_url' => $review->avatar_thumb_url,
                    'avatar_medium_url' => $review->avatar_medium_url,
                    'created_at' => $review->created_at->toISOString(),
                ];
            });

            return $this->responseWithSuccess(
                'Customer reviews retrieved successfully',
                [
                    'data' => $reviewsData,
                    'pagination' => [
                        'current_page' => $reviews->currentPage(),
                        'last_page' => $reviews->lastPage(),
                        'per_page' => $reviews->perPage(),
                        'total' => $reviews->total(),
                        'from' => $reviews->firstItem(),
                        'to' => $reviews->lastItem(),
                    ]
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve customer reviews: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve customer reviews',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get featured reviews only
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 10);

            $reviews = CustomerReview::active()
                ->featured()
                ->ordered()
                ->paginate($perPage);

            $reviewsData = $reviews->getCollection()->map(function ($review) {
                return [
                    'id' => $review->id,
                    'customer_name' => $review->customer_name,
                    'designation' => $review->designation,
                    'company' => $review->company,
                    'formatted_designation' => $review->formatted_designation,
                    'message' => $review->message,
                    'rating' => $review->rating,
                    'rating_stars' => $review->rating_stars,
                    'sort_order' => $review->sort_order,
                    'avatar_url' => $review->avatar_url,
                    'avatar_thumb_url' => $review->avatar_thumb_url,
                    'avatar_medium_url' => $review->avatar_medium_url,
                    'created_at' => $review->created_at->toISOString(),
                ];
            });

            return $this->responseWithSuccess(
                'Featured customer reviews retrieved successfully',
                [
                    'data' => $reviewsData,
                    'pagination' => [
                        'current_page' => $reviews->currentPage(),
                        'last_page' => $reviews->lastPage(),
                        'per_page' => $reviews->perPage(),
                        'total' => $reviews->total(),
                        'from' => $reviews->firstItem(),
                        'to' => $reviews->lastItem(),
                    ]
                ]
            );

        } catch (\Exception $e) {
            Log::error('Failed to retrieve featured customer reviews: ' . $e->getMessage());
            return $this->responseWithError(
                'Failed to retrieve featured customer reviews',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
