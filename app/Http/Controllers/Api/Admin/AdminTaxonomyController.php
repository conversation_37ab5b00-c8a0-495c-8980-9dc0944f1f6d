<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class AdminTaxonomyController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:sanctum', 'role:admin,super-admin']);
    }

    /**
     * Display a listing of taxonomies by type
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage', 'focus_region', 'investment_focus'])],
            'search' => ['nullable', 'string', 'max:255'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'sort_by' => ['nullable', 'string', Rule::in(['name', 'created_at', 'sort_order'])],
            'sort_direction' => ['nullable', 'string', Rule::in(['asc', 'desc'])],
        ]);

        $query = Taxonomy::where('type', $validated['type']);

        // Apply search filter
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', '%' . $validated['search'] . '%')
                  ->orWhere('description', 'like', '%' . $validated['search'] . '%');
            });
        }

        // Apply sorting
        $sortBy = $validated['sort_by'] ?? 'sort_order';
        $sortDirection = $validated['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Add secondary sort by name for consistency
        if ($sortBy !== 'name') {
            $query->orderBy('name', 'asc');
        }

        $perPage = $validated['per_page'] ?? 15;
        $taxonomies = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $taxonomies,
            'meta' => [
                'type' => $validated['type'],
                'total_count' => $taxonomies->total(),
            ],
        ]);
    }

    /**
     * Store a newly created taxonomy
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage', 'focus_region', 'investment_focus'])],
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
        ]);

        // Check for duplicate name within the same type
        $exists = Taxonomy::where('type', $validated['type'])
            ->where('name', $validated['name'])
            ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'A taxonomy with this name already exists for this type.',
                'errors' => ['name' => ['This name is already taken for this taxonomy type.']],
            ], 422);
        }

        $taxonomy = Taxonomy::create([
            'type' => $validated['type'],
            'name' => $validated['name'],
            'slug' => Str::slug($validated['name']),
            'description' => $validated['description'] ?? null,
            'sort_order' => $validated['sort_order'] ?? 0,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Taxonomy created successfully.',
            'data' => $taxonomy,
        ], 201);
    }

    /**
     * Display the specified taxonomy
     */
    public function show(Taxonomy $taxonomy): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $taxonomy,
        ]);
    }

    /**
     * Update the specified taxonomy
     */
    public function update(Request $request, Taxonomy $taxonomy): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
        ]);

        // Check for duplicate name within the same type (excluding current record)
        $exists = Taxonomy::where('type', $taxonomy->type)
            ->where('name', $validated['name'])
            ->where('id', '!=', $taxonomy->id)
            ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'A taxonomy with this name already exists for this type.',
                'errors' => ['name' => ['This name is already taken for this taxonomy type.']],
            ], 422);
        }

        $taxonomy->update([
            'name' => $validated['name'],
            'slug' => Str::slug($validated['name']),
            'description' => $validated['description'] ?? $taxonomy->description,
            'sort_order' => $validated['sort_order'] ?? $taxonomy->sort_order,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Taxonomy updated successfully.',
            'data' => $taxonomy->fresh(),
        ]);
    }

    /**
     * Remove the specified taxonomy
     */
    public function destroy(Taxonomy $taxonomy): JsonResponse
    {
        // Check if taxonomy is in use
        $usageCount = $taxonomy->taxonomables()->count();

        if ($usageCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete taxonomy. It is currently used by {$usageCount} record(s).",
                'errors' => ['taxonomy' => ['This taxonomy is currently in use and cannot be deleted.']],
            ], 422);
        }

        $taxonomy->delete();

        return response()->json([
            'success' => true,
            'message' => 'Taxonomy deleted successfully.',
        ]);
    }

    /**
     * Get taxonomy statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['nullable', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage', 'focus_region', 'investment_focus'])],
        ]);

        $query = Taxonomy::query();

        if (!empty($validated['type'])) {
            $query->where('type', $validated['type']);
        }

        $statistics = [
            'total_taxonomies' => $query->count(),
            'by_type' => Taxonomy::selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'usage_statistics' => Taxonomy::withCount('taxonomables')
                ->when(!empty($validated['type']), function($q) use ($validated) {
                    $q->where('type', $validated['type']);
                })
                ->orderBy('taxonomables_count', 'desc')
                ->limit(10)
                ->get(['id', 'name', 'type', 'taxonomables_count']),
        ];

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * Bulk update sort orders
     */
    public function bulkUpdateSortOrder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', Rule::in(['keyword', 'tag', 'category', 'brand', 'type', 'model', 'industry', 'technology', 'market', 'stage', 'focus_region', 'investment_focus'])],
            'taxonomies' => ['required', 'array', 'min:1'],
            'taxonomies.*.id' => ['required', 'integer', 'exists:taxonomies,id'],
            'taxonomies.*.sort_order' => ['required', 'integer', 'min:0'],
        ]);

        foreach ($validated['taxonomies'] as $taxonomyData) {
            Taxonomy::where('id', $taxonomyData['id'])
                ->where('type', $validated['type'])
                ->update(['sort_order' => $taxonomyData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Sort orders updated successfully.',
        ]);
    }
}
