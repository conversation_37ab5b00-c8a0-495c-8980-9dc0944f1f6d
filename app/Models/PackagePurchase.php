<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PackagePurchase extends Model
{
    use HasFactory;

    protected $table = 'package_purchases';

    protected $fillable = [
        'user_id',
        'package_id',
        'stripe_payment_intent_id',
        'amount',
        'status',
        'purchased_at',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'purchased_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the package purchase
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the package that was purchased
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    /**
     * Scope to get completed purchases
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get pending purchases
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get failed purchases
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Check if purchase is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if purchase is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if purchase failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'completed' => 'bg-success-500 text-white',
            'pending' => 'bg-warning-500 text-white',
            'failed' => 'bg-danger-500 text-white',
            'refunded' => 'bg-secondary-500 text-white',
            default => 'bg-slate-500 text-white',
        };
    }

    /**
     * Create a new package purchase record
     */
    public static function createPurchaseRecord(
        int $userId,
        int $packageId,
        string $stripePaymentIntentId,
        float $amount,
        string $status = 'pending',
        array $metadata = []
    ): self {
        return self::create([
            'user_id' => $userId,
            'package_id' => $packageId,
            'stripe_payment_intent_id' => $stripePaymentIntentId,
            'amount' => $amount,
            'status' => $status,
            'purchased_at' => $status === 'completed' ? now() : null,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Mark purchase as completed
     */
    public function markAsCompleted(): bool
    {
        return $this->update([
            'status' => 'completed',
            'purchased_at' => now(),
        ]);
    }

    /**
     * Mark purchase as failed
     */
    public function markAsFailed(): bool
    {
        return $this->update(['status' => 'failed']);
    }
}
