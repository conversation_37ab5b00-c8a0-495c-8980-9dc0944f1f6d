<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ContactSubmission extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'status',
        'read_at',
        'read_by',
        'admin_notes',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'read_at' => 'datetime',
    ];

    /**
     * Get the user who read this submission
     */
    public function readBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'read_by');
    }

    /**
     * Scope to get new submissions
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope to get read submissions
     */
    public function scopeRead($query)
    {
        return $query->where('status', 'read');
    }

    /**
     * Scope to get replied submissions
     */
    public function scopeReplied($query)
    {
        return $query->where('status', 'replied');
    }

    /**
     * <PERSON>ope to get archived submissions
     */
    public function scopeArchived($query)
    {
        return $query->where('status', 'archived');
    }

    /**
     * Mark submission as read
     */
    public function markAsRead(int $userId = null): void
    {
        $this->update([
            'status' => 'read',
            'read_at' => now(),
            'read_by' => $userId ?? auth()->id(),
        ]);
    }

    /**
     * Mark submission as replied
     */
    public function markAsReplied(int $userId = null): void
    {
        $this->update([
            'status' => 'replied',
            'read_at' => $this->read_at ?? now(),
            'read_by' => $this->read_by ?? ($userId ?? auth()->id()),
        ]);
    }

    /**
     * Archive submission
     */
    public function archive(): void
    {
        $this->update(['status' => 'archived']);
    }

    /**
     * Check if submission is new
     */
    public function isNew(): bool
    {
        return $this->status === 'new';
    }

    /**
     * Check if submission is read
     */
    public function isRead(): bool
    {
        return in_array($this->status, ['read', 'replied']);
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeColor(): string
    {
        return match ($this->status) {
            'new' => 'bg-warning-500',
            'read' => 'bg-info-500',
            'replied' => 'bg-success-500',
            'archived' => 'bg-slate-500',
            default => 'bg-slate-500',
        };
    }
}
