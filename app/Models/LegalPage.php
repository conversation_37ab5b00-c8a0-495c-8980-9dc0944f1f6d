<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class LegalPage extends Model
{
    protected $fillable = [
        'type',
        'title',
        'content',
        'is_active',
        'last_updated_at',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_updated_at' => 'datetime',
    ];

    /**
     * Get the user who last updated this legal page
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get active legal pages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get legal page by type
     */
    public static function getByType(string $type): ?self
    {
        return static::where('type', $type)->active()->first();
    }

    /**
     * Get terms and conditions
     */
    public static function getTermsAndConditions(): ?self
    {
        return static::getByType('terms-and-conditions');
    }

    /**
     * Get privacy policy
     */
    public static function getPrivacyPolicy(): ?self
    {
        return static::getByType('privacy-policy');
    }

    /**
     * Update the last_updated_at timestamp
     */
    public function markAsUpdated(int $userId = null): void
    {
        $this->update([
            'last_updated_at' => now(),
            'updated_by' => $userId ?? auth()->id(),
        ]);
    }
}
