<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class WebsiteSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        // Logo settings
        'header_logo',
        'footer_logo',
        'header_logo_dark',
        'footer_logo_dark',
        
        // Favicon settings
        'favicon_ico',
        'favicon_png_16',
        'favicon_png_32',
        'favicon_png_192',
        'favicon_svg',
        
        // Email settings
        'mail_driver',
        'mail_host',
        'mail_port',
        'mail_username',
        'mail_password',
        'mail_encryption',
        'mail_from_address',
        'mail_from_name',
        
        // Additional settings
        'site_name',
        'site_description',
        'meta_keywords',
        'google_analytics_id',
        'facebook_pixel_id',
    ];

    protected $casts = [
        'mail_port' => 'integer',
    ];

    /**
     * Get the header logo URL
     */
    public function getHeaderLogoUrlAttribute(): ?string
    {
        return $this->header_logo ? Storage::url($this->header_logo) : null;
    }

    /**
     * Get the footer logo URL
     */
    public function getFooterLogoUrlAttribute(): ?string
    {
        return $this->footer_logo ? Storage::url($this->footer_logo) : null;
    }

    /**
     * Get the header logo dark URL
     */
    public function getHeaderLogoDarkUrlAttribute(): ?string
    {
        return $this->header_logo_dark ? Storage::url($this->header_logo_dark) : null;
    }

    /**
     * Get the footer logo dark URL
     */
    public function getFooterLogoDarkUrlAttribute(): ?string
    {
        return $this->footer_logo_dark ? Storage::url($this->footer_logo_dark) : null;
    }

    /**
     * Get the favicon ICO URL
     */
    public function getFaviconIcoUrlAttribute(): ?string
    {
        return $this->favicon_ico ? Storage::url($this->favicon_ico) : null;
    }

    /**
     * Get the favicon PNG 16x16 URL
     */
    public function getFaviconPng16UrlAttribute(): ?string
    {
        return $this->favicon_png_16 ? Storage::url($this->favicon_png_16) : null;
    }

    /**
     * Get the favicon PNG 32x32 URL
     */
    public function getFaviconPng32UrlAttribute(): ?string
    {
        return $this->favicon_png_32 ? Storage::url($this->favicon_png_32) : null;
    }

    /**
     * Get the favicon PNG 192x192 URL
     */
    public function getFaviconPng192UrlAttribute(): ?string
    {
        return $this->favicon_png_192 ? Storage::url($this->favicon_png_192) : null;
    }

    /**
     * Get the favicon SVG URL
     */
    public function getFaviconSvgUrlAttribute(): ?string
    {
        return $this->favicon_svg ? Storage::url($this->favicon_svg) : null;
    }

    /**
     * Get email configuration as array
     */
    public function getEmailConfigAttribute(): array
    {
        return [
            'driver' => $this->mail_driver,
            'host' => $this->mail_host,
            'port' => $this->mail_port,
            'username' => $this->mail_username,
            'password' => $this->mail_password,
            'encryption' => $this->mail_encryption,
            'from_address' => $this->mail_from_address,
            'from_name' => $this->mail_from_name,
        ];
    }

    /**
     * Check if email is configured
     */
    public function isEmailConfiguredAttribute(): bool
    {
        return !empty($this->mail_host) && 
               !empty($this->mail_port) && 
               !empty($this->mail_from_address);
    }

    /**
     * Get the default favicon (fallback)
     */
    public function getDefaultFaviconAttribute(): ?string
    {
        return $this->favicon_ico_url ?? 
               $this->favicon_png_32_url ?? 
               $this->favicon_png_16_url ?? 
               $this->favicon_svg_url;
    }

    /**
     * Get all logo variants
     */
    public function getLogoVariantsAttribute(): array
    {
        return [
            'header_light' => $this->header_logo_url,
            'header_dark' => $this->header_logo_dark_url,
            'footer_light' => $this->footer_logo_url,
            'footer_dark' => $this->footer_logo_dark_url,
        ];
    }

    /**
     * Get all favicon variants
     */
    public function getFaviconVariantsAttribute(): array
    {
        return [
            'ico' => $this->favicon_ico_url,
            'png_16' => $this->favicon_png_16_url,
            'png_32' => $this->favicon_png_32_url,
            'png_192' => $this->favicon_png_192_url,
            'svg' => $this->favicon_svg_url,
        ];
    }

    /**
     * Scope to get the main settings record
     */
    public function scopeMain($query)
    {
        return $query->first() ?? new static();
    }
}
