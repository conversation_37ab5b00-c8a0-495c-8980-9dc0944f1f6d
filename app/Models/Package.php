<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Package extends Model
{
    use HasFactory;

    protected $table = 'packages';

    protected $fillable = [
        'name',
        'description',
        'price',
        'features',
        'is_active',
        'stripe_product_id',
        'stripe_price_id',
        'sort_order',
        'target_role',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the package purchases for this package
     */
    public function packagePurchases(): HasMany
    {
        return $this->hasMany(PackagePurchase::class);
    }

    /**
     * Get the package purchases for this package (alias)
     */
    public function purchases(): HasMany
    {
        return $this->packagePurchases();
    }

    /**
     * Scope to get active packages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get packages for a specific role
     */
    public function scopeForRole($query, string $role)
    {
        return $query->where('target_role', $role);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Check if package is available for purchase
     */
    public function isAvailable(): bool
    {
        return $this->is_active;
    }

    /**
     * Get total purchases count
     */
    public function getTotalPurchasesAttribute(): int
    {
        return $this->packagePurchases()->where('status', 'completed')->count();
    }

    /**
     * Get total revenue from this package
     */
    public function getTotalRevenueAttribute(): float
    {
        return $this->packagePurchases()
            ->where('status', 'completed')
            ->sum('amount');
    }
}
