<?php

namespace App\Services;

use App\Models\User;
use Stripe\StripeClient;
use Stripe\Customer;
use Stripe\PaymentMethod;
use Stripe\PaymentIntent;
use Stripe\Subscription;
use Stripe\Price;
use Stripe\Product;
use Stripe\Invoice;
use Stripe\Refund;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class StripeService
{
    protected StripeClient $stripe;

    public function __construct()
    {
        $this->stripe = app('stripe');
    }

    /**
     * Create or retrieve a Stripe customer for a user
     */
    public function createOrGetCustomer(User $user): Customer
    {
        // Use the normalized architecture - get stripe_customer_id from UserAccountStatus
        if ($user->stripe_customer_id) {
            try {
                return $this->stripe->customers->retrieve($user->stripe_customer_id);
            } catch (\Exception $e) {
                Log::warning('Failed to retrieve Stripe customer', [
                    'user_id' => $user->id,
                    'stripe_customer_id' => $user->stripe_customer_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $customer = $this->stripe->customers->create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        // Store stripe_customer_id in the UserAccountStatus model (normalized architecture)
        $accountStatus = $user->accountStatus;
        if (!$accountStatus) {
            // Create account status record if it doesn't exist
            $accountStatus = $user->accountStatus()->create([
                'user_id' => $user->id,
                'status' => 'active',
            ]);
        }

        $accountStatus->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    /**
     * Create a setup intent for payment method collection
     */
    public function createSetupIntent(string $customerId, array $options = []): \Stripe\SetupIntent
    {
        $params = array_merge([
            'customer' => $customerId,
            'payment_method_types' => ['card'],
            'usage' => 'off_session',
        ], $options);

        return $this->stripe->setupIntents->create($params);
    }

    /**
     * Create a Stripe Checkout Session for payment method setup
     */
    public function createCheckoutSession(string $customerId, string $successUrl, string $cancelUrl): \Stripe\Checkout\Session
    {
        return $this->stripe->checkout->sessions->create([
            'payment_method_types' => ['card'],
            'mode' => 'setup',
            'customer' => $customerId,
            'success_url' => $successUrl,
            'cancel_url' => $cancelUrl,
        ]);
    }

    /**
     * Create a Stripe Checkout Session for subscription
     */
    public function createSubscriptionCheckoutSession(string $customerId, string $priceId, string $successUrl, string $cancelUrl, array $metadata = []): \Stripe\Checkout\Session
    {
        return $this->stripe->checkout->sessions->create([
            'payment_method_types' => ['card'],
            'mode' => 'subscription',
            'customer' => $customerId,
            'line_items' => [
                [
                    'price' => $priceId,
                    'quantity' => 1,
                ],
            ],
            'success_url' => $successUrl,
            'cancel_url' => $cancelUrl,
            'metadata' => $metadata,
            'subscription_data' => [
                'metadata' => $metadata,
            ],
        ]);
    }

    /**
     * Create a Stripe Checkout Session for one-time payment (services)
     */
    public function createOneTimeCheckoutSession(
        string $customerId,
        string $priceId,
        string $successUrl,
        string $cancelUrl,
        array $metadata = []
    ): \Stripe\Checkout\Session {
        return $this->stripe->checkout->sessions->create([
            'payment_method_types' => ['card'],
            'mode' => 'payment',
            'customer' => $customerId,
            'line_items' => [
                [
                    'price' => $priceId,
                    'quantity' => 1,
                ],
            ],
            'success_url' => $successUrl,
            'cancel_url' => $cancelUrl,
            'metadata' => $metadata,
            'payment_intent_data' => [
                'metadata' => $metadata,
            ],
        ]);
    }

    /**
     * Retrieve a Stripe Checkout Session
     */
    public function retrieveCheckoutSession(string $sessionId): \Stripe\Checkout\Session
    {
        return $this->stripe->checkout->sessions->retrieve($sessionId, [
            'expand' => ['setup_intent', 'setup_intent.payment_method']
        ]);
    }

    /**
     * Create a payment intent
     */
    public function createPaymentIntent(int $amount, string $currency = 'usd', array $metadata = []): PaymentIntent
    {
        return $this->stripe->paymentIntents->create([
            'amount' => $amount,
            'currency' => $currency,
            'metadata' => $metadata,
            'automatic_payment_methods' => [
                'enabled' => true,
            ],
        ]);
    }

    /**
     * Retrieve a payment intent
     */
    public function retrievePaymentIntent(string $paymentIntentId): PaymentIntent
    {
        return $this->stripe->paymentIntents->retrieve($paymentIntentId);
    }

    /**
     * Confirm a payment intent
     */
    public function confirmPaymentIntent(string $paymentIntentId, array $params = []): PaymentIntent
    {
        return $this->stripe->paymentIntents->confirm($paymentIntentId, $params);
    }

    /**
     * Cancel a payment intent
     */
    public function cancelPaymentIntent(string $paymentIntentId): PaymentIntent
    {
        return $this->stripe->paymentIntents->cancel($paymentIntentId);
    }

    /**
     * Create a subscription
     */
    public function createSubscription(string $customerId, string $priceId, array $options = []): Subscription
    {
        $params = array_merge([
            'customer' => $customerId,
            'items' => [
                ['price' => $priceId],
            ],
            'payment_behavior' => 'default_incomplete',
            'payment_settings' => [
                'save_default_payment_method' => 'on_subscription',
            ],
            'expand' => ['latest_invoice.payment_intent'],
        ], $options);

        return $this->stripe->subscriptions->create($params);
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(string $subscriptionId, bool $immediately = false): Subscription
    {
        if ($immediately) {
            return $this->stripe->subscriptions->cancel($subscriptionId);
        }

        return $this->stripe->subscriptions->update($subscriptionId, [
            'cancel_at_period_end' => true,
        ]);
    }

    /**
     * Create a refund
     */
    public function createRefund(string $paymentIntentId, int $amount = null, array $metadata = []): Refund
    {
        $params = [
            'payment_intent' => $paymentIntentId,
            'metadata' => $metadata,
        ];

        if ($amount) {
            $params['amount'] = $amount;
        }

        return $this->stripe->refunds->create($params);
    }

    /**
     * Retrieve an invoice
     */
    public function getInvoice(string $invoiceId): Invoice
    {
        return $this->stripe->invoices->retrieve($invoiceId);
    }

    /**
     * Create a Stripe product
     */
    public function createProduct(array $data)
    {
        // Support both old and new format
        if (isset($data['name']) && is_string($data['name']) && !isset($data['description'])) {
            // Old format: createProduct($name, $description)
            $name = $data['name'];
            $description = $data['description'] ?? null;
            return $this->stripe->products->create([
                'name' => $name,
                'description' => $description,
            ]);
        }

        // New format: createProduct($data)
        return $this->stripe->products->create($data);
    }

    /**
     * Create a Stripe price
     */
    public function createPrice(array $data)
    {
        return $this->stripe->prices->create($data);
    }

    /**
     * Attach payment method to customer
     */
    public function attachPaymentMethod(string $paymentMethodId, string $customerId): PaymentMethod
    {
        return $this->stripe->paymentMethods->attach($paymentMethodId, [
            'customer' => $customerId,
        ]);
    }

    /**
     * Set default payment method for customer
     */
    public function setDefaultPaymentMethod(string $customerId, string $paymentMethodId): Customer
    {
        return $this->stripe->customers->update($customerId, [
            'invoice_settings' => [
                'default_payment_method' => $paymentMethodId,
            ],
        ]);
    }

    /**
     * Get customer's payment methods
     */
    public function getCustomerPaymentMethods(string $customerId, string $type = 'card'): array
    {
        $paymentMethods = $this->stripe->paymentMethods->all([
            'customer' => $customerId,
            'type' => $type,
        ]);

        return $paymentMethods->data;
    }

    /**
     * Get a specific payment method
     */
    public function getPaymentMethod(string $paymentMethodId): PaymentMethod
    {
        return $this->stripe->paymentMethods->retrieve($paymentMethodId);
    }

    /**
     * Detach payment method from customer
     */
    public function detachPaymentMethod(string $paymentMethodId): PaymentMethod
    {
        return $this->stripe->paymentMethods->detach($paymentMethodId);
    }

    /**
     * Attach payment method to customer (alias for consistency)
     */
    public function attachPaymentMethodToCustomer(string $paymentMethodId, string $customerId): PaymentMethod
    {
        return $this->attachPaymentMethod($paymentMethodId, $customerId);
    }

    /**
     * Get a subscription from Stripe
     */
    public function getSubscription(string $subscriptionId): Subscription
    {
        return $this->stripe->subscriptions->retrieve($subscriptionId);
    }

    /**
     * Get invoices for a subscription
     */
    public function getSubscriptionInvoices(string $subscriptionId, int $limit = 10): array
    {
        $invoices = $this->stripe->invoices->all([
            'subscription' => $subscriptionId,
            'limit' => $limit,
        ]);

        return $invoices->data;
    }

    /**
     * Pay an invoice
     */
    public function payInvoice(string $invoiceId): Invoice
    {
        return $this->stripe->invoices->pay($invoiceId);
    }

    /**
     * Create an invoice in Stripe
     */
    public function createInvoice(string $customerId, array $params = []): Invoice
    {
        $defaultParams = [
            'customer' => $customerId,
        ];

        return $this->stripe->invoices->create(array_merge($defaultParams, $params));
    }

    /**
     * Update an invoice in Stripe
     */
    public function updateInvoice(string $invoiceId, array $params = []): Invoice
    {
        return $this->stripe->invoices->update($invoiceId, $params);
    }

    /**
     * Get invoices for a subscription
     */
    public function getInvoicesForSubscription(string $subscriptionId)
    {
        return $this->stripe->invoices->all([
            'subscription' => $subscriptionId,
            'limit' => 100,
        ]);
    }

    /**
     * Calculate prorated refund amount
     */
    public function calculateProratedRefund(Subscription $subscription): int
    {
        $currentPeriodStart = $subscription->current_period_start;
        $currentPeriodEnd = $subscription->current_period_end;
        $now = time();

        $totalDays = ($currentPeriodEnd - $currentPeriodStart) / 86400; // Convert to days
        $usedDays = ($now - $currentPeriodStart) / 86400;
        $remainingDays = $totalDays - $usedDays;

        if ($remainingDays <= 0) {
            return 0;
        }

        $totalAmount = $subscription->items->data[0]->price->unit_amount;
        $refundAmount = ($remainingDays / $totalDays) * $totalAmount;

        return (int) round($refundAmount);
    }

    /**
     * Update a product in Stripe
     */
    public function updateProduct(string $productId, array $data)
    {
        return $this->stripe->products->update($productId, $data);
    }

    /**
     * Get a product from Stripe
     */
    public function getProduct(string $productId)
    {
        return $this->stripe->products->retrieve($productId);
    }

    /**
     * Get a price from Stripe
     */
    public function getPrice(string $priceId)
    {
        return $this->stripe->prices->retrieve($priceId);
    }

    /**
     * Update a subscription
     */
    public function updateSubscription(string $subscriptionId, string $newPriceId): Subscription
    {
        $subscription = $this->stripe->subscriptions->retrieve($subscriptionId);

        return $this->stripe->subscriptions->update($subscriptionId, [
            'items' => [
                [
                    'id' => $subscription->items->data[0]->id,
                    'price' => $newPriceId,
                ],
            ],
            'proration_behavior' => 'create_prorations',
        ]);
    }

    /**
     * Cancel subscription immediately
     */
    public function cancelSubscriptionImmediately(string $subscriptionId): Subscription
    {
        return $this->stripe->subscriptions->cancel($subscriptionId);
    }

    /**
     * Pause a subscription
     */
    public function pauseSubscription(string $subscriptionId, string $resumeDate = null): Subscription
    {
        $params = [
            'pause_collection' => [
                'behavior' => 'void',
            ],
        ];

        if ($resumeDate) {
            $params['pause_collection']['resumes_at'] = strtotime($resumeDate);
        }

        return $this->stripe->subscriptions->update($subscriptionId, $params);
    }

    /**
     * Resume a paused subscription
     */
    public function resumeSubscription(string $subscriptionId): Subscription
    {
        return $this->stripe->subscriptions->update($subscriptionId, [
            'pause_collection' => '',
        ]);
    }

    /**
     * Save a payment method to the database after successful Stripe Checkout
     */
    public function savePaymentMethod(User $user, \Stripe\PaymentMethod $stripePaymentMethod): \App\Models\PaymentMethod
    {
        DB::beginTransaction();

        try {
            // Create or get Stripe customer
            $customer = $this->createOrGetCustomer($user);

            // Attach payment method to customer in Stripe if not already attached
            if ($stripePaymentMethod->customer !== $customer->id) {
                $this->attachPaymentMethodToCustomer($stripePaymentMethod->id, $customer->id);
            }

            // Check if this is the user's first payment method to set as default
            $isFirstPaymentMethod = $user->paymentMethods()->count() === 0;

            // If this should be the default, unset other defaults
            if ($isFirstPaymentMethod) {
                $user->paymentMethods()->update(['is_default' => false]);

                // Set as default in Stripe
                $this->setDefaultPaymentMethod($customer->id, $stripePaymentMethod->id);
            }

            // Create local payment method record
            $paymentMethod = \App\Models\PaymentMethod::create([
                'user_id' => $user->id,
                'stripe_payment_method_id' => $stripePaymentMethod->id,
                'type' => $stripePaymentMethod->type,
                'is_default' => $isFirstPaymentMethod,
                'card_brand' => $stripePaymentMethod->card->brand ?? null,
                'card_last_four' => $stripePaymentMethod->card->last4 ?? null,
                'card_exp_month' => $stripePaymentMethod->card->exp_month ?? null,
                'card_exp_year' => $stripePaymentMethod->card->exp_year ?? null,
                'metadata' => [
                    'stripe_customer_id' => $customer->id,
                ],
            ]);

            DB::commit();

            return $paymentMethod;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
