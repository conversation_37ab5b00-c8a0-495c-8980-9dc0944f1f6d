<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\ImageOptimizer\Image;

class FileUploadService
{
    /**
     * Upload and process an image file
     */
    public function uploadImage(UploadedFile $file, string $directory, string $prefix = null, array $maxDimensions = null): string
    {
        // Generate unique filename
        $filename = ($prefix ? $prefix . '_' : '') . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = $directory . '/' . $filename;
        // Save original file
        Storage::disk('public')->putFileAs($directory, $file, $filename);
        return $path;
    }

    /**
     * Upload a regular file
     */
    public function uploadFile(UploadedFile $file, string $directory, string $prefix = null): string
    {
        $filename = ($prefix ? $prefix . '_' : '') . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = $directory . '/' . $filename;

        Storage::disk('public')->putFileAs($directory, $file, $filename);

        return $path;
    }

    /**
     * Delete a file from storage
     */
    public function deleteFile(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }

        return false;
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file, array $allowedMimes = ['jpeg', 'jpg', 'png', 'gif', 'svg'], int $maxSize = 5120): array
    {
        $errors = [];

        // Check file type
        if (!in_array($file->getClientOriginalExtension(), $allowedMimes)) {
            $errors[] = 'Invalid file type. Allowed types: ' . implode(', ', $allowedMimes);
        }

        // Check file size (in KB)
        if ($file->getSize() > ($maxSize * 1024)) {
            $errors[] = 'File size too large. Maximum size: ' . $maxSize . 'KB';
        }

        // Check if it's actually an image (except SVG)
        if (!in_array($file->getClientOriginalExtension(), ['svg']) && !getimagesize($file->getPathname())) {
            $errors[] = 'Invalid image file.';
        }

        return $errors;
    }

    /**
     * Generate multiple favicon sizes from a single image
     */
    public function generateFaviconSizes(UploadedFile $file, string $directory = 'favicons'): array
    {
        $sizes = [16, 32, 48, 64, 128, 192, 256];
        $generatedFiles = [];

        $image = Image::make($file);

        foreach ($sizes as $size) {
            $filename = "favicon_{$size}x{$size}.png";
            $path = $directory . '/' . $filename;

            $resizedImage = clone $image;
            $resizedImage->resize($size, $size);

            Storage::disk('public')->put($path, $resizedImage->encode('png'));
            $generatedFiles[$size] = $path;
        }

        return $generatedFiles;
    }

    /**
     * Get file URL from storage path
     */
    public function getFileUrl(string $path): string
    {
        return Storage::url($path);
    }

    /**
     * Check if file exists in storage
     */
    public function fileExists(string $path): bool
    {
        return Storage::disk('public')->exists($path);
    }

    /**
     * Get file size in human readable format
     */
    public function getFileSize(string $path): string
    {
        if (!$this->fileExists($path)) {
            return 'File not found';
        }

        $bytes = Storage::disk('public')->size($path);
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Create optimized image variants
     */
    public function createImageVariants(UploadedFile $file, string $directory, array $variants): array
    {
        $createdVariants = [];
        $image = Image::make($file);

        foreach ($variants as $variantName => $config) {
            $filename = $variantName . '_' . time() . '_' . Str::random(10) . '.jpg';
            $path = $directory . '/' . $filename;

            $variantImage = clone $image;

            // Apply transformations
            if (isset($config['width']) && isset($config['height'])) {
                $variantImage->resize($config['width'], $config['height'], function ($constraint) use ($config) {
                    if ($config['maintain_aspect'] ?? true) {
                        $constraint->aspectRatio();
                    }
                    if ($config['upsize'] ?? false) {
                        $constraint->upsize();
                    }
                });
            }

            // Apply quality
            $quality = $config['quality'] ?? 85;

            Storage::disk('public')->put($path, $variantImage->encode('jpg', $quality));
            $createdVariants[$variantName] = $path;
        }

        return $createdVariants;
    }
}
