<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;

class EmailConfigService
{
    /**
     * Get available email providers with their configurations
     */
    public function getEmailProviders(): array
    {
        return [
            'smtp' => [
                'name' => 'Custom SMTP',
                'description' => 'Configure your own SMTP server',
                'default_port' => 587,
                'default_encryption' => 'tls'
            ],
            'gmail' => [
                'name' => 'Gmail',
                'description' => 'Google Gmail SMTP',
                'host' => 'smtp.gmail.com',
                'port' => 587,
                'encryption' => 'tls'
            ],
            'sendgrid' => [
                'name' => 'SendGrid',
                'description' => 'SendGrid Email Service',
                'host' => 'smtp.sendgrid.net',
                'port' => 587,
                'encryption' => 'tls'
            ],
            'mailgun' => [
                'name' => 'Mailgun',
                'description' => 'Mailgun Email Service',
                'host' => 'smtp.mailgun.org',
                'port' => 587,
                'encryption' => 'tls'
            ],
            'ses' => [
                'name' => 'Amazon SES',
                'description' => 'Amazon Simple Email Service',
                'host' => 'email-smtp.us-east-1.amazonaws.com',
                'port' => 587,
                'encryption' => 'tls'
            ],
            'postmark' => [
                'name' => 'Postmark',
                'description' => 'Postmark Email Service',
                'host' => 'smtp.postmarkapp.com',
                'port' => 587,
                'encryption' => 'tls'
            ],
            'sparkpost' => [
                'name' => 'SparkPost',
                'description' => 'SparkPost Email Service',
                'host' => 'smtp.sparkpostmail.com',
                'port' => 587,
                'encryption' => 'tls'
            ]
        ];
    }

    /**
     * Update email configuration in environment file
     */
    public function updateEmailConfig(array $config): bool
    {
        try {
            $envPath = base_path('.env');
            
            if (!File::exists($envPath)) {
                return false;
            }

            $envContent = File::get($envPath);

            // Email configuration mappings
            $envMappings = [
                'MAIL_MAILER' => $config['mail_driver'] ?? 'smtp',
                'MAIL_HOST' => $config['mail_host'] ?? '',
                'MAIL_PORT' => $config['mail_port'] ?? 587,
                'MAIL_USERNAME' => $config['mail_username'] ?? '',
                'MAIL_PASSWORD' => $config['mail_password'] ?? '',
                'MAIL_ENCRYPTION' => $config['mail_encryption'] ?? 'tls',
                'MAIL_FROM_ADDRESS' => $config['mail_from_address'] ?? '',
                'MAIL_FROM_NAME' => '"' . ($config['mail_from_name'] ?? config('app.name')) . '"',
            ];

            // Update each configuration value
            foreach ($envMappings as $key => $value) {
                $pattern = "/^{$key}=.*/m";
                $replacement = "{$key}={$value}";

                if (preg_match($pattern, $envContent)) {
                    $envContent = preg_replace($pattern, $replacement, $envContent);
                } else {
                    $envContent .= "\n{$replacement}";
                }
            }

            File::put($envPath, $envContent);

            // Clear config cache to reload new values
            if (function_exists('artisan')) {
                \Artisan::call('config:clear');
            }

            return true;

        } catch (\Exception $e) {
            \Log::error('Failed to update email configuration', [
                'error' => $e->getMessage(),
                'config' => $config
            ]);

            return false;
        }
    }

    /**
     * Test email configuration
     */
    public function testEmailConfig(array $config): array
    {
        try {
            // Temporarily set mail configuration
            Config::set('mail.mailers.smtp.host', $config['mail_host']);
            Config::set('mail.mailers.smtp.port', $config['mail_port']);
            Config::set('mail.mailers.smtp.username', $config['mail_username']);
            Config::set('mail.mailers.smtp.password', $config['mail_password']);
            Config::set('mail.mailers.smtp.encryption', $config['mail_encryption']);
            Config::set('mail.from.address', $config['mail_from_address']);
            Config::set('mail.from.name', $config['mail_from_name']);

            // Test connection (this is a basic test)
            $transport = new \Swift_SmtpTransport(
                $config['mail_host'],
                $config['mail_port'],
                $config['mail_encryption']
            );

            if (!empty($config['mail_username'])) {
                $transport->setUsername($config['mail_username']);
            }

            if (!empty($config['mail_password'])) {
                $transport->setPassword($config['mail_password']);
            }

            $mailer = new \Swift_Mailer($transport);
            $transport->start();

            return [
                'success' => true,
                'message' => 'Email configuration is valid and connection successful.'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Email configuration test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get current email configuration from environment
     */
    public function getCurrentEmailConfig(): array
    {
        return [
            'mail_driver' => config('mail.default'),
            'mail_host' => config('mail.mailers.smtp.host'),
            'mail_port' => config('mail.mailers.smtp.port'),
            'mail_username' => config('mail.mailers.smtp.username'),
            'mail_encryption' => config('mail.mailers.smtp.encryption'),
            'mail_from_address' => config('mail.from.address'),
            'mail_from_name' => config('mail.from.name'),
        ];
    }

    /**
     * Validate email configuration
     */
    public function validateEmailConfig(array $config): array
    {
        $errors = [];

        if (empty($config['mail_host'])) {
            $errors[] = 'Mail host is required.';
        }

        if (empty($config['mail_port']) || !is_numeric($config['mail_port'])) {
            $errors[] = 'Valid mail port is required.';
        }

        if (empty($config['mail_from_address']) || !filter_var($config['mail_from_address'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Valid from email address is required.';
        }

        if (empty($config['mail_from_name'])) {
            $errors[] = 'From name is required.';
        }

        if (!empty($config['mail_encryption']) && !in_array($config['mail_encryption'], ['tls', 'ssl'])) {
            $errors[] = 'Encryption must be either TLS or SSL.';
        }

        return $errors;
    }

    /**
     * Get email templates configuration
     */
    public function getEmailTemplates(): array
    {
        return [
            'welcome' => [
                'name' => 'Welcome Email',
                'description' => 'Sent to new users after registration',
                'variables' => ['user_name', 'site_name', 'login_url']
            ],
            'password_reset' => [
                'name' => 'Password Reset',
                'description' => 'Sent when user requests password reset',
                'variables' => ['user_name', 'reset_url', 'expiry_time']
            ],
            'subscription_confirmation' => [
                'name' => 'Subscription Confirmation',
                'description' => 'Sent after successful subscription',
                'variables' => ['user_name', 'plan_name', 'amount', 'next_billing_date']
            ],
            'payment_failed' => [
                'name' => 'Payment Failed',
                'description' => 'Sent when payment fails',
                'variables' => ['user_name', 'plan_name', 'amount', 'retry_url']
            ],
            'subscription_cancelled' => [
                'name' => 'Subscription Cancelled',
                'description' => 'Sent when subscription is cancelled',
                'variables' => ['user_name', 'plan_name', 'cancellation_date']
            ]
        ];
    }
}
