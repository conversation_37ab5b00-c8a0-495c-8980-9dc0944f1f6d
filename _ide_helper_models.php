<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $title
 * @property string $excerpt
 * @property string $content
 * @property string|null $featured_image
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $published_at
 * @property int $author_id
 * @property string $slug
 * @property array<array-key, mixed>|null $meta_data
 * @property int $views_count
 * @property bool $featured
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $author
 * @property-read string|null $featured_image_url
 * @property-read int $reading_time
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog draft()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog featured()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog filterByTaxonomies(array $filters, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog published()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereAuthorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereExcerpt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereFeatured($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereFeaturedImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereMetaData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog wherePublishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog whereViewsCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withAllTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withAnyTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withTaxonomy($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withTaxonomyAtDepth(int $depth, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withTaxonomyHierarchy(int $taxonomyId, bool $includeDescendants = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withTaxonomySlug(string $slug, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withTaxonomyType(\Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string $type, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Blog withoutTaxonomies($taxonomies, string $name = 'taxonomable')
 */
	class Blog extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property string|null $phone
 * @property string $subject
 * @property string $message
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $read_at
 * @property int|null $read_by
 * @property string|null $admin_notes
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $readBy
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission archived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission new()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission read()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission replied()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereAdminNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereReadBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereSubject($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContactSubmission whereUserAgent($value)
 */
	class ContactSubmission extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $customer_name
 * @property string|null $designation
 * @property string $message
 * @property bool $is_featured
 * @property bool $is_active
 * @property int $sort_order
 * @property int|null $rating
 * @property string|null $company
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string|null $avatar_medium_url
 * @property-read string|null $avatar_thumb_url
 * @property-read string|null $avatar_url
 * @property-read string $formatted_designation
 * @property-read array $rating_stars
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview featured()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereCustomerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereDesignation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereIsFeatured($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerReview whereUpdatedAt($value)
 */
	class CustomerReview extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $question_text
 * @property string $category
 * @property string $type
 * @property array<array-key, mixed>|null $options
 * @property int $weight
 * @property int $sort_order
 * @property bool $is_required
 * @property bool $is_active
 * @property string|null $help_text
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\EsgResponse> $esgResponses
 * @property-read int|null $esg_responses_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion byCategory($category)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion required()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereHelpText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereIsRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereQuestionText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgQuestion whereWeight($value)
 */
	class EsgQuestion extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $startup_profile_id
 * @property int $esg_question_id
 * @property string $response_value
 * @property numeric|null $score
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\EsgQuestion $esgQuestion
 * @property-read \App\Models\StartupProfile $startupProfile
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse byCategory($category)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse byQuestion($questionId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse byStartup($startupProfileId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse whereEsgQuestionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse whereResponseValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse whereStartupProfileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EsgResponse whereUpdatedAt($value)
 */
	class EsgResponse extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $question
 * @property string $answer
 * @property string $target_role
 * @property string $status
 * @property int $sort_order
 * @property string|null $category
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $creator
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq byCategory(?string $category = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq forRole(string $role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereAnswer($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereQuestion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereTargetRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Faq whereUpdatedAt($value)
 */
	class Faq extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $group
 * @property string $name
 * @property bool $locked
 * @property string $payload
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting whereGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting whereLocked($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GeneralSetting whereUpdatedAt($value)
 */
	class GeneralSetting extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $investor_id
 * @property int $startup_id
 * @property string $status
 * @property string|null $message
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $investor
 * @property-read \App\Models\User $startup
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereInvestorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereStartupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interest whereUpdatedAt($value)
 */
	class Interest extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $requester_id
 * @property int $target_id
 * @property string $type
 * @property string $status
 * @property string|null $message
 * @property numeric|null $proposed_amount
 * @property array<array-key, mixed>|null $terms
 * @property int|null $approved_by
 * @property \Illuminate\Support\Carbon|null $approved_at
 * @property string|null $rejection_reason
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $approver
 * @property-read \App\Models\User $requester
 * @property-read \App\Models\User $target
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest approved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest byRequester($userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest byTarget($userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest byType($type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest fundingRequest()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest investmentInterest()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest rejected()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereApprovedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereApprovedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereProposedAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereRejectionReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereRequesterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereTargetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereTerms($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterestRequest whereUpdatedAt($value)
 */
	class InterestRequest extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $company_name
 * @property numeric|null $investment_budget_min
 * @property numeric|null $investment_budget_max
 * @property string|null $risk_tolerance
 * @property string|null $investment_experience
 * @property string|null $bio
 * @property \Illuminate\Support\Carbon|null $founded_date
 * @property int|null $number_of_investments
 * @property string|null $website
 * @property string|null $linkedin
 * @property array<array-key, mixed>|null $investment_preferences
 * @property bool $profile_completed
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InterestRequest> $receivedInterestRequests
 * @property-read int|null $received_interest_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InterestRequest> $sentInterestRequests
 * @property-read int|null $sent_interest_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SocialMediaLink> $socialMediaLinks
 * @property-read int|null $social_media_links_count
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile byExperience($experience)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile byRiskTolerance($riskTolerance)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile completed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile filterByTaxonomies(array $filters, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereFoundedDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereInvestmentBudgetMax($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereInvestmentBudgetMin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereInvestmentExperience($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereInvestmentPreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereLinkedin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereNumberOfInvestments($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereProfileCompleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereRiskTolerance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile whereWebsite($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withAllTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withAnyTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withTaxonomy($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withTaxonomyAtDepth(int $depth, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withTaxonomyHierarchy(int $taxonomyId, bool $includeDescendants = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withTaxonomySlug(string $slug, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withTaxonomyType(\Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string $type, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withinBudget($minAmount, $maxAmount = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvestorProfile withoutTaxonomies($taxonomies, string $name = 'taxonomable')
 */
	class InvestorProfile extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $user_subscription_id
 * @property int|null $payment_id
 * @property string $stripe_invoice_id
 * @property string $invoice_number
 * @property numeric $subtotal
 * @property numeric $tax_amount
 * @property numeric $total
 * @property string $currency
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $due_date
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property array<array-key, mixed>|null $line_items
 * @property array<array-key, mixed>|null $metadata
 * @property string|null $pdf_url
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_total
 * @property-read string $status_display
 * @property-read \App\Models\Payment|null $payment
 * @property-read \App\Models\User $user
 * @property-read \App\Models\UserSubscription|null $userSubscription
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice open()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice overdue()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice paid()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereInvoiceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereLineItems($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePaymentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePdfUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereStripeInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereSubtotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTaxAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereUserSubscriptionId($value)
 */
	class Invoice extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $type
 * @property string $title
 * @property string $content
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $last_updated_at
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $updatedBy
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereLastUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LegalPage whereUpdatedBy($value)
 */
	class LegalPage extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property numeric $price
 * @property array<array-key, mixed>|null $features
 * @property bool $is_active
 * @property string|null $stripe_product_id
 * @property string|null $stripe_price_id
 * @property int $sort_order
 * @property string $target_role
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_price
 * @property-read int $total_purchases
 * @property-read float $total_revenue
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PackagePurchase> $packagePurchases
 * @property-read int|null $package_purchases_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PackagePurchase> $purchases
 * @property-read int|null $purchases_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package forRole(string $role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereFeatures($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereStripePriceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereStripeProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereTargetRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Package whereUpdatedAt($value)
 */
	class Package extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int $package_id
 * @property string|null $stripe_payment_intent_id
 * @property numeric $amount
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $purchased_at
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_amount
 * @property-read string $status_badge_class
 * @property-read \App\Models\Package $package
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase completed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase wherePackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase wherePurchasedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereStripePaymentIntentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PackagePurchase whereUserId($value)
 */
	class PackagePurchase extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $user_subscription_id
 * @property string|null $stripe_payment_intent_id
 * @property string|null $stripe_invoice_id
 * @property numeric $amount
 * @property string $currency
 * @property string $status
 * @property string $type
 * @property string|null $payment_method_type
 * @property string|null $failure_code
 * @property string|null $failure_message
 * @property bool $requires_3d_secure
 * @property string|null $3d_secure_url
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_amount
 * @property-read string $status_display
 * @property-read float $total_refunded
 * @property-read \App\Models\Invoice|null $invoice
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Refund> $refunds
 * @property-read int|null $refunds_count
 * @property-read \App\Models\UserSubscription|null $subscription
 * @property-read \App\Models\User $user
 * @property-read \App\Models\UserSubscription|null $userSubscription
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment requiresAction()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment successful()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment where3dSecureUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereFailureCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereFailureMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment wherePaymentMethodType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereRequires3dSecure($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereStripeInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereStripePaymentIntentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereUserSubscriptionId($value)
 */
	class Payment extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string $stripe_payment_method_id
 * @property string $type
 * @property bool $is_default
 * @property bool $is_active
 * @property string|null $card_brand
 * @property string|null $card_last_four
 * @property int|null $card_exp_month
 * @property int|null $card_exp_year
 * @property string|null $bank_name
 * @property string|null $bank_last_four
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string|null $card_expiration
 * @property-read string $display_name
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod cards()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod default()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereBankLastFour($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereBankName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereCardBrand($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereCardExpMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereCardExpYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereCardLastFour($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereStripePaymentMethodId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereUserId($value)
 */
	class PaymentMethod extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int $payment_id
 * @property int|null $user_subscription_id
 * @property string $stripe_refund_id
 * @property int $amount
 * @property string $currency
 * @property string $status
 * @property string $reason
 * @property string|null $description
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $processed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $invoice_id
 * @property string|null $stripe_payment_intent_id
 * @property string|null $stripe_charge_id
 * @property int|null $original_amount
 * @property int|null $prorated_amount
 * @property string $type
 * @property array<array-key, mixed>|null $proration_details
 * @property \Illuminate\Support\Carbon|null $service_start_date
 * @property \Illuminate\Support\Carbon|null $service_end_date
 * @property \Illuminate\Support\Carbon|null $cancellation_date
 * @property int|null $days_used
 * @property int|null $total_days
 * @property string|null $internal_notes
 * @property int|null $processed_by
 * @property \Illuminate\Support\Carbon|null $stripe_created_at
 * @property-read string $formatted_amount
 * @property-read string $formatted_original_amount
 * @property-read string $formatted_prorated_amount
 * @property-read float|null $proration_percentage
 * @property-read string $reason_display
 * @property-read string $status_display
 * @property-read \App\Models\Invoice|null $invoice
 * @property-read \App\Models\Payment $payment
 * @property-read \App\Models\User|null $processedBy
 * @property-read \App\Models\User $user
 * @property-read \App\Models\UserSubscription|null $userSubscription
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund successful()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereCancellationDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereDaysUsed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereInternalNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereOriginalAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund wherePaymentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereProcessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereProcessedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereProratedAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereProrationDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereServiceEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereServiceStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereStripeChargeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereStripeCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereStripePaymentIntentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereStripeRefundId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereTotalDays($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Refund whereUserSubscriptionId($value)
 */
	class Refund extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $linkable_type
 * @property int $linkable_id
 * @property string $platform
 * @property string $url
 * @property string|null $username
 * @property bool $is_primary
 * @property bool $is_public
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_url
 * @property-read string $platform_icon
 * @property-read string $platform_name
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $linkable
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink platform($platform)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink primary()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink public()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereIsPrimary($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereIsPublic($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereLinkableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereLinkableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink wherePlatform($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SocialMediaLink whereUsername($value)
 */
	class SocialMediaLink extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string $company_name
 * @property string $company_description
 * @property \Illuminate\Support\Carbon|null $founding_date
 * @property int|null $employee_count
 * @property string|null $website
 * @property string|null $linkedin
 * @property string|null $company_linkedin
 * @property string|null $incorporation_status
 * @property string|null $incorporation_date
 * @property string|null $incorporation_place
 * @property string|null $company_headquarters
 * @property string|null $current_stage
 * @property string|null $funding_stage
 * @property numeric|null $funding_amount_sought
 * @property numeric|null $current_valuation
 * @property array<array-key, mixed>|null $business_model
 * @property string|null $use_of_funds
 * @property string|null $previous_funding_rounds
 * @property string|null $current_investors
 * @property string|null $monthly_revenue
 * @property string|null $monthly_burn_rate
 * @property int|null $runway_months
 * @property numeric|null $esg_score
 * @property array<array-key, mixed>|null $esg_breakdown
 * @property bool $esg_completed
 * @property bool $profile_completed
 * @property string $profile_completion_percentage
 * @property string|null $profile_completion_steps
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\EsgResponse> $esgResponses
 * @property-read int|null $esg_responses_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InterestRequest> $receivedInterestRequests
 * @property-read int|null $received_interest_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InterestRequest> $sentInterestRequests
 * @property-read int|null $sent_interest_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SocialMediaLink> $socialMediaLinks
 * @property-read int|null $social_media_links_count
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile byEsgScore($minScore, $maxScore = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile byFundingStage($stage)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile completed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile esgCompleted()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile filterByTaxonomies(array $filters, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile seekingFunding($minAmount, $maxAmount = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereBusinessModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCompanyDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCompanyHeadquarters($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCompanyLinkedin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCurrentInvestors($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCurrentStage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereCurrentValuation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereEmployeeCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereEsgBreakdown($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereEsgCompleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereEsgScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereFoundingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereFundingAmountSought($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereFundingStage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereIncorporationDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereIncorporationPlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereIncorporationStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereLinkedin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereMonthlyBurnRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereMonthlyRevenue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile wherePreviousFundingRounds($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereProfileCompleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereProfileCompletionPercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereProfileCompletionSteps($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereRunwayMonths($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereUseOfFunds($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile whereWebsite($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withAllTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withAnyTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withTaxonomy($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withTaxonomyAtDepth(int $depth, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withTaxonomyHierarchy(int $taxonomyId, bool $includeDescendants = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withTaxonomySlug(string $slug, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withTaxonomyType(\Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string $type, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StartupProfile withoutTaxonomies($taxonomies, string $name = 'taxonomable')
 */
	class StartupProfile extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property numeric $price
 * @property string $billing_cycle
 * @property array<array-key, mixed>|null $features
 * @property array<array-key, mixed>|null $limits
 * @property bool $is_active
 * @property string|null $stripe_product_id
 * @property string|null $stripe_price_id
 * @property int $sort_order
 * @property string $target_role
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $billing_cycle_display
 * @property-read string $formatted_price
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\UserSubscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\UserSubscription> $userSubscriptions
 * @property-read int|null $user_subscriptions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct active()
 * @method static \Database\Factories\SubscriptionProductFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct forRole(string $role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereBillingCycle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereFeatures($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereLimits($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereStripePriceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereStripeProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereTargetRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionProduct whereUpdatedAt($value)
 */
	class SubscriptionProduct extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property string|null $phone
 * @property string|null $post_code
 * @property string|null $city
 * @property string|null $country
 * @property string|null $referral_source
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $role
 * @property string $password
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\UserAccountStatus|null $accountStatus
 * @property-read \App\Models\UserSubscription|null $activeSubscription
 * @property-read \App\Models\UserSubscription|null $activeSubscriptionWithProduct
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InterestRequest> $approvedInterestRequests
 * @property-read int|null $approved_interest_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PackagePurchase> $completedPackagePurchases
 * @property-read int|null $completed_package_purchases_count
 * @property-read \App\Models\PaymentMethod|null $defaultPaymentMethod
 * @property-read string|null $account_locked_at
 * @property-read bool $account_locked
 * @property-read string $account_status_string
 * @property-read string|null $stripe_customer_id
 * @property-read \App\Models\InvestorProfile|null $investorProfile
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invoice> $invoices
 * @property-read int|null $invoices_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PackagePurchase> $packagePurchases
 * @property-read int|null $package_purchases_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PaymentMethod> $paymentMethods
 * @property-read int|null $payment_methods_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Payment> $payments
 * @property-read int|null $payments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InterestRequest> $receivedInterestRequests
 * @property-read int|null $received_interest_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Refund> $refunds
 * @property-read int|null $refunds_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InterestRequest> $sentInterestRequests
 * @property-read int|null $sent_interest_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SocialMediaLink> $socialMediaLinks
 * @property-read int|null $social_media_links_count
 * @property-read \App\Models\StartupProfile|null $startupProfile
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\UserSubscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User filterByTaxonomies(array $filters, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePostCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereReferralSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withAllTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withAnyTaxonomies($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTaxonomy($taxonomies, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTaxonomyAtDepth(int $depth, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTaxonomyHierarchy(int $taxonomyId, bool $includeDescendants = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTaxonomySlug(string $slug, \Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string|null $type = null, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTaxonomyType(\Aliziodev\LaravelTaxonomy\Enums\TaxonomyType|string $type, string $name = 'taxonomable')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutAuthUser()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutSuperAdmin()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutTaxonomies($taxonomies, string $name = 'taxonomable')
 */
	class User extends \Eloquent implements \Illuminate\Contracts\Auth\MustVerifyEmail, \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string $status
 * @property string|null $stripe_customer_id
 * @property bool $is_locked
 * @property \Illuminate\Support\Carbon|null $locked_at
 * @property string|null $locked_reason
 * @property int|null $locked_by
 * @property \Illuminate\Support\Carbon|null $suspended_at
 * @property string|null $suspended_reason
 * @property int|null $suspended_by
 * @property \Illuminate\Support\Carbon|null $activated_at
 * @property int|null $activated_by
 * @property \Illuminate\Support\Carbon|null $unlocked_at
 * @property int|null $unlocked_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $activatedBy
 * @property-read \App\Models\User|null $lockedBy
 * @property-read \App\Models\User|null $suspendedBy
 * @property-read \App\Models\User|null $unlockedBy
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus locked()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus suspended()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereActivatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereActivatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereIsLocked($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereLockedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereLockedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereLockedReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereStripeCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereSuspendedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereSuspendedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereSuspendedReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereUnlockedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereUnlockedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAccountStatus whereUserId($value)
 */
	class UserAccountStatus extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int $subscription_product_id
 * @property string $stripe_subscription_id
 * @property string|null $stripe_customer_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $current_period_start
 * @property \Illuminate\Support\Carbon|null $current_period_end
 * @property \Illuminate\Support\Carbon|null $trial_start
 * @property \Illuminate\Support\Carbon|null $trial_end
 * @property \Illuminate\Support\Carbon|null $canceled_at
 * @property string|null $cancellation_reason
 * @property \Illuminate\Support\Carbon|null $paused_at
 * @property string|null $pause_reason
 * @property \Illuminate\Support\Carbon|null $ends_at
 * @property numeric $amount
 * @property string $currency
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invoice> $invoices
 * @property-read int|null $invoices_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Payment> $payments
 * @property-read int|null $payments_count
 * @property-read \App\Models\SubscriptionProduct $product
 * @property-read \App\Models\SubscriptionProduct $subscriptionProduct
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription pastDue()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCanceledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCancellationReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCurrentPeriodEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCurrentPeriodStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereEndsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription wherePauseReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription wherePausedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereStripeCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereStripeSubscriptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereSubscriptionProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereTrialEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereTrialStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereUserId($value)
 */
	class UserSubscription extends \Eloquent {}
}

