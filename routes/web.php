<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SetLocaleController;
use App\Http\Controllers\GeneralSettingController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\AdminUserManagementController;
use App\Http\Controllers\Admin\AdminSubscriptionManagementController;
use App\Http\Controllers\Admin\AdminFinancialController;
use App\Http\Controllers\Admin\PaymentMethodMonitoringController;
use App\Http\Controllers\Admin\AdminProductController;
use App\Http\Controllers\Admin\AdminPackageController;
use App\Http\Controllers\Admin\AdminPackagePurchaseController;
use App\Http\Controllers\Admin\AdminLegalPageController;
use App\Http\Controllers\Admin\AdminContactController;
use App\Http\Controllers\Admin\AdminCustomerReviewController;
use App\Http\Controllers\Admin\AdminCategoryController;
use App\Http\Controllers\Admin\AdminRoleController;
use App\Http\Controllers\Admin\AdminInvestmentController;
use App\Http\Controllers\InvestorController;
use App\Http\Controllers\StartupController;
use App\Http\Controllers\Admin\AdminReportsController;
use App\Http\Controllers\Admin\AdminBlogController;
use App\Http\Controllers\Admin\AdminFaqController;
use App\Http\Controllers\Admin\AdminEsgController;
use App\Http\Controllers\FaqController;

require __DIR__ . '/auth.php';

Route::get('/', function () {
    return to_route('login');
});

// React App Route - Catch all routes for React Router
Route::get('/app/{any?}', function () {
    return view('react-app');
})->where('any', '.*')->name('react.app');

// Test route to debug
Route::get('/test-react', function () {
    return view('react-app');
});

// Frontend FAQ Routes (accessible to all users)
Route::prefix('faqs')->name('faqs.')->group(function () {
    Route::get('/', [FaqController::class, 'index'])->name('index');
    Route::get('search', [FaqController::class, 'search'])->name('search');
});

// Stripe Checkout Routes (outside auth middleware for Stripe redirects)
Route::middleware(['auth'])->group(function () {
    Route::get('/payment-method/add', [App\Http\Controllers\Api\PaymentMethodController::class, 'addPaymentMethod'])->name('payment.add');
    Route::get('/stripe/card-add/success', [App\Http\Controllers\Api\PaymentMethodController::class, 'handleCheckoutSuccess'])->name('stripe.card_add.success');
    Route::get('/stripe/card-add/cancelled', [App\Http\Controllers\Api\PaymentMethodController::class, 'handleCheckoutCancel'])->name('stripe.card_add.cancelled');
});

Route::group(['middleware' => ['auth', 'verified']], function () {
    // Redirect to admin dashboard for authenticated users
    Route::get('dashboard', function () {
        return redirect()->route('admin.dashboard');
    })->name('dashboard.index');

    // Locale (available to all authenticated users)
    Route::get('setlocale/{locale}', SetLocaleController::class)->name('setlocale');

    // DataTable server-side processing routes (must be before resource routes)
    Route::get('investors/datatable', [InvestorController::class, 'datatable'])->name('investors.datatable')->middleware('role:admin|super-admin|analyst');
    Route::get('startups/datatable', [StartupController::class, 'datatable'])->name('startups.datatable')->middleware('role:admin|super-admin|analyst');

    // Investment Platform Routes (role-based access)
    Route::resource('investors', InvestorController::class)->middleware('role:admin|super-admin|analyst');
    Route::resource('startups', StartupController::class)->middleware('role:admin|super-admin|analyst');

    // Admin Dashboard Routes
    Route::prefix('admin')->name('admin.')->middleware('role:admin|super-admin|analyst')->group(function () {
        // Main Dashboard
        Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

        // Investment Platform Overview Routes
        Route::prefix('investment')->name('investment.')->group(function () {
            Route::get('/', [AdminInvestmentController::class, 'index'])->name('index');
            Route::get('interest-requests', [AdminInvestmentController::class, 'interestRequests'])->name('interest-requests');
            Route::get('users', [AdminInvestmentController::class, 'users'])->name('users');
            Route::get('esg-analytics', [AdminInvestmentController::class, 'esgAnalytics'])->name('esg-analytics');
        });

        // ESG Management Routes
        Route::prefix('esg')->name('esg.')->group(function () {
            Route::get('/', [AdminEsgController::class, 'index'])->name('index');

            // ESG Questions Management
            Route::prefix('questions')->name('questions.')->group(function () {
                Route::get('/', [AdminEsgController::class, 'questions'])->name('index');
                Route::get('create', [AdminEsgController::class, 'createQuestion'])->name('create');
                Route::post('/', [AdminEsgController::class, 'storeQuestion'])->name('store');
                Route::get('{question}/edit', [AdminEsgController::class, 'editQuestion'])->name('edit');
                Route::put('{question}', [AdminEsgController::class, 'updateQuestion'])->name('update');
                Route::delete('{question}', [AdminEsgController::class, 'destroyQuestion'])->name('destroy');
            });

            // ESG Responses Review
            Route::prefix('responses')->name('responses.')->group(function () {
                Route::get('/', [AdminEsgController::class, 'responses'])->name('index');
                Route::get('{startupProfile}', [AdminEsgController::class, 'viewResponse'])->name('view');
            });

            // ESG Scoring Configuration
            Route::get('configuration', [AdminEsgController::class, 'configuration'])->name('configuration');
        });

        // User Management (Admin/Super-Admin only) - Updated to use enhanced AdminUserManagementController
        Route::middleware('role:admin|super-admin')->group(function () {
            Route::get('users', [AdminUserManagementController::class, 'index'])->name('users.index');
            Route::get('users/create', [UserController::class, 'create'])->name('users.create');
            Route::post('users', [UserController::class, 'store'])->name('users.store');
            Route::get('users/{user}', [AdminUserManagementController::class, 'show'])->name('users.show');
            Route::get('users/{user}/edit', [AdminUserManagementController::class, 'edit'])->name('users.edit');
            Route::put('users/{user}', [AdminUserManagementController::class, 'update'])->name('users.update');
            Route::delete('users/{user}', [UserController::class, 'destroy'])->name('users.destroy');

            // Profile Management
            Route::resource('profiles', ProfileController::class)->only(['index', 'update'])
                ->parameter('profiles', 'user')->names([
                    'index' => 'profiles.index',
                    'update' => 'profiles.update',
                ]);
        });

        // System Settings (Super-Admin only)
        Route::middleware('role:admin|super-admin')->group(function () {
            Route::singleton('general-settings', GeneralSettingController::class)->names([
                'show' => 'general-settings.show',
                'edit' => 'general-settings.edit',
                'update' => 'general-settings.update',
            ]);
            Route::post('general-settings-logo', [GeneralSettingController::class, 'logoUpdate'])->name('general-settings.logo');
        });

        // Investment Platform Management (All admin roles)
        Route::prefix('investment')->name('investment.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\AdminInvestmentController::class, 'index'])->name('index');
            Route::get('interest-requests', [App\Http\Controllers\Admin\AdminInvestmentController::class, 'interestRequests'])->name('interest-requests');
            Route::get('interest-requests/{interestRequest}', [App\Http\Controllers\Admin\AdminInvestmentController::class, 'showInterestRequest'])->name('interest-requests.show');
            Route::post('interest-requests/{interestRequest}/approve', [App\Http\Controllers\Admin\AdminInvestmentController::class, 'approveInterestRequest'])->name('interest-requests.approve');
            Route::post('interest-requests/{interestRequest}/reject', [App\Http\Controllers\Admin\AdminInvestmentController::class, 'rejectInterestRequest'])->name('interest-requests.reject');
            Route::get('users', [App\Http\Controllers\Admin\AdminInvestmentController::class, 'users'])->name('users');
            Route::get('esg-analytics', [App\Http\Controllers\Admin\AdminInvestmentController::class, 'esgAnalytics'])->name('esg-analytics');
        });

        // Subscription User Management (All admin roles can view, Admin/Super-Admin can modify)
        Route::prefix('subscription-users')->name('subscription-users.')->group(function () {
            Route::get('/', [AdminUserManagementController::class, 'index'])->name('index');
            Route::get('{user}', [AdminUserManagementController::class, 'show'])->name('show');
            Route::get('{user}/subscription-analytics', [AdminUserManagementController::class, 'subscriptionAnalytics'])->name('subscription-analytics');
            Route::get('{user}/payment-history', [AdminUserManagementController::class, 'paymentHistory'])->name('payment-history');

            // Admin/Super-Admin only actions
            Route::middleware('role:admin|super-admin')->group(function () {
                Route::get('{user}/edit', [AdminUserManagementController::class, 'edit'])->name('edit');
                Route::put('{user}', [AdminUserManagementController::class, 'update'])->name('update');
                Route::put('{user}/account-status', [AdminUserManagementController::class, 'updateAccountStatus'])->name('account-status');
            });
        });

        // Subscription Management (All admin roles can view, Admin/Super-Admin can modify)
        Route::prefix('subscriptions')->name('subscriptions.')->group(function () {
            Route::get('/', [AdminSubscriptionManagementController::class, 'index'])->name('index');
            Route::get('{subscription}', [AdminSubscriptionManagementController::class, 'show'])->name('show');
            Route::get('analytics', [AdminSubscriptionManagementController::class, 'analytics'])->name('analytics');

            // Admin/Super-Admin only actions
            Route::middleware('role:admin|super-admin')->group(function () {
                Route::put('{subscription}/cancel', [AdminSubscriptionManagementController::class, 'cancel'])->name('cancel');
                Route::put('{subscription}/pause', [AdminSubscriptionManagementController::class, 'pause'])->name('pause');
                Route::put('{subscription}/resume', [AdminSubscriptionManagementController::class, 'resume'])->name('resume');
            });
        });

        // Financial Management (Admin/Super-Admin only)
        Route::prefix('financial')->name('financial.')->middleware('role:admin|super-admin')->group(function () {
            Route::get('/', [AdminFinancialController::class, 'index'])->name('index');
            Route::get('payments', [AdminFinancialController::class, 'payments'])->name('payments');
            Route::get('payments/{payment}', [AdminFinancialController::class, 'showPayment'])->name('payments.show');
            Route::get('invoices', [AdminFinancialController::class, 'invoices'])->name('invoices');
            Route::get('invoices/{invoice}', [AdminFinancialController::class, 'showInvoice'])->name('invoices.show');
            Route::get('refunds', [AdminFinancialController::class, 'refunds'])->name('refunds');
            Route::post('refunds', [AdminFinancialController::class, 'processRefund'])->name('refunds.process');
        });

        // Payment Method Monitoring (Admin/Super-Admin only)
        Route::prefix('payment-methods')->name('payment-methods.')->middleware('role:admin|super-admin')->group(function () {
            Route::get('/', [PaymentMethodMonitoringController::class, 'index'])->name('index');
            Route::get('three-d-secure', [PaymentMethodMonitoringController::class, 'threeDSecure'])->name('three-d-secure');
            Route::get('analytics', [PaymentMethodMonitoringController::class, 'analytics'])->name('analytics');
        });

        // Subscription Products Management (Admin/Super-Admin only)
        Route::middleware('role:admin|super-admin')->group(function () {
            Route::resource('products', AdminProductController::class)->except(['show']);
            Route::put('products/{product}/toggle-status', [AdminProductController::class, 'toggleStatus'])->name('products.toggle-status');
        });

        // Packages Management (Admin/Super-Admin only)
        Route::middleware('role:admin|super-admin')->group(function () {
            Route::resource('packages', AdminPackageController::class)->except(['show']);
            Route::put('packages/{package}/toggle-status', [AdminPackageController::class, 'toggleStatus'])->name('packages.toggle-status');
        });

        // Package Purchases Management (All admin roles can view, Admin/Super-Admin can modify)
        Route::prefix('package-purchases')->name('package-purchases.')->group(function () {
            Route::get('/', [AdminPackagePurchaseController::class, 'index'])->name('index');
            Route::get('{packagePurchase}', [AdminPackagePurchaseController::class, 'show'])->name('show');

            // Admin/Super-Admin only actions
            Route::middleware('role:admin|super-admin')->group(function () {
                Route::put('{packagePurchase}', [AdminPackagePurchaseController::class, 'update'])->name('update');
                Route::delete('{packagePurchase}', [AdminPackagePurchaseController::class, 'destroy'])->name('destroy');
            });
        });

        // Categories Management (Admin/Super-Admin only - hidden from analysts)
        Route::middleware('role:admin|super-admin')->group(function () {
            Route::resource('categories', AdminCategoryController::class)->names([
                'index' => 'categories.index',
                'create' => 'categories.create',
                'store' => 'categories.store',
                'show' => 'categories.show',
                'edit' => 'categories.edit',
                'update' => 'categories.update',
                'destroy' => 'categories.destroy',
            ]);
            Route::put('categories/{category}/toggle-status', [AdminCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
        });

        // Legal Pages Management (Admin/Super-Admin only)
        Route::middleware('role:admin|super-admin')->group(function () {
            Route::resource('legal-pages', AdminLegalPageController::class)->except(['show']);
            Route::put('legal-pages/{legalPage}/toggle-status', [AdminLegalPageController::class, 'toggleStatus'])->name('legal-pages.toggle-status');
        });

        // Contact Submissions Management (All admin roles can view, Admin/Super-Admin can modify)
        Route::prefix('contacts')->name('contacts.')->group(function () {
            Route::get('/', [AdminContactController::class, 'index'])->name('index');
            Route::get('{contact}', [AdminContactController::class, 'show'])->name('show');

            // Admin/Super-Admin only actions
            Route::middleware('role:admin|super-admin')->group(function () {
                Route::put('{contact}', [AdminContactController::class, 'update'])->name('update');
                Route::put('{contact}/mark-replied', [AdminContactController::class, 'markAsReplied'])->name('mark-replied');
                Route::put('{contact}/archive', [AdminContactController::class, 'archive'])->name('archive');
                Route::delete('{contact}', [AdminContactController::class, 'destroy'])->name('destroy');
            });
        });

        // Customer Reviews Management (Admin/Super-Admin only)
        Route::middleware('role:admin|super-admin')->group(function () {
            Route::resource('reviews', AdminCustomerReviewController::class)->except(['show']);
            Route::put('reviews/{review}/toggle-featured', [AdminCustomerReviewController::class, 'toggleFeatured'])->name('reviews.toggle-featured');
            Route::put('reviews/{review}/toggle-active', [AdminCustomerReviewController::class, 'toggleActive'])->name('reviews.toggle-active');
        });

        // Admin Role Management (Super-Admin only)
        Route::middleware('role:super-admin')->group(function () {
            Route::resource('admin-roles', AdminRoleController::class)->names([
                'index' => 'roles.index',
                'create' => 'roles.create',
                'store' => 'roles.store',
                'show' => 'roles.show',
                'edit' => 'roles.edit',
                'update' => 'roles.update',
                'destroy' => 'roles.destroy',
            ]);
            Route::post('admin-roles/{user}/toggle-status', [AdminRoleController::class, 'toggleAccountStatus'])->name('roles.toggle-status');
        });

        // Reports (All admin roles can view)
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', [AdminReportsController::class, 'index'])->name('index');
            Route::get('subscriptions', [AdminReportsController::class, 'subscriptions'])->name('subscriptions');
            Route::get('users', [AdminReportsController::class, 'users'])->name('users');
            Route::get('refund-requests', [AdminReportsController::class, 'refundRequests'])->name('refund-requests');
        });

        // Blog Management (Admin and Super Admin only)
        Route::prefix('blogs')->name('blogs.')->middleware('role:admin|super-admin')->group(function () {
            Route::get('/', [AdminBlogController::class, 'index'])->name('index');
            Route::get('create', [AdminBlogController::class, 'create'])->name('create');
            Route::post('/', [AdminBlogController::class, 'store'])->name('store');
            Route::get('{blog}', [AdminBlogController::class, 'show'])->name('show');
            Route::get('{blog}/edit', [AdminBlogController::class, 'edit'])->name('edit');
            Route::put('{blog}', [AdminBlogController::class, 'update'])->name('update');
            Route::delete('{blog}', [AdminBlogController::class, 'destroy'])->name('destroy');
        });

        // FAQ Management (Admin and Super Admin only)
        Route::prefix('faqs')->name('faqs.')->middleware('role:admin|super-admin')->group(function () {
            Route::get('/', [AdminFaqController::class, 'index'])->name('index');
            Route::get('create', [AdminFaqController::class, 'create'])->name('create');
            Route::post('/', [AdminFaqController::class, 'store'])->name('store');
            Route::get('{faq}', [AdminFaqController::class, 'show'])->name('show');
            Route::get('{faq}/edit', [AdminFaqController::class, 'edit'])->name('edit');
            Route::put('{faq}', [AdminFaqController::class, 'update'])->name('update');
            Route::delete('{faq}', [AdminFaqController::class, 'destroy'])->name('destroy');
            Route::patch('{faq}/toggle-status', [AdminFaqController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('update-order', [AdminFaqController::class, 'updateOrder'])->name('update-order');
        });
    });
});
