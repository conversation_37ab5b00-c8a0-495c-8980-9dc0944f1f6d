<?php

namespace Tests\Browser\Admin;

use App\Models\User;
use App\Models\ContactSubmission;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class ContactSubmissionsTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test contact submissions index page loads correctly
     */
    public function test_contact_submissions_index_loads()
    {
        ContactSubmission::factory()->count(5)->create();

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->assertSee('Contact Submissions Management')
                    ->assertSee('Search & Filter')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test viewing contact submission details
     */
    public function test_view_contact_submission_details()
    {
        $contact = ContactSubmission::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'This is a test message',
            'status' => 'new',
        ]);

        $this->browse(function (Browser $browser) use ($contact) {
            $browser->loginAs($this->adminUser)
                    ->visit("/admin/contacts/{$contact->id}")
                    ->assertSee('Contact Submission Details')
                    ->assertSee('John Doe')
                    ->assertSee('<EMAIL>')
                    ->assertSee('Test Subject')
                    ->assertSee('This is a test message')
                    ->assertSee('New');
        });
    }

    /**
     * Test marking contact as replied
     */
    public function test_mark_contact_as_replied()
    {
        $contact = ContactSubmission::factory()->create([
            'status' => 'new',
        ]);

        $this->browse(function (Browser $browser) use ($contact) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->press('button[data-tippy-content="Mark as Replied"]')
                    ->assertSee('Contact submission marked as replied')
                    ->assertSee('Replied');
        });

        $this->assertDatabaseHas('contact_submissions', [
            'id' => $contact->id,
            'status' => 'replied',
        ]);
    }

    /**
     * Test archiving contact submission
     */
    public function test_archive_contact_submission()
    {
        $contact = ContactSubmission::factory()->create([
            'status' => 'read',
        ]);

        $this->browse(function (Browser $browser) use ($contact) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->press('button[data-tippy-content="Archive"]')
                    ->assertSee('Contact submission archived successfully')
                    ->assertSee('Archived');
        });

        $this->assertDatabaseHas('contact_submissions', [
            'id' => $contact->id,
            'status' => 'archived',
        ]);
    }

    /**
     * Test updating contact submission with admin notes
     */
    public function test_update_contact_with_admin_notes()
    {
        $contact = ContactSubmission::factory()->create([
            'status' => 'new',
            'admin_notes' => null,
        ]);

        $this->browse(function (Browser $browser) use ($contact) {
            $browser->loginAs($this->adminUser)
                    ->visit("/admin/contacts/{$contact->id}")
                    ->select('status', 'read')
                    ->type('admin_notes', 'This is an admin note')
                    ->press('Update')
                    ->assertSee('Contact submission updated successfully');
        });

        $this->assertDatabaseHas('contact_submissions', [
            'id' => $contact->id,
            'status' => 'read',
            'admin_notes' => 'This is an admin note',
        ]);
    }

    /**
     * Test search functionality
     */
    public function test_search_contact_submissions()
    {
        ContactSubmission::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Unique Subject',
        ]);

        ContactSubmission::factory()->create([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'subject' => 'Different Subject',
        ]);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->type('q', 'Unique Subject')
                    ->press('Search')
                    ->assertSee('John Doe')
                    ->assertSee('Unique Subject')
                    ->assertDontSee('Jane Smith');
        });
    }

    /**
     * Test status filtering
     */
    public function test_filter_by_status()
    {
        ContactSubmission::factory()->create(['status' => 'new']);
        ContactSubmission::factory()->create(['status' => 'replied']);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->select('status', 'new')
                    ->press('Search')
                    ->assertSee('New')
                    ->assertDontSee('Replied');
        });
    }

    /**
     * Test deleting contact submission
     */
    public function test_delete_contact_submission()
    {
        $contact = ContactSubmission::factory()->create([
            'name' => 'Test Contact',
        ]);

        $this->browse(function (Browser $browser) use ($contact) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->assertSee('Test Contact')
                    ->press('button[data-tippy-content="Delete"]')
                    ->acceptDialog()
                    ->assertSee('Contact submission deleted successfully')
                    ->assertDontSee('Test Contact');
        });

        $this->assertDatabaseMissing('contact_submissions', [
            'id' => $contact->id,
        ]);
    }

    /**
     * Test responsive design on mobile
     */
    public function test_contact_submissions_responsive_mobile()
    {
        ContactSubmission::factory()->create();

        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE
                    ->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->assertSee('Contact Submissions Management')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test responsive design on tablet
     */
    public function test_contact_submissions_responsive_tablet()
    {
        ContactSubmission::factory()->create();

        $this->browse(function (Browser $browser) {
            $browser->resize(768, 1024) // iPad
                    ->loginAs($this->adminUser)
                    ->visit('/admin/contacts')
                    ->assertSee('Contact Submissions Management')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test analyst can view but not modify
     */
    public function test_analyst_can_view_but_not_modify()
    {
        $analystUser = User::factory()->create([
            'role' => 'analyst',
            'email' => '<EMAIL>',
        ]);

        $contact = ContactSubmission::factory()->create();

        $this->browse(function (Browser $browser) use ($analystUser, $contact) {
            $browser->loginAs($analystUser)
                    ->visit('/admin/contacts')
                    ->assertSee('Contact Submissions Management')
                    ->visit("/admin/contacts/{$contact->id}")
                    ->assertSee('Contact Submission Details')
                    ->assertMissing('button[data-tippy-content="Mark as Replied"]')
                    ->assertMissing('button[data-tippy-content="Archive"]')
                    ->assertMissing('button[data-tippy-content="Delete"]');
        });
    }
}
