<?php

namespace Tests\Browser\Admin;

use App\Models\User;
use App\Models\CustomerReview;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class CustomerReviewsTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test customer reviews index page loads correctly
     */
    public function test_customer_reviews_index_loads()
    {
        CustomerReview::factory()->count(5)->create();

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->assertSee('Customer Reviews Management')
                    ->assertSee('Create Review')
                    ->assertSee('Search & Filter')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test creating a new customer review
     */
    public function test_create_customer_review()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews/create')
                    ->assertSee('Create Customer Review')
                    ->type('customer_name', 'John Doe')
                    ->type('designation', 'CEO')
                    ->type('company', 'Tech Corp')
                    ->type('message', 'This is an excellent service!')
                    ->select('rating', '5')
                    ->type('sort_order', '1')
                    ->check('is_featured')
                    ->check('is_active')
                    ->press('Create Review')
                    ->assertPathIs('/admin/reviews')
                    ->assertSee('Customer review created successfully')
                    ->assertSee('John Doe');
        });

        $this->assertDatabaseHas('customer_reviews', [
            'customer_name' => 'John Doe',
            'designation' => 'CEO',
            'company' => 'Tech Corp',
            'message' => 'This is an excellent service!',
            'rating' => 5,
            'is_featured' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Test editing a customer review
     */
    public function test_edit_customer_review()
    {
        $review = CustomerReview::factory()->create([
            'customer_name' => 'Jane Smith',
            'designation' => 'CTO',
            'company' => 'Original Corp',
            'message' => 'Original message',
            'rating' => 4,
            'is_featured' => false,
        ]);

        $this->browse(function (Browser $browser) use ($review) {
            $browser->loginAs($this->adminUser)
                    ->visit("/admin/reviews/{$review->id}/edit")
                    ->assertSee('Edit Customer Review')
                    ->type('customer_name', 'Jane Doe')
                    ->type('company', 'Updated Corp')
                    ->type('message', 'Updated message')
                    ->select('rating', '5')
                    ->check('is_featured')
                    ->press('Update Review')
                    ->assertPathIs('/admin/reviews')
                    ->assertSee('Customer review updated successfully')
                    ->assertSee('Jane Doe')
                    ->assertSee('Updated Corp');
        });

        $this->assertDatabaseHas('customer_reviews', [
            'id' => $review->id,
            'customer_name' => 'Jane Doe',
            'company' => 'Updated Corp',
            'message' => 'Updated message',
            'rating' => 5,
            'is_featured' => true,
        ]);
    }

    /**
     * Test toggling featured status
     */
    public function test_toggle_featured_status()
    {
        $review = CustomerReview::factory()->create([
            'is_featured' => false,
        ]);

        $this->browse(function (Browser $browser) use ($review) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->press('button[data-tippy-content="Feature"]')
                    ->assertSee('Review featured successfully');
        });

        $this->assertDatabaseHas('customer_reviews', [
            'id' => $review->id,
            'is_featured' => true,
        ]);
    }

    /**
     * Test toggling active status
     */
    public function test_toggle_active_status()
    {
        $review = CustomerReview::factory()->create([
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($review) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->press('button[data-tippy-content="Deactivate"]')
                    ->assertSee('Review deactivated successfully');
        });

        $this->assertDatabaseHas('customer_reviews', [
            'id' => $review->id,
            'is_active' => false,
        ]);
    }

    /**
     * Test search functionality
     */
    public function test_search_customer_reviews()
    {
        CustomerReview::factory()->create([
            'customer_name' => 'John Unique',
            'company' => 'Unique Corp',
        ]);

        CustomerReview::factory()->create([
            'customer_name' => 'Jane Common',
            'company' => 'Common Corp',
        ]);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->type('q', 'Unique')
                    ->press('Search')
                    ->assertSee('John Unique')
                    ->assertSee('Unique Corp')
                    ->assertDontSee('Jane Common');
        });
    }

    /**
     * Test featured filter
     */
    public function test_filter_by_featured()
    {
        CustomerReview::factory()->create(['is_featured' => true, 'customer_name' => 'Featured Customer']);
        CustomerReview::factory()->create(['is_featured' => false, 'customer_name' => 'Regular Customer']);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->select('featured', '1')
                    ->press('Search')
                    ->assertSee('Featured Customer')
                    ->assertDontSee('Regular Customer');
        });
    }

    /**
     * Test active status filter
     */
    public function test_filter_by_active_status()
    {
        CustomerReview::factory()->create(['is_active' => true, 'customer_name' => 'Active Customer']);
        CustomerReview::factory()->create(['is_active' => false, 'customer_name' => 'Inactive Customer']);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->select('active', '1')
                    ->press('Search')
                    ->assertSee('Active Customer')
                    ->assertDontSee('Inactive Customer');
        });
    }

    /**
     * Test deleting customer review
     */
    public function test_delete_customer_review()
    {
        $review = CustomerReview::factory()->create([
            'customer_name' => 'Test Customer',
        ]);

        $this->browse(function (Browser $browser) use ($review) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->assertSee('Test Customer')
                    ->press('button[data-tippy-content="Delete"]')
                    ->acceptDialog()
                    ->assertSee('Customer review deleted successfully')
                    ->assertDontSee('Test Customer');
        });

        $this->assertDatabaseMissing('customer_reviews', [
            'id' => $review->id,
        ]);
    }

    /**
     * Test responsive design on mobile
     */
    public function test_customer_reviews_responsive_mobile()
    {
        CustomerReview::factory()->create();

        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE
                    ->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->assertSee('Customer Reviews Management')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test responsive design on tablet
     */
    public function test_customer_reviews_responsive_tablet()
    {
        CustomerReview::factory()->create();

        $this->browse(function (Browser $browser) {
            $browser->resize(768, 1024) // iPad
                    ->loginAs($this->adminUser)
                    ->visit('/admin/reviews')
                    ->assertSee('Customer Reviews Management')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test access control - only admin/super-admin can access
     */
    public function test_customer_reviews_access_control()
    {
        $analystUser = User::factory()->create([
            'role' => 'analyst',
            'email' => '<EMAIL>',
        ]);

        $this->browse(function (Browser $browser) use ($analystUser) {
            $browser->loginAs($analystUser)
                    ->visit('/admin/reviews')
                    ->assertPathIsNot('/admin/reviews')
                    ->assertSee('403'); // Forbidden
        });
    }
}
