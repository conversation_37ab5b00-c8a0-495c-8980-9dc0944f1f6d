<?php

namespace Tests\Browser\Admin;

use App\Models\User;
use App\Models\LegalPage;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class LegalPagesTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test legal pages index page loads correctly
     */
    public function test_legal_pages_index_loads()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages')
                    ->assertSee('Legal Pages Management')
                    ->assertSee('Create Legal Page')
                    ->assertPresent('.card');
        });
    }

    /**
     * Test creating a new legal page
     */
    public function test_create_legal_page()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages/create')
                    ->assertSee('Create Legal Page')
                    ->select('type', 'terms-and-conditions')
                    ->type('title', 'Terms and Conditions')
                    ->type('#content', '<h1>Terms and Conditions</h1><p>These are our terms...</p>')
                    ->check('is_active')
                    ->press('Create Legal Page')
                    ->assertPathIs('/admin/legal-pages')
                    ->assertSee('Legal page created successfully')
                    ->assertSee('Terms and Conditions');
        });

        $this->assertDatabaseHas('legal_pages', [
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
            'is_active' => true,
        ]);
    }

    /**
     * Test editing a legal page
     */
    public function test_edit_legal_page()
    {
        $legalPage = LegalPage::factory()->create([
            'type' => 'privacy-policy',
            'title' => 'Privacy Policy',
            'content' => '<p>Original content</p>',
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($legalPage) {
            $browser->loginAs($this->adminUser)
                    ->visit("/admin/legal-pages/{$legalPage->id}/edit")
                    ->assertSee('Edit Privacy Policy')
                    ->type('title', 'Updated Privacy Policy')
                    ->type('#content', '<h1>Updated Privacy Policy</h1><p>Updated content...</p>')
                    ->press('Update Legal Page')
                    ->assertPathIs('/admin/legal-pages')
                    ->assertSee('Legal page updated successfully')
                    ->assertSee('Updated Privacy Policy');
        });

        $this->assertDatabaseHas('legal_pages', [
            'id' => $legalPage->id,
            'title' => 'Updated Privacy Policy',
        ]);
    }

    /**
     * Test toggling legal page status
     */
    public function test_toggle_legal_page_status()
    {
        $legalPage = LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($legalPage) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages')
                    ->assertSee('Active')
                    ->press('button[data-tippy-content="Deactivate"]')
                    ->assertSee('Legal page deactivated successfully')
                    ->assertSee('Inactive');
        });

        $this->assertDatabaseHas('legal_pages', [
            'id' => $legalPage->id,
            'is_active' => false,
        ]);
    }

    /**
     * Test deleting a legal page
     */
    public function test_delete_legal_page()
    {
        $legalPage = LegalPage::factory()->create([
            'type' => 'privacy-policy',
            'title' => 'Privacy Policy',
        ]);

        $this->browse(function (Browser $browser) use ($legalPage) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages')
                    ->assertSee('Privacy Policy')
                    ->press('button[data-tippy-content="Delete"]')
                    ->acceptDialog()
                    ->assertSee('Legal page deleted successfully')
                    ->assertDontSee('Privacy Policy');
        });

        $this->assertDatabaseMissing('legal_pages', [
            'id' => $legalPage->id,
        ]);
    }

    /**
     * Test responsive design on mobile
     */
    public function test_legal_pages_responsive_mobile()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE
                    ->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages')
                    ->assertSee('Legal Pages Management')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test responsive design on tablet
     */
    public function test_legal_pages_responsive_tablet()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(768, 1024) // iPad
                    ->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages')
                    ->assertSee('Legal Pages Management')
                    ->assertPresent('.card')
                    ->assertPresent('table');
        });
    }

    /**
     * Test access control - only admin/super-admin can access
     */
    public function test_legal_pages_access_control()
    {
        $analystUser = User::factory()->create([
            'role' => 'analyst',
            'email' => '<EMAIL>',
        ]);

        $this->browse(function (Browser $browser) use ($analystUser) {
            $browser->loginAs($analystUser)
                    ->visit('/admin/legal-pages')
                    ->assertPathIsNot('/admin/legal-pages')
                    ->assertSee('403'); // Forbidden
        });
    }
}
