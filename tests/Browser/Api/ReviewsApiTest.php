<?php

namespace Tests\Browser\Api;

use App\Models\CustomerReview;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class ReviewsApiTest extends DuskTestCase
{
    use DatabaseTransactions;

    /**
     * Test getting all active reviews
     */
    public function test_get_all_active_reviews()
    {
        // Create active reviews
        CustomerReview::factory()->count(3)->create([
            'is_active' => true,
            'is_featured' => false,
        ]);

        // Create inactive review (should not appear)
        CustomerReview::factory()->create([
            'is_active' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/reviews')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertEquals('Customer reviews retrieved successfully', $json['message']);
            $this->assertCount(3, $json['data']['data']); // Only active reviews
            
            // Check pagination structure
            $this->assertArrayHasKey('pagination', $json['data']);
            $this->assertArrayHasKey('current_page', $json['data']['pagination']);
            $this->assertArrayHasKey('total', $json['data']['pagination']);
        });
    }

    /**
     * Test getting featured reviews only
     */
    public function test_get_featured_reviews_only()
    {
        // Create featured reviews
        CustomerReview::factory()->count(2)->create([
            'is_active' => true,
            'is_featured' => true,
        ]);

        // Create non-featured review
        CustomerReview::factory()->create([
            'is_active' => true,
            'is_featured' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/reviews?featured=true')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertCount(2, $json['data']['data']); // Only featured reviews
            
            // Verify all returned reviews are featured
            foreach ($json['data']['data'] as $review) {
                $this->assertTrue($review['is_featured']);
            }
        });
    }

    /**
     * Test featured reviews endpoint
     */
    public function test_featured_reviews_endpoint()
    {
        // Create featured reviews
        CustomerReview::factory()->count(2)->create([
            'is_active' => true,
            'is_featured' => true,
        ]);

        // Create non-featured review
        CustomerReview::factory()->create([
            'is_active' => true,
            'is_featured' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/reviews/featured')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertEquals('Featured customer reviews retrieved successfully', $json['message']);
            $this->assertCount(2, $json['data']['data']); // Only featured reviews
        });
    }

    /**
     * Test pagination functionality
     */
    public function test_reviews_pagination()
    {
        // Create 15 reviews
        CustomerReview::factory()->count(15)->create([
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/reviews?per_page=5')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertCount(5, $json['data']['data']); // 5 per page
            $this->assertEquals(1, $json['data']['pagination']['current_page']);
            $this->assertEquals(3, $json['data']['pagination']['last_page']); // 15/5 = 3 pages
            $this->assertEquals(15, $json['data']['pagination']['total']);
        });
    }

    /**
     * Test review data structure
     */
    public function test_review_data_structure()
    {
        $review = CustomerReview::factory()->create([
            'customer_name' => 'John Doe',
            'designation' => 'CEO',
            'company' => 'Tech Corp',
            'message' => 'Great service!',
            'rating' => 5,
            'is_active' => true,
            'is_featured' => true,
            'sort_order' => 1,
        ]);

        $this->browse(function (Browser $browser) use ($review) {
            $response = $browser->visit('/api/reviews')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $reviewData = $json['data']['data'][0];
            
            // Check all required fields are present
            $this->assertArrayHasKey('id', $reviewData);
            $this->assertArrayHasKey('customer_name', $reviewData);
            $this->assertArrayHasKey('designation', $reviewData);
            $this->assertArrayHasKey('company', $reviewData);
            $this->assertArrayHasKey('formatted_designation', $reviewData);
            $this->assertArrayHasKey('message', $reviewData);
            $this->assertArrayHasKey('rating', $reviewData);
            $this->assertArrayHasKey('rating_stars', $reviewData);
            $this->assertArrayHasKey('is_featured', $reviewData);
            $this->assertArrayHasKey('sort_order', $reviewData);
            $this->assertArrayHasKey('avatar_url', $reviewData);
            $this->assertArrayHasKey('avatar_thumb_url', $reviewData);
            $this->assertArrayHasKey('avatar_medium_url', $reviewData);
            $this->assertArrayHasKey('created_at', $reviewData);
            
            // Verify data values
            $this->assertEquals('John Doe', $reviewData['customer_name']);
            $this->assertEquals('CEO', $reviewData['designation']);
            $this->assertEquals('Tech Corp', $reviewData['company']);
            $this->assertEquals('Great service!', $reviewData['message']);
            $this->assertEquals(5, $reviewData['rating']);
            $this->assertTrue($reviewData['is_featured']);
            $this->assertEquals(1, $reviewData['sort_order']);
        });
    }

    /**
     * Test reviews are ordered correctly
     */
    public function test_reviews_ordering()
    {
        // Create reviews with different sort orders
        CustomerReview::factory()->create([
            'customer_name' => 'Third Review',
            'sort_order' => 3,
            'is_active' => true,
        ]);

        CustomerReview::factory()->create([
            'customer_name' => 'First Review',
            'sort_order' => 1,
            'is_active' => true,
        ]);

        CustomerReview::factory()->create([
            'customer_name' => 'Second Review',
            'sort_order' => 2,
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/reviews')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $reviews = $json['data']['data'];
            
            // Verify ordering by sort_order
            $this->assertEquals('First Review', $reviews[0]['customer_name']);
            $this->assertEquals('Second Review', $reviews[1]['customer_name']);
            $this->assertEquals('Third Review', $reviews[2]['customer_name']);
        });
    }

    /**
     * Test rating stars array
     */
    public function test_rating_stars_array()
    {
        $review = CustomerReview::factory()->create([
            'rating' => 3,
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($review) {
            $response = $browser->visit('/api/reviews')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $reviewData = $json['data']['data'][0];
            $ratingStars = $reviewData['rating_stars'];
            
            // Should have 5 elements (for 5 stars)
            $this->assertCount(5, $ratingStars);
            
            // First 3 should be true, last 2 should be false
            $this->assertTrue($ratingStars[0]);
            $this->assertTrue($ratingStars[1]);
            $this->assertTrue($ratingStars[2]);
            $this->assertFalse($ratingStars[3]);
            $this->assertFalse($ratingStars[4]);
        });
    }

    /**
     * Test formatted designation
     */
    public function test_formatted_designation()
    {
        $review = CustomerReview::factory()->create([
            'designation' => 'CEO',
            'company' => 'Tech Corp',
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($review) {
            $response = $browser->visit('/api/reviews')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $reviewData = $json['data']['data'][0];
            
            $this->assertEquals('CEO at Tech Corp', $reviewData['formatted_designation']);
        });
    }

    /**
     * Test empty reviews response
     */
    public function test_empty_reviews_response()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/reviews')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertEquals('Customer reviews retrieved successfully', $json['message']);
            $this->assertCount(0, $json['data']['data']);
            $this->assertEquals(0, $json['data']['pagination']['total']);
        });
    }

    /**
     * Test API handles invalid per_page parameter
     */
    public function test_invalid_per_page_parameter()
    {
        CustomerReview::factory()->count(5)->create(['is_active' => true]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/reviews?per_page=invalid')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            // Should default to 10 per page
            $this->assertEquals('success', $json['status']);
            $this->assertCount(5, $json['data']['data']);
        });
    }
}
