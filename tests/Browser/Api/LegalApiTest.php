<?php

namespace Tests\Browser\Api;

use App\Models\LegalPage;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class LegalApiTest extends DuskTestCase
{
    use DatabaseTransactions;

    /**
     * Test getting terms and conditions via API
     */
    public function test_get_terms_and_conditions_api()
    {
        $termsPage = LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
            'content' => '<h1>Terms and Conditions</h1><p>These are our terms...</p>',
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($termsPage) {
            $response = $browser->visit('/api/legal/terms-and-conditions')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertEquals('Terms and conditions retrieved successfully', $json['message']);
            $this->assertEquals('Terms and Conditions', $json['data']['title']);
            $this->assertStringContainsString('These are our terms', $json['data']['content']);
        });
    }

    /**
     * Test getting privacy policy via API
     */
    public function test_get_privacy_policy_api()
    {
        $privacyPage = LegalPage::factory()->create([
            'type' => 'privacy-policy',
            'title' => 'Privacy Policy',
            'content' => '<h1>Privacy Policy</h1><p>This is our privacy policy...</p>',
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($privacyPage) {
            $response = $browser->visit('/api/legal/privacy-policy')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertEquals('Privacy policy retrieved successfully', $json['message']);
            $this->assertEquals('Privacy Policy', $json['data']['title']);
            $this->assertStringContainsString('This is our privacy policy', $json['data']['content']);
        });
    }

    /**
     * Test getting all legal pages via API
     */
    public function test_get_all_legal_pages_api()
    {
        LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
            'is_active' => true,
        ]);

        LegalPage::factory()->create([
            'type' => 'privacy-policy',
            'title' => 'Privacy Policy',
            'is_active' => true,
        ]);

        // Create inactive page that should not appear
        LegalPage::factory()->create([
            'type' => 'inactive-page',
            'title' => 'Inactive Page',
            'is_active' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/legal')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertEquals('Legal pages retrieved successfully', $json['message']);
            $this->assertCount(2, $json['data']); // Only active pages
            
            $types = array_column($json['data'], 'type');
            $this->assertContains('terms-and-conditions', $types);
            $this->assertContains('privacy-policy', $types);
            $this->assertNotContains('inactive-page', $types);
        });
    }

    /**
     * Test 404 response when legal page not found
     */
    public function test_legal_page_not_found()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/legal/terms-and-conditions')
                    ->assertStatus(404);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('error', $json['status']);
            $this->assertEquals('Terms and conditions not found', $json['message']);
        });
    }

    /**
     * Test inactive legal page returns 404
     */
    public function test_inactive_legal_page_returns_404()
    {
        LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
            'is_active' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/legal/terms-and-conditions')
                    ->assertStatus(404);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('error', $json['status']);
            $this->assertEquals('Terms and conditions not found', $json['message']);
        });
    }

    /**
     * Test API response includes last_updated timestamp
     */
    public function test_api_includes_last_updated_timestamp()
    {
        $termsPage = LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
            'content' => '<p>Content</p>',
            'is_active' => true,
            'last_updated_at' => now(),
        ]);

        $this->browse(function (Browser $browser) use ($termsPage) {
            $response = $browser->visit('/api/legal/terms-and-conditions')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertArrayHasKey('last_updated', $json['data']);
            $this->assertNotNull($json['data']['last_updated']);
        });
    }

    /**
     * Test API response structure
     */
    public function test_api_response_structure()
    {
        LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Terms and Conditions',
            'content' => '<p>Content</p>',
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/legal/terms-and-conditions')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            // Check response structure
            $this->assertArrayHasKey('status', $json);
            $this->assertArrayHasKey('message', $json);
            $this->assertArrayHasKey('data', $json);
            
            // Check data structure
            $this->assertArrayHasKey('title', $json['data']);
            $this->assertArrayHasKey('content', $json['data']);
            $this->assertArrayHasKey('last_updated', $json['data']);
        });
    }
}
