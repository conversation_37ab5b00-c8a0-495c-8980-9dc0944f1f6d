<?php

namespace Tests\Browser\Api;

use App\Models\ContactSubmission;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class ContactApiTest extends DuskTestCase
{
    use DatabaseTransactions;

    /**
     * Test successful contact form submission
     */
    public function test_successful_contact_form_submission()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->type('name', '<PERSON>')
                    ->type('email', '<EMAIL>')
                    ->type('phone', '+1234567890')
                    ->type('subject', 'Test Subject')
                    ->type('message', 'This is a test message for the contact form.')
                    ->press('Submit')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
            $this->assertEquals('Contact form submitted successfully. We will get back to you soon.', $json['message']);
            $this->assertArrayHasKey('submission_id', $json['data']);
            $this->assertArrayHasKey('submitted_at', $json['data']);
        });

        $this->assertDatabaseHas('contact_submissions', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'subject' => 'Test Subject',
            'message' => 'This is a test message for the contact form.',
            'status' => 'new',
        ]);
    }

    /**
     * Test contact form submission without phone (optional field)
     */
    public function test_contact_form_submission_without_phone()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->type('name', 'Jane Doe')
                    ->type('email', '<EMAIL>')
                    ->type('subject', 'Test Subject')
                    ->type('message', 'This is a test message without phone.')
                    ->press('Submit')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('success', $json['status']);
        });

        $this->assertDatabaseHas('contact_submissions', [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone' => null,
            'subject' => 'Test Subject',
            'message' => 'This is a test message without phone.',
        ]);
    }

    /**
     * Test validation errors for required fields
     */
    public function test_contact_form_validation_errors()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->press('Submit')
                    ->assertStatus(422);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('error', $json['status']);
            $this->assertArrayHasKey('errors', $json);
            $this->assertArrayHasKey('name', $json['errors']);
            $this->assertArrayHasKey('email', $json['errors']);
            $this->assertArrayHasKey('subject', $json['errors']);
            $this->assertArrayHasKey('message', $json['errors']);
        });
    }

    /**
     * Test email validation
     */
    public function test_contact_form_email_validation()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->type('name', 'John Doe')
                    ->type('email', 'invalid-email')
                    ->type('subject', 'Test Subject')
                    ->type('message', 'Test message')
                    ->press('Submit')
                    ->assertStatus(422);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('error', $json['status']);
            $this->assertArrayHasKey('errors', $json);
            $this->assertArrayHasKey('email', $json['errors']);
        });
    }

    /**
     * Test message length validation
     */
    public function test_contact_form_message_length_validation()
    {
        $longMessage = str_repeat('This is a very long message. ', 100); // Over 2000 chars

        $this->browse(function (Browser $browser) use ($longMessage) {
            $response = $browser->visit('/api/contact')
                    ->type('name', 'John Doe')
                    ->type('email', '<EMAIL>')
                    ->type('subject', 'Test Subject')
                    ->type('message', $longMessage)
                    ->press('Submit')
                    ->assertStatus(422);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('error', $json['status']);
            $this->assertArrayHasKey('errors', $json);
            $this->assertArrayHasKey('message', $json['errors']);
        });
    }

    /**
     * Test rate limiting functionality
     */
    public function test_contact_form_rate_limiting()
    {
        // Submit 5 forms quickly (the limit)
        for ($i = 0; $i < 5; $i++) {
            $this->browse(function (Browser $browser) use ($i) {
                $response = $browser->visit('/api/contact')
                        ->type('name', "User {$i}")
                        ->type('email', "user{$i}@example.com")
                        ->type('subject', "Subject {$i}")
                        ->type('message', "Message {$i}")
                        ->press('Submit')
                        ->assertStatus(200);
            });
        }

        // 6th submission should be rate limited
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->type('name', 'Rate Limited User')
                    ->type('email', '<EMAIL>')
                    ->type('subject', 'Rate Limited Subject')
                    ->type('message', 'This should be rate limited')
                    ->press('Submit')
                    ->assertStatus(429);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            $this->assertEquals('error', $json['status']);
            $this->assertStringContainsString('Too many contact form submissions', $json['message']);
        });
    }

    /**
     * Test IP address and user agent are stored
     */
    public function test_contact_form_stores_ip_and_user_agent()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->type('name', 'John Doe')
                    ->type('email', '<EMAIL>')
                    ->type('subject', 'Test Subject')
                    ->type('message', 'Test message')
                    ->press('Submit')
                    ->assertStatus(200);
        });

        $contact = ContactSubmission::where('email', '<EMAIL>')->first();
        
        $this->assertNotNull($contact->ip_address);
        $this->assertNotNull($contact->user_agent);
    }

    /**
     * Test API response structure
     */
    public function test_contact_api_response_structure()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->type('name', 'John Doe')
                    ->type('email', '<EMAIL>')
                    ->type('subject', 'Test Subject')
                    ->type('message', 'Test message')
                    ->press('Submit')
                    ->assertStatus(200);

            $json = json_decode($browser->driver->getPageSource(), true);
            
            // Check response structure
            $this->assertArrayHasKey('status', $json);
            $this->assertArrayHasKey('message', $json);
            $this->assertArrayHasKey('data', $json);
            
            // Check data structure
            $this->assertArrayHasKey('submission_id', $json['data']);
            $this->assertArrayHasKey('submitted_at', $json['data']);
            
            // Verify data types
            $this->assertIsInt($json['data']['submission_id']);
            $this->assertIsString($json['data']['submitted_at']);
        });
    }

    /**
     * Test contact form with special characters
     */
    public function test_contact_form_with_special_characters()
    {
        $this->browse(function (Browser $browser) {
            $response = $browser->visit('/api/contact')
                    ->type('name', 'José María')
                    ->type('email', '<EMAIL>')
                    ->type('subject', 'Tëst Sübject with spëcial chars')
                    ->type('message', 'This message contains special characters: àáâãäåæçèéêë')
                    ->press('Submit')
                    ->assertStatus(200);
        });

        $this->assertDatabaseHas('contact_submissions', [
            'name' => 'José María',
            'email' => '<EMAIL>',
            'subject' => 'Tëst Sübject with spëcial chars',
        ]);
    }
}
