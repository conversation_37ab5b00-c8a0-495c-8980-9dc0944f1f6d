<?php

namespace Tests\Browser;

use App\Models\User;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class TaxonomyFixBrowserTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test that category creation works through the admin interface
     */
    public function test_category_creation_through_admin_interface()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/categories')
                    ->assertSee('Categories Management')
                    ->click('@create-category-btn')
                    ->assertSee('Create Category')
                    ->type('name', 'Test Category via Browser')
                    ->type('description', 'This category was created via browser test')
                    ->select('type', 'category')
                    ->press('Create Category')
                    ->assertPathIs('/admin/categories')
                    ->assertSee('Category created successfully')
                    ->assertSee('Test Category via Browser');
        });

        // Verify in database
        $this->assertDatabaseHas('taxonomies', [
            'name' => 'Test Category via Browser',
            'type' => 'category',
        ]);
    }

    /**
     * Test that subcategory creation works with parent selection
     */
    public function test_subcategory_creation_with_parent()
    {
        // Create parent category first
        $parent = Taxonomy::create([
            'name' => 'Parent Category',
            'type' => 'category',
            'description' => 'Parent for testing',
        ]);

        $this->browse(function (Browser $browser) use ($parent) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/categories/create')
                    ->type('name', 'Child Category via Browser')
                    ->type('description', 'Child category created via browser test')
                    ->select('type', 'category')
                    ->select('parent_id', $parent->id)
                    ->press('Create Category')
                    ->assertPathIs('/admin/categories')
                    ->assertSee('Category created successfully')
                    ->assertSee('Child Category via Browser');
        });

        // Verify in database
        $this->assertDatabaseHas('taxonomies', [
            'name' => 'Child Category via Browser',
            'type' => 'category',
            'parent_id' => $parent->id,
        ]);
    }

    /**
     * Test that category editing works correctly
     */
    public function test_category_editing_works()
    {
        $category = Taxonomy::create([
            'name' => 'Original Category Name',
            'type' => 'category',
            'description' => 'Original description',
        ]);

        $this->browse(function (Browser $browser) use ($category) {
            $browser->loginAs($this->adminUser)
                    ->visit("/admin/categories/{$category->id}/edit")
                    ->assertSee('Edit Category')
                    ->clear('name')
                    ->type('name', 'Updated Category Name')
                    ->clear('description')
                    ->type('description', 'Updated description via browser test')
                    ->press('Update Category')
                    ->assertPathIs('/admin/categories')
                    ->assertSee('Category updated successfully')
                    ->assertSee('Updated Category Name');
        });

        // Verify in database
        $this->assertDatabaseHas('taxonomies', [
            'id' => $category->id,
            'name' => 'Updated Category Name',
            'description' => 'Updated description via browser test',
        ]);
    }

    /**
     * Test that different taxonomy types can be created
     */
    public function test_different_taxonomy_types_creation()
    {
        $types = [
            'keyword' => 'Test Keyword',
            'industry' => 'Test Industry',
            'technology' => 'Test Technology',
        ];

        foreach ($types as $type => $name) {
            $this->browse(function (Browser $browser) use ($type, $name) {
                $browser->loginAs($this->adminUser)
                        ->visit('/admin/categories/create')
                        ->type('name', $name)
                        ->type('description', "Test {$type} description")
                        ->select('type', $type)
                        ->press('Create Category')
                        ->assertPathIs('/admin/categories')
                        ->assertSee('Category created successfully');
            });

            // Verify in database
            $this->assertDatabaseHas('taxonomies', [
                'name' => $name,
                'type' => $type,
            ]);
        }
    }

    /**
     * Test that validation errors are displayed correctly
     */
    public function test_validation_errors_display()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/categories/create')
                    ->press('Create Category') // Submit without required fields
                    ->assertSee('The name field is required')
                    ->assertSee('The type field is required');
        });
    }

    /**
     * Test that duplicate name validation works
     */
    public function test_duplicate_name_validation()
    {
        // Create existing category
        Taxonomy::create([
            'name' => 'Duplicate Test Category',
            'type' => 'category',
        ]);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/categories/create')
                    ->type('name', 'Duplicate Test Category')
                    ->type('description', 'This should fail')
                    ->select('type', 'category')
                    ->press('Create Category')
                    ->assertSee('The name has already been taken');
        });
    }

    /**
     * Test that circular reference prevention works
     */
    public function test_circular_reference_prevention()
    {
        $category = Taxonomy::create([
            'name' => 'Self Parent Test',
            'type' => 'category',
        ]);

        $this->browse(function (Browser $browser) use ($category) {
            $browser->loginAs($this->adminUser)
                    ->visit("/admin/categories/{$category->id}/edit")
                    ->select('parent_id', $category->id) // Try to set self as parent
                    ->press('Update Category')
                    ->assertSee('A category cannot be its own parent');
        });
    }

    /**
     * Test that the categories list displays correctly
     */
    public function test_categories_list_display()
    {
        // Create test categories
        $categories = [
            Taxonomy::create(['name' => 'Category 1', 'type' => 'category']),
            Taxonomy::create(['name' => 'Category 2', 'type' => 'category']),
            Taxonomy::create(['name' => 'Category 3', 'type' => 'category']),
        ];

        $this->browse(function (Browser $browser) use ($categories) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/categories')
                    ->assertSee('Categories Management');

            foreach ($categories as $category) {
                $browser->assertSee($category->name);
            }
        });
    }

    /**
     * Test that search functionality works
     */
    public function test_search_functionality()
    {
        // Create test categories
        Taxonomy::create(['name' => 'Searchable Category', 'type' => 'category']);
        Taxonomy::create(['name' => 'Another Category', 'type' => 'category']);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/categories')
                    ->type('search', 'Searchable')
                    ->press('Search')
                    ->assertSee('Searchable Category')
                    ->assertDontSee('Another Category');
        });
    }

    /**
     * Test that category deletion works
     */
    public function test_category_deletion()
    {
        $category = Taxonomy::create([
            'name' => 'Category to Delete',
            'type' => 'category',
        ]);

        $this->browse(function (Browser $browser) use ($category) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/categories')
                    ->assertSee('Category to Delete')
                    ->click("@delete-category-{$category->id}")
                    ->whenAvailable('.modal', function ($modal) {
                        $modal->press('Confirm Delete');
                    })
                    ->assertSee('Category deleted successfully')
                    ->assertDontSee('Category to Delete');
        });

        // Verify deletion in database
        $this->assertDatabaseMissing('taxonomies', [
            'id' => $category->id,
        ]);
    }
}
