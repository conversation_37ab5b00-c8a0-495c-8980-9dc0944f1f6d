<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\WebsiteSetting;
use App\Models\UserSubscription;
use App\Models\SubscriptionProduct;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class AdminDashboardEnhancementsTest extends DuskTestCase
{
    use DatabaseMigrations;

    public User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('secret'),
            'role' => 'admin',
            'email_verified_at' => now()
        ]);
    }

    /**
     * Test website settings management functionality
     */
    public function test_website_settings_management_workflow()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('http://impactintels.test/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'secret')
                    ->press('Sign In')
                    ->waitForLocation('/admin/dashboard')
                    
                    // Navigate to Website Settings
                    ->clickLink('Website Settings')
                    ->waitForText('Website Configuration')
                    ->clickLink('Website Configuration')
                    ->waitForLocation('/admin/website-settings')
                    
                    // Verify website settings dashboard
                    ->assertSee('Website Settings Management')
                    ->assertSee('Logo Management')
                    ->assertSee('Favicon Management')
                    ->assertSee('Email Settings')
                    
                    // Test Logo Management
                    ->clickLink('Manage Logos')
                    ->waitForLocation('/admin/website-settings/logo-management')
                    ->assertSee('Logo Management')
                    ->assertSee('Light Theme Header Logo')
                    ->assertSee('Dark Theme Header Logo')
                    ->assertSee('Light Theme Footer Logo')
                    ->assertSee('Dark Theme Footer Logo')
                    
                    // Go back and test Favicon Management
                    ->clickLink('Back to Settings')
                    ->waitForLocation('/admin/website-settings')
                    ->clickLink('Manage Favicons')
                    ->waitForLocation('/admin/website-settings/favicon-management')
                    ->assertSee('Favicon Management')
                    ->assertSee('ICO Format')
                    ->assertSee('PNG 16x16')
                    ->assertSee('PNG 32x32')
                    ->assertSee('PNG 192x192')
                    ->assertSee('SVG Format')
                    
                    // Go back and test Email Settings
                    ->clickLink('Back to Settings')
                    ->waitForLocation('/admin/website-settings')
                    ->clickLink('Configure Email')
                    ->waitForLocation('/admin/website-settings/email-settings')
                    ->assertSee('Email Settings')
                    ->assertSee('SMTP Configuration')
                    ->assertSee('Email Provider')
                    ->assertSee('Test Email');
        });
    }

    /**
     * Test enhanced dashboard analytics display
     */
    public function test_enhanced_dashboard_analytics_display()
    {
        // Create test data
        $this->createTestUsersAndSubscriptions();

        $this->browse(function (Browser $browser) {
            $browser->visit('http://impactintels.test/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'secret')
                    ->press('Sign In')
                    ->waitForLocation('/admin/dashboard')
                    
                    // Verify Role-based KPI Dashboard
                    ->assertSee('Role-based Analytics Dashboard')
                    ->assertSee('Investor Metrics')
                    ->assertSee('Startup Metrics')
                    ->assertSee('Consultant Metrics')
                    
                    // Verify KPI Cards
                    ->assertSee('Total Investor')
                    ->assertSee('Active Investor')
                    ->assertSee('Yearly Investor')
                    ->assertSee('Monthly Investor')
                    
                    ->assertSee('Total Startup')
                    ->assertSee('Active Startup')
                    ->assertSee('Yearly Startup')
                    ->assertSee('Monthly Startup')
                    
                    ->assertSee('Total Consultant')
                    ->assertSee('Active Consultant')
                    ->assertSee('Yearly Consultant')
                    ->assertSee('Monthly Consultant')
                    
                    // Verify Revenue & Subscription Analytics
                    ->assertSee('Revenue & Subscription Analytics')
                    ->assertSee('Monthly Subscription Revenue (TK)')
                    ->assertSee('Yearly Subscription Revenue (TK)')
                    ->assertSee('Monthly Package Distribution')
                    ->assertSee('Yearly Package Distribution');
        });
    }

    /**
     * Test charts rendering and responsiveness
     */
    public function test_charts_rendering_and_responsiveness()
    {
        $this->createTestUsersAndSubscriptions();

        $this->browse(function (Browser $browser) {
            $browser->visit('http://impactintels.test/admin/dashboard')
                    ->loginAs($this->admin)
                    
                    // Wait for charts to load
                    ->waitFor('#monthly-revenue-chart')
                    ->waitFor('#yearly-revenue-chart')
                    ->waitFor('#monthly-package-chart')
                    ->waitFor('#yearly-package-chart')
                    
                    // Test mobile responsiveness (375px)
                    ->resize(375, 667)
                    ->pause(1000)
                    ->assertVisible('#monthly-revenue-chart')
                    ->assertVisible('#yearly-revenue-chart')
                    
                    // Test tablet responsiveness (768px)
                    ->resize(768, 1024)
                    ->pause(1000)
                    ->assertVisible('#monthly-package-chart')
                    ->assertVisible('#yearly-package-chart')
                    
                    // Test desktop responsiveness (1280px+)
                    ->resize(1280, 720)
                    ->pause(1000)
                    ->assertVisible('#monthly-revenue-chart')
                    ->assertVisible('#yearly-revenue-chart')
                    ->assertVisible('#monthly-package-chart')
                    ->assertVisible('#yearly-package-chart');
        });
    }

    /**
     * Test cross-browser compatibility
     */
    public function test_cross_browser_compatibility()
    {
        $this->createTestUsersAndSubscriptions();

        // Test in Chrome
        $this->browse(function (Browser $browser) {
            $browser->visit('http://impactintels.test/admin/dashboard')
                    ->loginAs($this->admin)
                    ->waitFor('#monthly-revenue-chart')
                    ->assertSee('Role-based Analytics Dashboard')
                    ->assertSee('Revenue & Subscription Analytics');
        });
    }

    /**
     * Test website settings form validation
     */
    public function test_website_settings_form_validation()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('http://impactintels.test/admin/website-settings/email-settings')
                    ->loginAs($this->admin)
                    
                    // Test email settings validation
                    ->type('mail_from_address', 'invalid-email')
                    ->press('Update Email Settings')
                    ->waitForText('The mail from address must be a valid email address')
                    
                    // Test valid email settings
                    ->type('mail_driver', 'smtp')
                    ->type('mail_host', 'smtp.gmail.com')
                    ->type('mail_port', '587')
                    ->type('mail_from_address', '<EMAIL>')
                    ->type('mail_from_name', 'Test Application')
                    ->press('Update Email Settings')
                    ->waitForText('Email settings updated successfully');
        });
    }

    /**
     * Test dashboard loading performance
     */
    public function test_dashboard_loading_performance()
    {
        $this->createTestUsersAndSubscriptions();

        $this->browse(function (Browser $browser) {
            $startTime = microtime(true);
            
            $browser->visit('http://impactintels.test/admin/dashboard')
                    ->loginAs($this->admin)
                    ->waitFor('#monthly-revenue-chart')
                    ->waitFor('#yearly-revenue-chart')
                    ->waitFor('#monthly-package-chart')
                    ->waitFor('#yearly-package-chart');
            
            $loadTime = microtime(true) - $startTime;
            
            // Assert that dashboard loads within 5 seconds
            $this->assertLessThan(5, $loadTime, 'Dashboard should load within 5 seconds');
        });
    }

    /**
     * Test TK currency formatting in charts
     */
    public function test_tk_currency_formatting()
    {
        $this->createTestUsersAndSubscriptions();

        $this->browse(function (Browser $browser) {
            $browser->visit('http://impactintels.test/admin/dashboard')
                    ->loginAs($this->admin)
                    ->waitFor('#monthly-revenue-chart')
                    
                    // Check that TK currency is displayed
                    ->assertSeeIn('.card-body', 'TK')
                    ->assertSeeIn('.card-body', 'Revenue (TK)')
                    
                    // Verify chart tooltips show TK formatting (would need JavaScript execution)
                    ->script('return document.querySelector("#monthly-revenue-chart") !== null');
        });
    }

    /**
     * Create test users and subscriptions for testing
     */
    private function createTestUsersAndSubscriptions(): void
    {
        // Create test users
        $investor = User::create([
            'name' => 'Test Investor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);

        $startup = User::create([
            'name' => 'Test Startup',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'startup',
            'email_verified_at' => now()
        ]);

        $consultant = User::create([
            'name' => 'Test Consultant',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'analyst',
            'email_verified_at' => now()
        ]);

        // Create subscription product
        $product = SubscriptionProduct::create([
            'name' => 'Test Plan',
            'description' => 'Test subscription plan',
            'price' => 99.99,
            'billing_cycle' => 'monthly',
            'is_active' => true,
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123'
        ]);

        // Create active subscriptions
        UserSubscription::create([
            'user_id' => $investor->id,
            'subscription_product_id' => $product->id,
            'stripe_subscription_id' => 'sub_test_investor',
            'status' => 'active',
            'amount' => 99.99,
            'currency' => 'usd',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth()
        ]);
    }
}
