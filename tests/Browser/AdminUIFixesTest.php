<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\LegalPage;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class AdminUIFixesTest extends DuskTestCase
{
    use DatabaseTransactions;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test that Quill.js editor loads correctly with CDN assets
     */
    public function test_quill_editor_loads_with_cdn_assets()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages/create')
                    ->assertSee('Create Legal Page')
                    ->assertPresent('#content-editor')
                    ->assertPresent('.ql-toolbar')
                    ->assertPresent('.ql-editor')
                    ->assertSee('Page Type')
                    ->assertSee('Content');
        });
    }

    /**
     * Test that Quill.js editor has toolbar features
     */
    public function test_quill_editor_has_toolbar_features()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages/create')
                    ->assertPresent('.ql-toolbar')
                    ->assertPresent('.ql-header')
                    ->assertPresent('.ql-bold')
                    ->assertPresent('.ql-italic')
                    ->assertPresent('.ql-list')
                    ->assertPresent('.ql-link');
        });
    }

    /**
     * Test that Quill.js editor works in edit view
     */
    public function test_quill_editor_works_in_edit_view()
    {
        $legalPage = LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Test Terms',
            'content' => '<h1>Test Content</h1><p>This is test content.</p>',
        ]);

        $this->browse(function (Browser $browser) use ($legalPage) {
            $browser->loginAs($this->adminUser)
                    ->visit("/admin/legal-pages/{$legalPage->id}/edit")
                    ->assertSee('Edit Terms And Conditions')
                    ->assertPresent('#content-editor')
                    ->assertPresent('.ql-toolbar')
                    ->assertPresent('.ql-editor')
                    ->assertSee('Test Terms');
        });
    }

    /**
     * Test that sidebar navigation is properly consolidated
     */
    public function test_sidebar_navigation_consolidation()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages')
                    ->assertSee('Website Settings')
                    ->assertSee('Content')
                    ->assertSee('Blog Management')
                    ->assertSee('FAQ Management')
                    ->assertSee('Legal Pages')
                    ->assertSee('Customer Reviews')
                    ->assertSee('Contact Submissions')
                    ->assertDontSee('CONTENT MANAGEMENT');
        });
    }

    /**
     * Test that content management routes are accessible
     */
    public function test_content_management_routes_accessible()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/blogs')
                    ->assertSee('Blog Management')
                    ->visit('/admin/faqs')
                    ->assertSee('FAQ Management')
                    ->visit('/admin/legal-pages')
                    ->assertSee('Legal Pages Management')
                    ->visit('/admin/reviews')
                    ->assertSee('Customer Reviews Management')
                    ->visit('/admin/contacts')
                    ->assertSee('Contact Submissions Management');
        });
    }

    /**
     * Test that Website Settings shows active state correctly
     */
    public function test_website_settings_active_state()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages')
                    ->assertSee('Website Settings')
                    ->visit('/admin/blogs')
                    ->assertSee('Website Settings');
        });
    }

    /**
     * Test responsive design on mobile
     */
    public function test_quill_editor_responsive_mobile()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE
                    ->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages/create')
                    ->assertSee('Create Legal Page')
                    ->assertPresent('#content-editor')
                    ->assertPresent('.ql-toolbar');
        });
    }

    /**
     * Test responsive design on tablet
     */
    public function test_quill_editor_responsive_tablet()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(768, 1024) // iPad
                    ->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages/create')
                    ->assertSee('Create Legal Page')
                    ->assertPresent('#content-editor')
                    ->assertPresent('.ql-toolbar');
        });
    }

    /**
     * Test that editor can be typed in
     */
    public function test_quill_editor_functionality()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages/create')
                    ->select('type', 'terms-and-conditions')
                    ->type('title', 'Test Legal Page')
                    ->click('.ql-editor')
                    ->type('.ql-editor', 'This is test content for the legal page.')
                    ->assertSee('This is test content');
        });
    }

    /**
     * Test form submission with Quill content
     */
    public function test_quill_editor_form_submission()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->adminUser)
                    ->visit('/admin/legal-pages/create')
                    ->select('type', 'privacy-policy')
                    ->type('title', 'Test Privacy Policy')
                    ->click('.ql-editor')
                    ->type('.ql-editor', 'This is test privacy policy content.')
                    ->check('is_active')
                    ->press('Create Legal Page')
                    ->assertPathIs('/admin/legal-pages')
                    ->assertSee('Legal page created successfully');
        });

        $this->assertDatabaseHas('legal_pages', [
            'type' => 'privacy-policy',
            'title' => 'Test Privacy Policy',
            'is_active' => true,
        ]);
    }
}
