#!/bin/bash

# Comprehensive Test Runner for Legal Pages, Contact System, and Customer Reviews
# This script runs all tests across different browsers and responsive breakpoints

echo "🚀 Starting Comprehensive Test Suite for Legal Pages, Contact System, and Customer Reviews"
echo "=============================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
BROWSERS=("chrome" "firefox" "safari" "edge")
BREAKPOINTS=("375,667" "768,1024" "1280,800" "1920,1080")
BREAKPOINT_NAMES=("Mobile (iPhone SE)" "Tablet (iPad)" "Desktop (1280px)" "Large Desktop (1920px)")

# Function to run tests for a specific browser and breakpoint
run_test_suite() {
    local browser=$1
    local breakpoint=$2
    local breakpoint_name=$3
    
    echo -e "${BLUE}Testing on $browser - $breakpoint_name${NC}"
    
    # Set browser environment
    export DUSK_BROWSER=$browser
    export DUSK_WINDOW_SIZE=$breakpoint
    
    # Run admin tests
    echo "  📋 Running Legal Pages Admin Tests..."
    php artisan dusk tests/Browser/Admin/LegalPagesTest.php --stop-on-failure
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Legal Pages Admin Tests Failed on $browser - $breakpoint_name${NC}"
        return 1
    fi
    
    echo "  📧 Running Contact Submissions Admin Tests..."
    php artisan dusk tests/Browser/Admin/ContactSubmissionsTest.php --stop-on-failure
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Contact Submissions Admin Tests Failed on $browser - $breakpoint_name${NC}"
        return 1
    fi
    
    echo "  ⭐ Running Customer Reviews Admin Tests..."
    php artisan dusk tests/Browser/Admin/CustomerReviewsTest.php --stop-on-failure
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Customer Reviews Admin Tests Failed on $browser - $breakpoint_name${NC}"
        return 1
    fi
    
    # Run API tests
    echo "  🔗 Running Legal API Tests..."
    php artisan dusk tests/Browser/Api/LegalApiTest.php --stop-on-failure
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Legal API Tests Failed on $browser - $breakpoint_name${NC}"
        return 1
    fi
    
    echo "  📞 Running Contact API Tests..."
    php artisan dusk tests/Browser/Api/ContactApiTest.php --stop-on-failure
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Contact API Tests Failed on $browser - $breakpoint_name${NC}"
        return 1
    fi
    
    echo "  💬 Running Reviews API Tests..."
    php artisan dusk tests/Browser/Api/ReviewsApiTest.php --stop-on-failure
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Reviews API Tests Failed on $browser - $breakpoint_name${NC}"
        return 1
    fi
    
    echo -e "${GREEN}  ✅ All tests passed on $browser - $breakpoint_name${NC}"
    return 0
}

# Function to run unit tests
run_unit_tests() {
    echo -e "${BLUE}Running Unit Tests...${NC}"
    
    # Run model tests
    php artisan test --filter=LegalPageTest
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Legal Page Unit Tests Failed${NC}"
        return 1
    fi
    
    php artisan test --filter=ContactSubmissionTest
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Contact Submission Unit Tests Failed${NC}"
        return 1
    fi
    
    php artisan test --filter=CustomerReviewTest
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Customer Review Unit Tests Failed${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All Unit Tests Passed${NC}"
    return 0
}

# Function to run feature tests
run_feature_tests() {
    echo -e "${BLUE}Running Feature Tests...${NC}"
    
    # Run API feature tests
    php artisan test --filter=LegalApiFeatureTest
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Legal API Feature Tests Failed${NC}"
        return 1
    fi
    
    php artisan test --filter=ContactApiFeatureTest
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Contact API Feature Tests Failed${NC}"
        return 1
    fi
    
    php artisan test --filter=ReviewApiFeatureTest
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Review API Feature Tests Failed${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All Feature Tests Passed${NC}"
    return 0
}

# Main execution
main() {
    # Check if Laravel Dusk is installed
    if ! php artisan dusk:install --help > /dev/null 2>&1; then
        echo -e "${RED}❌ Laravel Dusk is not installed. Please install it first.${NC}"
        exit 1
    fi
    
    # Prepare test environment
    echo -e "${YELLOW}🔧 Preparing test environment...${NC}"
    php artisan migrate:fresh --seed --env=testing
    php artisan config:clear
    php artisan cache:clear
    
    # Run unit tests first
    echo -e "\n${YELLOW}📋 Step 1: Running Unit Tests${NC}"
    run_unit_tests
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Unit tests failed. Stopping execution.${NC}"
        exit 1
    fi
    
    # Run feature tests
    echo -e "\n${YELLOW}🔧 Step 2: Running Feature Tests${NC}"
    run_feature_tests
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Feature tests failed. Stopping execution.${NC}"
        exit 1
    fi
    
    # Run browser tests across all browsers and breakpoints
    echo -e "\n${YELLOW}🌐 Step 3: Running Browser Tests Across All Browsers and Breakpoints${NC}"
    
    failed_tests=()
    total_combinations=$((${#BROWSERS[@]} * ${#BREAKPOINTS[@]}))
    current_combination=0
    
    for browser in "${BROWSERS[@]}"; do
        for i in "${!BREAKPOINTS[@]}"; do
            current_combination=$((current_combination + 1))
            breakpoint="${BREAKPOINTS[$i]}"
            breakpoint_name="${BREAKPOINT_NAMES[$i]}"
            
            echo -e "\n${YELLOW}Testing combination $current_combination/$total_combinations${NC}"
            
            run_test_suite "$browser" "$breakpoint" "$breakpoint_name"
            if [ $? -ne 0 ]; then
                failed_tests+=("$browser - $breakpoint_name")
            fi
        done
    done
    
    # Summary
    echo -e "\n${BLUE}=============================================================================="
    echo -e "📊 TEST SUMMARY"
    echo -e "==============================================================================${NC}"
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED!"
        echo -e "✅ Legal Pages, Contact System, and Customer Reviews are working correctly"
        echo -e "✅ All browsers tested: ${BROWSERS[*]}"
        echo -e "✅ All breakpoints tested: Mobile, Tablet, Desktop, Large Desktop"
        echo -e "✅ Total test combinations: $total_combinations${NC}"
    else
        echo -e "${RED}❌ SOME TESTS FAILED!"
        echo -e "Failed combinations:${NC}"
        for failed in "${failed_tests[@]}"; do
            echo -e "${RED}  - $failed${NC}"
        done
        echo -e "${YELLOW}Please check the logs above for detailed error information.${NC}"
        exit 1
    fi
}

# Run the main function
main "$@"
