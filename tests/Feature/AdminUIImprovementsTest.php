<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\LegalPage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminUIImprovementsTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test that the sidebar navigation has been reorganized correctly
     */
    public function test_sidebar_navigation_reorganization()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check that Website Settings section exists
        $this->assertStringContainsString('Website Settings', $content);
        
        // Check that Content subsection exists under Website Settings
        $this->assertStringContainsString('Content', $content);
        
        // Check that Legal Pages, Customer Reviews, and Contact Submissions are accessible
        $this->assertStringContainsString('Legal Pages', $content);
    }

    /**
     * Test that legal pages create view includes Quill.js editor
     */
    public function test_legal_pages_create_includes_quill_editor()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for Quill.js editor elements
        $this->assertStringContainsString('quill-editor', $content);
        $this->assertStringContainsString('content-editor', $content);
        
        // Check for Quill.js assets
        $this->assertStringContainsString('quill.css', $content);
        $this->assertStringContainsString('quill.min.js', $content);
        
        // Check for enhanced toolbar options
        $this->assertStringContainsString('header', $content);
        $this->assertStringContainsString('bold', $content);
        $this->assertStringContainsString('italic', $content);
        $this->assertStringContainsString('link', $content);
        $this->assertStringContainsString('blockquote', $content);
        $this->assertStringContainsString('code-block', $content);
    }

    /**
     * Test that legal pages edit view includes Quill.js editor
     */
    public function test_legal_pages_edit_includes_quill_editor()
    {
        $this->actingAs($this->adminUser);

        $legalPage = LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Test Terms',
            'content' => '<h1>Test Content</h1><p>This is test content.</p>',
        ]);

        $response = $this->get(route('admin.legal-pages.edit', $legalPage));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for Quill.js editor elements
        $this->assertStringContainsString('quill-editor', $content);
        $this->assertStringContainsString('content-editor', $content);
        
        // Check for Quill.js assets
        $this->assertStringContainsString('quill.css', $content);
        $this->assertStringContainsString('quill.min.js', $content);
        
        // Check that existing content is loaded
        $this->assertStringContainsString('Test Content', $content);
    }

    /**
     * Test that Quill.js editor has full toolbar features
     */
    public function test_quill_editor_has_full_toolbar_features()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for comprehensive toolbar options
        $toolbarFeatures = [
            'header',           // Headings
            'bold',             // Bold text
            'italic',           // Italic text
            'underline',        // Underline text
            'strike',           // Strikethrough
            'list',             // Lists (ordered/bullet)
            'indent',           // Indentation
            'link',             // Links
            'align',            // Text alignment
            'color',            // Text color
            'background',       // Background color
            'blockquote',       // Blockquotes
            'code-block',       // Code blocks
            'clean'             // Remove formatting
        ];

        foreach ($toolbarFeatures as $feature) {
            $this->assertStringContainsString($feature, $content, 
                "Quill.js editor should include {$feature} in toolbar");
        }
    }

    /**
     * Test that the editor properly syncs with hidden textarea
     */
    public function test_quill_editor_syncs_with_textarea()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for sync functionality
        $this->assertStringContainsString('text-change', $content);
        $this->assertStringContainsString('contentEditor.root.innerHTML', $content);
        $this->assertStringContainsString('form.addEventListener', $content);
    }

    /**
     * Test that the editor has proper styling and responsive design
     */
    public function test_quill_editor_styling_and_responsive_design()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for custom styling
        $this->assertStringContainsString('quill-editor', $content);
        $this->assertStringContainsString('ql-toolbar', $content);
        $this->assertStringContainsString('ql-editor', $content);
        
        // Check for responsive design
        $this->assertStringContainsString('@media (max-width: 768px)', $content);
        
        // Check for dark mode support
        $this->assertStringContainsString('.dark .quill-editor', $content);
        $this->assertStringContainsString('.dark .ql-toolbar', $content);
        
        // Check for focus states
        $this->assertStringContainsString('focus-within', $content);
    }

    /**
     * Test that content management routes are accessible under new navigation structure
     */
    public function test_content_management_routes_accessible()
    {
        $this->actingAs($this->adminUser);

        // Test Legal Pages routes
        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);

        // Test Contact Submissions routes
        $response = $this->get(route('admin.contacts.index'));
        $response->assertStatus(200);

        // Test Customer Reviews routes
        $response = $this->get(route('admin.reviews.index'));
        $response->assertStatus(200);

        $response = $this->get(route('admin.reviews.create'));
        $response->assertStatus(200);
    }
}
