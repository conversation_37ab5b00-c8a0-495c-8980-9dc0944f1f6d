<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\LegalPage;
use App\Models\ContactSubmission;
use App\Models\CustomerReview;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ViewLayoutTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test that legal pages views load without layout errors
     */
    public function test_legal_pages_views_load_correctly()
    {
        $this->actingAs($this->adminUser);

        // Test index view
        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);
        $response->assertSee('Legal Pages Management');
        $response->assertSee('Create Legal Page');

        // Test create view
        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        $response->assertSee('Create Legal Page');
        $response->assertSee('Page Type');

        // Create a legal page for edit test
        $legalPage = LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Test Terms',
            'content' => '<p>Test content</p>',
        ]);

        // Test edit view
        $response = $this->get(route('admin.legal-pages.edit', $legalPage));
        $response->assertStatus(200);
        $response->assertSee('Edit Terms And Conditions');
        $response->assertSee('Test Terms');
    }

    /**
     * Test that contact submissions views load without layout errors
     */
    public function test_contact_submissions_views_load_correctly()
    {
        $this->actingAs($this->adminUser);

        // Test index view
        $response = $this->get(route('admin.contacts.index'));
        $response->assertStatus(200);
        $response->assertSee('Contact Submissions Management');
        $response->assertSee('Search & Filter');

        // Create a contact submission for show test
        $contact = ContactSubmission::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message',
        ]);

        // Test show view
        $response = $this->get(route('admin.contacts.show', $contact));
        $response->assertStatus(200);
        $response->assertSee('Contact Submission Details');
        $response->assertSee('Test User');
        $response->assertSee('<EMAIL>');
    }

    /**
     * Test that customer reviews views load without layout errors
     */
    public function test_customer_reviews_views_load_correctly()
    {
        $this->actingAs($this->adminUser);

        // Test index view
        $response = $this->get(route('admin.reviews.index'));
        $response->assertStatus(200);
        $response->assertSee('Customer Reviews Management');
        $response->assertSee('Create Review');

        // Test create view
        $response = $this->get(route('admin.reviews.create'));
        $response->assertStatus(200);
        $response->assertSee('Create Customer Review');
        $response->assertSee('Customer Name');

        // Create a customer review for edit test
        $review = CustomerReview::factory()->create([
            'customer_name' => 'Test Customer',
            'message' => 'Great service!',
        ]);

        // Test edit view
        $response = $this->get(route('admin.reviews.edit', $review));
        $response->assertStatus(200);
        $response->assertSee('Edit Customer Review');
        $response->assertSee('Test Customer');
    }

    /**
     * Test that breadcrumbs are rendered correctly
     */
    public function test_breadcrumbs_render_correctly()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);
        
        // Check that breadcrumb elements are present
        $response->assertSee('Admin');
        $response->assertSee('Legal Pages');
        
        // Check that the page title is displayed
        $response->assertSee('Legal Pages Management');
    }

    /**
     * Test that the app layout is used correctly
     */
    public function test_app_layout_structure()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);
        
        // Check for common layout elements that should be present
        $content = $response->getContent();
        
        // The response should contain the main content structure
        $this->assertStringContainsString('Legal Pages Management', $content);
        $this->assertStringContainsString('card', $content);
        
        // Should not contain any layout error messages
        $this->assertStringNotContainsString('View [layouts.dashboard] not found', $content);
        $this->assertStringNotContainsString('InvalidArgumentException', $content);
    }
}
