<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use Tests\TestCase;

class CleanedDashboardTest extends TestCase
{
    /** @test */
    public function admin_can_access_cleaned_dashboard()
    {
        // Create a simple admin user without using factory
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard.index');
        $response->assertSee('Role-based Analytics Dashboard');
    }

    /** @test */
    public function dashboard_has_required_view_data()
    {
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check that metrics are passed to view
        $response->assertViewHas('metrics');
        $metrics = $response->viewData('metrics');

        // Verify all required metric keys exist
        $this->assertArrayHasKey('totalInvestors', $metrics);
        $this->assertArrayHasKey('totalStartups', $metrics);
        $this->assertArrayHasKey('totalConsultants', $metrics);
        $this->assertArrayHasKey('activeInvestors', $metrics);
        $this->assertArrayHasKey('yearlyInvestors', $metrics);
        $this->assertArrayHasKey('monthlyInvestors', $metrics);
        $this->assertArrayHasKey('activeStartups', $metrics);
        $this->assertArrayHasKey('yearlyStartups', $metrics);
        $this->assertArrayHasKey('monthlyStartups', $metrics);
        $this->assertArrayHasKey('activeConsultants', $metrics);
        $this->assertArrayHasKey('yearlyConsultants', $metrics);
        $this->assertArrayHasKey('monthlyConsultants', $metrics);
    }

    /** @test */
    public function dashboard_shows_correct_ui_elements()
    {
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check for role-based sections
        $response->assertSee('Investor Metrics');
        $response->assertSee('Startup Metrics');
        $response->assertSee('Consultant Metrics');

        // Check for metric labels
        $response->assertSee('Total Investor');
        $response->assertSee('Active Investor');
        $response->assertSee('Yearly Investor');
        $response->assertSee('Monthly Investor');

        $response->assertSee('Total Startup');
        $response->assertSee('Active Startup');
        $response->assertSee('Yearly Startup');
        $response->assertSee('Monthly Startup');

        $response->assertSee('Total Consultant');
        $response->assertSee('Active Consultant');
        $response->assertSee('Yearly Consultant');
        $response->assertSee('Monthly Consultant');
    }

    /** @test */
    public function dashboard_does_not_show_removed_sections()
    {
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Verify removed sections are not present
        $response->assertDontSee('System Health');
        $response->assertDontSee('Recent Activity Feed');
        $response->assertDontSee('Revenue & Subscription Analytics');
        $response->assertDontSee('User Distribution');
        $response->assertDontSee('Recent Users');
        $response->assertDontSee('Today\'s Activity');
        $response->assertDontSee('Quick Actions');
        $response->assertDontSee('Key Performance Indicators');
    }
}
