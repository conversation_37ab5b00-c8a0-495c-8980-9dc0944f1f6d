<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use Tests\TestCase;

class RestoredDashboardTest extends TestCase
{
    /** @test */
    public function admin_can_access_restored_dashboard()
    {
        // Create a simple admin user without using factory
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard.index');

        // Check for restored sections
        $response->assertSee('Investment Platform');
        $response->assertSee('System running smoothly');
        $response->assertSee('Role-based Analytics Dashboard');
        $response->assertSee('Investment Platform Overview');
        $response->assertSee('Quick Actions');
        $response->assertSee('Today\'s Activity');
    }

    /** @test */
    public function dashboard_has_all_required_view_data()
    {
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check that all required data is passed to view
        $response->assertViewHas('metrics');
        $response->assertViewHas('quickActions');
        $response->assertViewHas('platformActivity');

        $metrics = $response->viewData('metrics');
        $quickActions = $response->viewData('quickActions');
        $platformActivity = $response->viewData('platformActivity');

        // Verify KPI Card metrics
        $this->assertArrayHasKey('totalUsers', $metrics);
        $this->assertArrayHasKey('activeSubscriptions', $metrics);
        $this->assertArrayHasKey('userGrowthRate', $metrics);
        $this->assertArrayHasKey('churnRate', $metrics);

        // Verify Investment Platform Overview metrics
        $this->assertArrayHasKey('totalInvestors', $metrics);
        $this->assertArrayHasKey('totalStartups', $metrics);
        $this->assertArrayHasKey('totalInterests', $metrics);
        $this->assertArrayHasKey('activeInterests', $metrics);
        $this->assertArrayHasKey('esgCompletionRate', $metrics);
        $this->assertArrayHasKey('documentUploadRate', $metrics);

        // Verify Role-based KPI metrics
        $this->assertArrayHasKey('activeInvestors', $metrics);
        $this->assertArrayHasKey('yearlyInvestors', $metrics);
        $this->assertArrayHasKey('monthlyInvestors', $metrics);
        $this->assertArrayHasKey('activeStartups', $metrics);
        $this->assertArrayHasKey('yearlyStartups', $metrics);
        $this->assertArrayHasKey('monthlyStartups', $metrics);
        $this->assertArrayHasKey('activeConsultants', $metrics);
        $this->assertArrayHasKey('yearlyConsultants', $metrics);
        $this->assertArrayHasKey('monthlyConsultants', $metrics);

        // Verify Quick Actions data
        $this->assertArrayHasKey('pending_verifications', $quickActions);
        $this->assertArrayHasKey('locked_accounts', $quickActions);
        $this->assertArrayHasKey('pending_refunds', $quickActions);
        $this->assertArrayHasKey('failed_payments_week', $quickActions);

        // Verify Platform Activity data
        $this->assertArrayHasKey('new_registrations_today', $platformActivity);
        $this->assertArrayHasKey('new_subscriptions_today', $platformActivity);
        $this->assertArrayHasKey('payments_today', $platformActivity);
        $this->assertArrayHasKey('interests_today', $platformActivity);
    }

    /** @test */
    public function dashboard_shows_all_restored_ui_elements()
    {
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check for System Status Banner
        $response->assertSee('Investment Platform');
        $response->assertSee('System running smoothly');
        $response->assertSee('Live');

        // Check for KPI Cards
        $response->assertSee('Total Users');
        $response->assertSee('Active Subscriptions');
        $response->assertSee('User Growth');
        $response->assertSee('Churn Rate');
        $response->assertSee('vs last month');
        $response->assertSee('Monthly cancellation rate');

        // Check for Investment Platform Overview
        $response->assertSee('Investment Platform Overview');
        $response->assertSee('Total Investors');
        $response->assertSee('Total Startups');
        $response->assertSee('Total Interests');
        $response->assertSee('Active Interests');
        $response->assertSee('ESG Completion');
        $response->assertSee('Document Upload');

        // Check for Role-based Analytics Dashboard
        $response->assertSee('Role-based Analytics Dashboard');
        $response->assertSee('Investor Metrics');
        $response->assertSee('Startup Metrics');
        $response->assertSee('Consultant Metrics');

        // Check for Quick Actions
        $response->assertSee('Quick Actions');
        $response->assertSee('Items requiring attention');

        // Check for Today's Activity
        $response->assertSee('Today\'s Activity');
        $response->assertSee('New Registrations');
        $response->assertSee('New Subscriptions');
        $response->assertSee('Payments Today');
        $response->assertSee('New Interests');
    }

    /** @test */
    public function dashboard_does_not_show_permanently_removed_sections()
    {
        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password')
        ]);
        $admin->id = 1;

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Verify permanently removed sections are not present
        $response->assertDontSee('System Health');
        $response->assertDontSee('Recent Activity Feed');
        $response->assertDontSee('Revenue & Subscription Analytics');
        $response->assertDontSee('User Distribution');
        $response->assertDontSee('Recent Users');
        $response->assertDontSee('Monthly Subscription Revenue (TK)');
        $response->assertDontSee('Yearly Subscription Revenue (TK)');
        $response->assertDontSee('Package Distribution');
    }
}
