<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\WebsiteSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class WebsiteSettingsTest extends TestCase
{
    use RefreshDatabase;

    public ?User $testAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        Storage::fake('public');
        
        $this->testAdmin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'email_verified_at' => now()
        ]);
    }

    public function test_admin_can_access_website_settings_index()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.website-settings.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.website-settings.index');
        $response->assertViewHas('settings');
    }

    public function test_admin_can_access_logo_management()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.website-settings.logo-management'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.website-settings.logo-management');
        $response->assertSee('Logo Management');
    }

    public function test_admin_can_upload_logos()
    {
        $headerLogo = UploadedFile::fake()->image('header-logo.png', 800, 200);
        $footerLogo = UploadedFile::fake()->image('footer-logo.png', 400, 100);

        $response = $this->actingAs($this->testAdmin)
            ->post(route('admin.website-settings.update-logos'), [
                'header_logo' => $headerLogo,
                'footer_logo' => $footerLogo,
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('message');
        $response->assertSessionHas('type', 'success');

        // Verify files were stored
        $this->assertDatabaseHas('website_settings', [
            'id' => 1
        ]);

        $settings = WebsiteSetting::first();
        $this->assertNotNull($settings->header_logo);
        $this->assertNotNull($settings->footer_logo);

        Storage::disk('public')->assertExists($settings->header_logo);
        Storage::disk('public')->assertExists($settings->footer_logo);
    }

    public function test_admin_can_access_favicon_management()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.website-settings.favicon-management'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.website-settings.favicon-management');
        $response->assertSee('Favicon Management');
    }

    public function test_admin_can_upload_favicons()
    {
        $faviconPng16 = UploadedFile::fake()->image('favicon-16.png', 16, 16);
        $faviconPng32 = UploadedFile::fake()->image('favicon-32.png', 32, 32);

        $response = $this->actingAs($this->testAdmin)
            ->post(route('admin.website-settings.update-favicons'), [
                'favicon_png_16' => $faviconPng16,
                'favicon_png_32' => $faviconPng32,
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('message');
        $response->assertSessionHas('type', 'success');

        $settings = WebsiteSetting::first();
        $this->assertNotNull($settings->favicon_png_16);
        $this->assertNotNull($settings->favicon_png_32);
    }

    public function test_admin_can_access_email_settings()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.website-settings.email-settings'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.website-settings.email-settings');
        $response->assertSee('Email Settings');
    }

    public function test_admin_can_update_email_settings()
    {
        $emailData = [
            'mail_driver' => 'smtp',
            'mail_host' => 'smtp.gmail.com',
            'mail_port' => 587,
            'mail_username' => '<EMAIL>',
            'mail_password' => 'password123',
            'mail_encryption' => 'tls',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'Test Application',
        ];

        $response = $this->actingAs($this->testAdmin)
            ->post(route('admin.website-settings.update-email-settings'), $emailData);

        $response->assertRedirect();
        $response->assertSessionHas('message');
        $response->assertSessionHas('type', 'success');

        $this->assertDatabaseHas('website_settings', [
            'mail_host' => 'smtp.gmail.com',
            'mail_port' => 587,
            'mail_from_address' => '<EMAIL>',
        ]);
    }

    public function test_non_admin_cannot_access_website_settings()
    {
        $user = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);

        $response = $this->actingAs($user)
            ->get(route('admin.website-settings.index'));

        $response->assertStatus(403);
    }

    public function test_logo_upload_validation()
    {
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000);

        $response = $this->actingAs($this->testAdmin)
            ->post(route('admin.website-settings.update-logos'), [
                'header_logo' => $invalidFile,
            ]);

        $response->assertSessionHasErrors(['header_logo']);
    }

    public function test_email_settings_validation()
    {
        $response = $this->actingAs($this->testAdmin)
            ->post(route('admin.website-settings.update-email-settings'), [
                'mail_driver' => 'smtp',
                'mail_host' => '', // Required when driver is smtp
                'mail_from_address' => 'invalid-email', // Invalid email
            ]);

        $response->assertSessionHasErrors(['mail_host', 'mail_from_address']);
    }

    public function test_website_settings_model_accessors()
    {
        $settings = WebsiteSetting::create([
            'header_logo' => 'logos/header.png',
            'favicon_ico' => 'favicons/favicon.ico',
            'mail_host' => 'smtp.gmail.com',
            'mail_port' => 587,
        ]);

        $this->assertStringContains('logos/header.png', $settings->header_logo_url);
        $this->assertStringContains('favicons/favicon.ico', $settings->favicon_ico_url);

        $emailConfig = $settings->email_config;
        $this->assertEquals('smtp.gmail.com', $emailConfig['host']);
        $this->assertEquals(587, $emailConfig['port']);
    }

    public function test_website_settings_sidebar_navigation()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertSee('Website Configuration');
        $response->assertSee(route('admin.website-settings.index'));
    }
}
