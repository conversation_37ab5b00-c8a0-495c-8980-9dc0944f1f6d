<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionProduct;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class EnhancedDashboardTest extends TestCase
{
    use RefreshDatabase;

    public ?User $testAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->testAdmin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'email_verified_at' => now()
        ]);
    }

    public function test_dashboard_displays_role_based_kpi_metrics()
    {
        // Create test users with different roles
        $investor = User::create([
            'name' => 'Test Investor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);

        $startup = User::create([
            'name' => 'Test Startup',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'startup',
            'email_verified_at' => now()
        ]);

        $consultant = User::create([
            'name' => 'Test Consultant',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'analyst', // Using analyst as consultant
            'email_verified_at' => now()
        ]);

        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check that role-based KPI sections are displayed
        $response->assertSee('Role-based Analytics Dashboard');
        $response->assertSee('Investor Metrics');
        $response->assertSee('Startup Metrics');
        $response->assertSee('Consultant Metrics');

        // Check that metrics are displayed
        $response->assertSee('Total Investor');
        $response->assertSee('Active Investor');
        $response->assertSee('Yearly Investor');
        $response->assertSee('Monthly Investor');

        $response->assertSee('Total Startup');
        $response->assertSee('Active Startup');
        $response->assertSee('Yearly Startup');
        $response->assertSee('Monthly Startup');

        $response->assertSee('Total Consultant');
        $response->assertSee('Active Consultant');
        $response->assertSee('Yearly Consultant');
        $response->assertSee('Monthly Consultant');
    }

    public function test_dashboard_calculates_correct_role_metrics()
    {
        // Create users from different time periods
        $thisMonthInvestor = User::create([
            'name' => 'This Month Investor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);
        $thisMonthInvestor->created_at = Carbon::now();
        $thisMonthInvestor->save();

        $thisYearStartup = User::create([
            'name' => 'This Year Startup',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'startup',
            'email_verified_at' => now()
        ]);
        $thisYearStartup->created_at = Carbon::now()->subMonths(3);
        $thisYearStartup->save();

        $lastYearConsultant = User::create([
            'name' => 'Last Year Consultant',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'analyst',
            'email_verified_at' => now()
        ]);
        $lastYearConsultant->created_at = Carbon::now()->subYear();
        $lastYearConsultant->save();

        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $metrics = $response->viewData('metrics');

        // Verify monthly counts
        $this->assertEquals(1, $metrics['monthlyInvestors']);
        $this->assertEquals(0, $metrics['monthlyStartups']);
        $this->assertEquals(0, $metrics['monthlyConsultants']);

        // Verify yearly counts
        $this->assertEquals(1, $metrics['yearlyInvestors']);
        $this->assertEquals(1, $metrics['yearlyStartups']);
        $this->assertEquals(0, $metrics['yearlyConsultants']); // Last year user not counted
    }

    public function test_dashboard_displays_revenue_analytics_charts()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check that revenue analytics section is displayed
        $response->assertSee('Revenue & Subscription Analytics');
        $response->assertSee('Monthly Subscription Revenue (TK)');
        $response->assertSee('Yearly Subscription Revenue (TK)');
        $response->assertSee('Monthly Package Distribution');
        $response->assertSee('Yearly Package Distribution');

        // Check that chart containers exist
        $response->assertSee('monthly-revenue-chart');
        $response->assertSee('yearly-revenue-chart');
        $response->assertSee('monthly-package-chart');
        $response->assertSee('yearly-package-chart');
    }

    public function test_dashboard_includes_enhanced_chart_data()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        
        $chartData = $response->viewData('chartData');
        
        // Check that enhanced chart data exists
        $this->assertArrayHasKey('monthlyRevenue', $chartData);
        $this->assertArrayHasKey('yearlyRevenue', $chartData);
        $this->assertArrayHasKey('monthlyPackageDistribution', $chartData);
        $this->assertArrayHasKey('yearlyPackageDistribution', $chartData);
        
        // Verify structure of revenue data
        $this->assertArrayHasKey('labels', $chartData['monthlyRevenue']);
        $this->assertArrayHasKey('data', $chartData['monthlyRevenue']);
        $this->assertIsArray($chartData['monthlyRevenue']['labels']);
        $this->assertIsArray($chartData['monthlyRevenue']['data']);
        
        // Verify structure of package distribution data
        $this->assertArrayHasKey('investor', $chartData['monthlyPackageDistribution']);
        $this->assertArrayHasKey('startup', $chartData['monthlyPackageDistribution']);
        $this->assertArrayHasKey('consultant', $chartData['monthlyPackageDistribution']);
    }

    public function test_dashboard_includes_tk_currency_formatting()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check that TK currency is mentioned in the charts
        $response->assertSee('Revenue (TK)');
        $response->assertSee('Monthly Subscription Revenue (TK)');
        $response->assertSee('Yearly Subscription Revenue (TK)');

        // Check that the JavaScript includes TK formatting
        $response->assertSee('৳');
        $response->assertSee('TK');
    }

    public function test_dashboard_calculates_active_users_with_subscriptions()
    {
        // Create users
        $investor = User::create([
            'name' => 'Test Investor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);

        $startup = User::create([
            'name' => 'Test Startup',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'startup',
            'email_verified_at' => now()
        ]);

        // Create subscription product
        $product = SubscriptionProduct::create([
            'name' => 'Test Plan',
            'description' => 'Test subscription plan',
            'price' => 99.99,
            'billing_cycle' => 'monthly',
            'is_active' => true,
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123'
        ]);

        // Create active subscription for investor only
        UserSubscription::create([
            'user_id' => $investor->id,
            'subscription_product_id' => $product->id,
            'stripe_subscription_id' => 'sub_test_investor',
            'status' => 'active',
            'amount' => 99.99,
            'currency' => 'usd',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth()
        ]);

        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $metrics = $response->viewData('metrics');

        // Verify active user counts
        $this->assertEquals(1, $metrics['activeInvestors']); // Has active subscription
        $this->assertEquals(0, $metrics['activeStartups']); // No active subscription
        $this->assertEquals(0, $metrics['activeConsultants']); // No users
    }

    public function test_dashboard_responsive_design_classes()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check for responsive grid classes
        $response->assertSee('grid-cols-12');
        $response->assertSee('md:grid-cols-4');
        $response->assertSee('lg:grid-cols-2');
        $response->assertSee('grid-cols-2');

        // Check for DashCode Tailwind CSS patterns
        $response->assertSee('card');
        $response->assertSee('card-header');
        $response->assertSee('card-body');
        $response->assertSee('btn');
    }

    public function test_dashboard_includes_apexcharts_integration()
    {
        $response = $this->actingAs($this->testAdmin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Check that ApexCharts is included
        $response->assertSee('ApexCharts');
        $response->assertSee('new ApexCharts');

        // Check that chart rendering is included
        $response->assertSee('monthlyRevenueChart.render()');
        $response->assertSee('yearlyRevenueChart.render()');
        $response->assertSee('monthlyPackageChart.render()');
        $response->assertSee('yearlyPackageChart.render()');
    }
}
