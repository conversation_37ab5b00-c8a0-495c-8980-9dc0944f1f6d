<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionProduct;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class DashboardAnalyticsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user manually to avoid factory issues
        $this->admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'email_verified_at' => now()
        ]);
    }

    /** @test */
    public function admin_can_view_dashboard_with_role_based_metrics()
    {
        // Create test users with different roles
        $investor = User::create([
            'name' => 'Test Investor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);

        $startup = User::create([
            'name' => 'Test Startup',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'startup',
            'email_verified_at' => now()
        ]);

        $consultant = User::create([
            'name' => 'Test Consultant',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'analyst',
            'email_verified_at' => now()
        ]);

        // Create subscription product
        $product = SubscriptionProduct::create([
            'name' => 'Test Plan',
            'description' => 'Test subscription plan',
            'price' => 99.99,
            'billing_cycle' => 'monthly',
            'is_active' => true,
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123'
        ]);

        // Create active subscriptions
        $investorSubscription = UserSubscription::create([
            'user_id' => $investor->id,
            'subscription_product_id' => $product->id,
            'stripe_subscription_id' => 'sub_test_investor',
            'status' => 'active',
            'amount' => 99.99,
            'currency' => 'usd',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth()
        ]);

        $startupSubscription = UserSubscription::create([
            'user_id' => $startup->id,
            'subscription_product_id' => $product->id,
            'stripe_subscription_id' => 'sub_test_startup',
            'status' => 'active',
            'amount' => 99.99,
            'currency' => 'usd',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth()
        ]);

        // Create payments
        Payment::create([
            'user_id' => $investor->id,
            'user_subscription_id' => $investorSubscription->id,
            'amount' => 9999, // $99.99 in cents
            'currency' => 'usd',
            'status' => 'succeeded',
            'type' => 'subscription'
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        
        // Check that role-based metrics are present
        $response->assertViewHas('metrics');
        $metrics = $response->viewData('metrics');
        
        $this->assertArrayHasKey('totalInvestors', $metrics);
        $this->assertArrayHasKey('totalStartups', $metrics);
        $this->assertArrayHasKey('totalConsultants', $metrics);
        $this->assertArrayHasKey('activeInvestors', $metrics);
        $this->assertArrayHasKey('activeStartups', $metrics);
        $this->assertArrayHasKey('activeConsultants', $metrics);
        $this->assertArrayHasKey('yearlyInvestors', $metrics);
        $this->assertArrayHasKey('monthlyInvestors', $metrics);

        // Verify counts
        $this->assertEquals(1, $metrics['totalInvestors']);
        $this->assertEquals(1, $metrics['totalStartups']);
        $this->assertEquals(1, $metrics['totalConsultants']);
        $this->assertEquals(1, $metrics['activeInvestors']);
        $this->assertEquals(1, $metrics['activeStartups']);
    }

    /** @test */
    public function dashboard_includes_enhanced_chart_data()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        
        // Check that chart data is present
        $response->assertViewHas('chartData');
        $chartData = $response->viewData('chartData');
        
        $this->assertArrayHasKey('monthlyRevenue', $chartData);
        $this->assertArrayHasKey('yearlyRevenue', $chartData);
        $this->assertArrayHasKey('monthlyPackageDistribution', $chartData);
        $this->assertArrayHasKey('yearlyPackageDistribution', $chartData);

        // Verify structure
        $this->assertArrayHasKey('labels', $chartData['monthlyRevenue']);
        $this->assertArrayHasKey('data', $chartData['monthlyRevenue']);
        $this->assertArrayHasKey('labels', $chartData['yearlyRevenue']);
        $this->assertArrayHasKey('data', $chartData['yearlyRevenue']);
    }

    /** @test */
    public function monthly_and_yearly_user_counts_are_accurate()
    {
        // Create users from different time periods
        $thisMonthInvestor = User::create([
            'name' => 'This Month Investor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);
        $thisMonthInvestor->created_at = Carbon::now();
        $thisMonthInvestor->save();

        $thisYearStartup = User::create([
            'name' => 'This Year Startup',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'startup',
            'email_verified_at' => now()
        ]);
        $thisYearStartup->created_at = Carbon::now()->subMonths(3);
        $thisYearStartup->save();

        $lastYearConsultant = User::create([
            'name' => 'Last Year Consultant',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'analyst',
            'email_verified_at' => now()
        ]);
        $lastYearConsultant->created_at = Carbon::now()->subYear();
        $lastYearConsultant->save();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.dashboard'));

        $metrics = $response->viewData('metrics');

        // This month counts
        $this->assertEquals(1, $metrics['monthlyInvestors']);
        $this->assertEquals(0, $metrics['monthlyStartups']);
        $this->assertEquals(0, $metrics['monthlyConsultants']);

        // This year counts (should include this month + other months this year)
        $this->assertEquals(1, $metrics['yearlyInvestors']);
        $this->assertEquals(1, $metrics['yearlyStartups']);
        $this->assertEquals(0, $metrics['yearlyConsultants']); // Last year user not counted
    }
}
