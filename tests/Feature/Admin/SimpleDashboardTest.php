<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SimpleDashboardTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_dashboard()
    {
        // Create admin user manually
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'email_verified_at' => now()
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard.index');
        $response->assertViewHas('metrics');
        $response->assertViewHas('chartData');
    }

    public function test_dashboard_has_role_based_metrics()
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'email_verified_at' => now()
        ]);

        // Create some test users
        User::create([
            'name' => 'Test Investor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'investor',
            'email_verified_at' => now()
        ]);

        User::create([
            'name' => 'Test Startup',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'startup',
            'email_verified_at' => now()
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        
        $metrics = $response->viewData('metrics');
        
        // Check that new role-based metrics exist
        $this->assertArrayHasKey('totalInvestors', $metrics);
        $this->assertArrayHasKey('totalStartups', $metrics);
        $this->assertArrayHasKey('totalConsultants', $metrics);
        $this->assertArrayHasKey('activeInvestors', $metrics);
        $this->assertArrayHasKey('monthlyInvestors', $metrics);
        $this->assertArrayHasKey('yearlyInvestors', $metrics);
        
        // Verify basic counts
        $this->assertEquals(1, $metrics['totalInvestors']);
        $this->assertEquals(1, $metrics['totalStartups']);
    }

    public function test_dashboard_has_enhanced_chart_data()
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'email_verified_at' => now()
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);
        
        $chartData = $response->viewData('chartData');
        
        // Check that new chart data exists
        $this->assertArrayHasKey('monthlyRevenue', $chartData);
        $this->assertArrayHasKey('yearlyRevenue', $chartData);
        $this->assertArrayHasKey('monthlyPackageDistribution', $chartData);
        $this->assertArrayHasKey('yearlyPackageDistribution', $chartData);
        
        // Verify structure
        $this->assertIsArray($chartData['monthlyRevenue']['labels']);
        $this->assertIsArray($chartData['monthlyRevenue']['data']);
        $this->assertIsArray($chartData['yearlyRevenue']['labels']);
        $this->assertIsArray($chartData['yearlyRevenue']['data']);
    }
}
