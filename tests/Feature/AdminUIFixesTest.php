<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\LegalPage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminUIFixesTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test that Quill.js editor loads correctly with CDN assets
     */
    public function test_quill_editor_loads_with_cdn_assets()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for CDN Quill.js assets
        $this->assertStringContainsString('https://cdn.quilljs.com/1.3.6/quill.snow.css', $content);
        $this->assertStringContainsString('https://cdn.quilljs.com/1.3.6/quill.min.js', $content);
        
        // Check for editor elements
        $this->assertStringContainsString('quill-editor', $content);
        $this->assertStringContainsString('content-editor', $content);
        
        // Check for proper editor initialization
        $this->assertStringContainsString('new Quill(\'#content-editor\'', $content);
        $this->assertStringContainsString('theme: \'snow\'', $content);
    }

    /**
     * Test that Quill.js editor has full toolbar configuration
     */
    public function test_quill_editor_has_full_toolbar()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for comprehensive toolbar options
        $toolbarFeatures = [
            'header',           // Headings
            'bold',             // Bold text
            'italic',           // Italic text
            'underline',        // Underline text
            'strike',           // Strikethrough
            'list',             // Lists (ordered/bullet)
            'indent',           // Indentation
            'link',             // Links
            'align',            // Text alignment
            'color',            // Text color
            'background',       // Background color
            'blockquote',       // Blockquotes
            'code-block',       // Code blocks
            'clean'             // Remove formatting
        ];

        foreach ($toolbarFeatures as $feature) {
            $this->assertStringContainsString($feature, $content, 
                "Quill.js editor should include {$feature} in toolbar");
        }
    }

    /**
     * Test that Quill.js editor works in edit view with existing content
     */
    public function test_quill_editor_works_in_edit_view()
    {
        $this->actingAs($this->adminUser);

        $legalPage = LegalPage::factory()->create([
            'type' => 'terms-and-conditions',
            'title' => 'Test Terms',
            'content' => '<h1>Test Content</h1><p>This is test content.</p>',
        ]);

        $response = $this->get(route('admin.legal-pages.edit', $legalPage));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for CDN Quill.js assets
        $this->assertStringContainsString('https://cdn.quilljs.com/1.3.6/quill.snow.css', $content);
        $this->assertStringContainsString('https://cdn.quilljs.com/1.3.6/quill.min.js', $content);
        
        // Check that existing content is loaded
        $this->assertStringContainsString('Test Content', $content);
        $this->assertStringContainsString('This is test content', $content);
    }

    /**
     * Test that sidebar navigation is properly consolidated
     */
    public function test_sidebar_navigation_consolidation()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check that Website Settings section exists
        $this->assertStringContainsString('Website Settings', $content);
        
        // Check that Content subsection exists under Website Settings
        $this->assertStringContainsString('Content', $content);
        
        // Check that all content management items are present
        $this->assertStringContainsString('Blog Management', $content);
        $this->assertStringContainsString('FAQ Management', $content);
        $this->assertStringContainsString('Legal Pages', $content);
        $this->assertStringContainsString('Customer Reviews', $content);
        $this->assertStringContainsString('Contact Submissions', $content);
        
        // Check that there's no separate "CONTENT MANAGEMENT" section
        $this->assertStringNotContainsString('CONTENT MANAGEMENT', $content);
    }

    /**
     * Test that all content management routes are accessible
     */
    public function test_all_content_management_routes_accessible()
    {
        $this->actingAs($this->adminUser);

        // Test Blog Management routes
        $response = $this->get(route('admin.blogs.index'));
        $response->assertStatus(200);

        // Test FAQ Management routes
        $response = $this->get(route('admin.faqs.index'));
        $response->assertStatus(200);

        // Test Legal Pages routes
        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);

        // Test Customer Reviews routes
        $response = $this->get(route('admin.reviews.index'));
        $response->assertStatus(200);

        // Test Contact Submissions routes
        $response = $this->get(route('admin.contacts.index'));
        $response->assertStatus(200);
    }

    /**
     * Test that Website Settings active state works correctly
     */
    public function test_website_settings_active_state()
    {
        $this->actingAs($this->adminUser);

        // Test that Website Settings is active when on legal pages
        $response = $this->get(route('admin.legal-pages.index'));
        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should contain active class for Website Settings
        $this->assertStringContainsString('Website Settings', $content);
        
        // Test that Website Settings is active when on blog management
        $response = $this->get(route('admin.blogs.index'));
        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should contain active class for Website Settings
        $this->assertStringContainsString('Website Settings', $content);
    }

    /**
     * Test that editor styling is responsive and accessible
     */
    public function test_quill_editor_styling_and_accessibility()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for responsive design styles
        $this->assertStringContainsString('@media (max-width: 768px)', $content);
        
        // Check for dark mode support
        $this->assertStringContainsString('.dark .quill-editor', $content);
        $this->assertStringContainsString('.dark .ql-toolbar', $content);
        
        // Check for focus states (accessibility)
        $this->assertStringContainsString('focus-within', $content);
        
        // Check for proper styling classes
        $this->assertStringContainsString('quill-editor', $content);
        $this->assertStringContainsString('ql-toolbar', $content);
        $this->assertStringContainsString('ql-editor', $content);
    }

    /**
     * Test that content syncing works properly
     */
    public function test_quill_editor_content_syncing()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.legal-pages.create'));
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for content syncing functionality
        $this->assertStringContainsString('text-change', $content);
        $this->assertStringContainsString('contentEditor.root.innerHTML', $content);
        $this->assertStringContainsString('form.addEventListener', $content);
        $this->assertStringContainsString('submit', $content);
        
        // Check that hidden textarea exists
        $this->assertStringContainsString('style="display: none;"', $content);
        $this->assertStringContainsString('name="content"', $content);
    }
}
