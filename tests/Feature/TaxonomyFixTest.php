<?php

namespace Tests\Feature;

use App\Models\User;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TaxonomyFixTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /**
     * Test that categories can be created successfully without saveAsRoot() method
     */
    public function test_category_creation_works_without_save_as_root()
    {
        $this->actingAs($this->adminUser);

        $response = $this->post(route('admin.categories.store'), [
            'name' => 'Test Category',
            'description' => 'This is a test category',
            'type' => 'category',
        ]);

        $response->assertRedirect(route('admin.categories.index'));
        $response->assertSessionHas('message', 'Category created successfully.');

        $this->assertDatabaseHas('taxonomies', [
            'name' => 'Test Category',
            'type' => 'category',
            'description' => 'This is a test category',
        ]);
    }

    /**
     * Test that subcategories can be created with parent relationship
     */
    public function test_subcategory_creation_with_parent()
    {
        $this->actingAs($this->adminUser);

        // Create parent category first
        $parent = Taxonomy::create([
            'name' => 'Parent Category',
            'type' => 'category',
            'description' => 'Parent category for testing',
        ]);

        $response = $this->post(route('admin.categories.store'), [
            'name' => 'Child Category',
            'description' => 'This is a child category',
            'type' => 'category',
            'parent_id' => $parent->id,
        ]);

        $response->assertRedirect(route('admin.categories.index'));
        $response->assertSessionHas('message', 'Category created successfully.');

        $this->assertDatabaseHas('taxonomies', [
            'name' => 'Child Category',
            'type' => 'category',
            'parent_id' => $parent->id,
        ]);
    }

    /**
     * Test that categories can be updated successfully
     */
    public function test_category_update_works_correctly()
    {
        $this->actingAs($this->adminUser);

        $category = Taxonomy::create([
            'name' => 'Original Category',
            'type' => 'category',
            'description' => 'Original description',
        ]);

        $response = $this->put(route('admin.categories.update', $category), [
            'name' => 'Updated Category',
            'description' => 'Updated description',
            'type' => 'category',
        ]);

        $response->assertRedirect(route('admin.categories.index'));
        $response->assertSessionHas('message', 'Category updated successfully.');

        $this->assertDatabaseHas('taxonomies', [
            'id' => $category->id,
            'name' => 'Updated Category',
            'description' => 'Updated description',
        ]);
    }

    /**
     * Test that parent relationship can be changed during update
     */
    public function test_category_parent_relationship_update()
    {
        $this->actingAs($this->adminUser);

        $parent = Taxonomy::create([
            'name' => 'New Parent',
            'type' => 'category',
        ]);

        $category = Taxonomy::create([
            'name' => 'Test Category',
            'type' => 'category',
        ]);

        $response = $this->put(route('admin.categories.update', $category), [
            'name' => 'Test Category',
            'description' => 'Test description',
            'type' => 'category',
            'parent_id' => $parent->id,
        ]);

        $response->assertRedirect(route('admin.categories.index'));
        $response->assertSessionHas('message', 'Category updated successfully.');

        $category->refresh();
        $this->assertEquals($parent->id, $category->parent_id);
    }

    /**
     * Test that circular reference prevention works
     */
    public function test_circular_reference_prevention()
    {
        $this->actingAs($this->adminUser);

        $category = Taxonomy::create([
            'name' => 'Test Category',
            'type' => 'category',
        ]);

        $response = $this->put(route('admin.categories.update', $category), [
            'name' => 'Test Category',
            'description' => 'Test description',
            'type' => 'category',
            'parent_id' => $category->id, // Self-parenting
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['parent_id' => 'A category cannot be its own parent.']);
    }

    /**
     * Test that keywords can be created via API
     */
    public function test_keyword_creation_via_api()
    {
        $this->actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/taxonomies', [
            'type' => 'keyword',
            'name' => 'Test Keyword',
            'description' => 'This is a test keyword',
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'success' => true,
            'message' => 'Taxonomy created successfully.',
        ]);

        $this->assertDatabaseHas('taxonomies', [
            'name' => 'Test Keyword',
            'type' => 'keyword',
        ]);
    }

    /**
     * Test that focus regions can be created
     */
    public function test_focus_region_creation()
    {
        $this->actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/taxonomies', [
            'type' => 'focus_region',
            'name' => 'North America',
            'description' => 'North American region',
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'success' => true,
            'message' => 'Taxonomy created successfully.',
        ]);

        $this->assertDatabaseHas('taxonomies', [
            'name' => 'North America',
            'type' => 'focus_region',
        ]);
    }

    /**
     * Test that investment focus can be created
     */
    public function test_investment_focus_creation()
    {
        $this->actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/taxonomies', [
            'type' => 'investment_focus',
            'name' => 'FinTech',
            'description' => 'Financial Technology sector',
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'success' => true,
            'message' => 'Taxonomy created successfully.',
        ]);

        $this->assertDatabaseHas('taxonomies', [
            'name' => 'FinTech',
            'type' => 'investment_focus',
        ]);
    }

    /**
     * Test that duplicate names within same type are prevented
     */
    public function test_duplicate_name_prevention()
    {
        $this->actingAs($this->adminUser);

        // Create first taxonomy
        Taxonomy::create([
            'name' => 'Duplicate Test',
            'type' => 'keyword',
        ]);

        // Try to create another with same name and type
        $response = $this->postJson('/api/admin/taxonomies', [
            'type' => 'keyword',
            'name' => 'Duplicate Test',
            'description' => 'This should fail',
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'message' => 'A taxonomy with this name already exists for this type.',
        ]);
    }

    /**
     * Test that taxonomies can be retrieved by type
     */
    public function test_taxonomy_retrieval_by_type()
    {
        // Create test taxonomies
        Taxonomy::create(['name' => 'Category 1', 'type' => 'category']);
        Taxonomy::create(['name' => 'Category 2', 'type' => 'category']);
        Taxonomy::create(['name' => 'Keyword 1', 'type' => 'keyword']);

        $response = $this->getJson('/api/taxonomies?type=category');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => ['id', 'name', 'type', 'slug']
            ]
        ]);

        $data = $response->json('data');
        $this->assertCount(2, $data);
        $this->assertEquals('category', $data[0]['type']);
        $this->assertEquals('category', $data[1]['type']);
    }
}
