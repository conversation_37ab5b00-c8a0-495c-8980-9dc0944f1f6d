import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { serviceService } from '../../services/serviceService';
import LoadingSpinner from '../../components/LoadingSpinner';

const PurchaseHistory = () => {
    const [purchases, setPurchases] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [filters, setFilters] = useState({
        status: '',
        per_page: 10
    });

    useEffect(() => {
        loadPurchases();
    }, [filters]);

    const loadPurchases = async () => {
        setLoading(true);
        try {
            const result = await serviceService.getPurchaseHistory(filters);
            if (result.success) {
                setPurchases(result.data);
                setError('');
            } else {
                setError(result.message);
            }
        } catch (err) {
            setError('Failed to load purchase history');
            console.error('Error loading purchases:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    if (loading) {
        return <LoadingSpinner />;
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                                Purchase History
                            </h1>
                            <p className="mt-2 text-gray-600 dark:text-gray-400">
                                View all your service purchases and their status
                            </p>
                        </div>
                        <Link
                            to="/app/services"
                            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-200"
                        >
                            Browse Services
                        </Link>
                    </div>
                </div>

                {/* Filters */}
                <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Status
                            </label>
                            <select
                                value={filters.status}
                                onChange={(e) => handleFilterChange('status', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value="">All Statuses</option>
                                <option value="completed">Completed</option>
                                <option value="pending">Pending</option>
                                <option value="failed">Failed</option>
                                <option value="refunded">Refunded</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Per Page
                            </label>
                            <select
                                value={filters.per_page}
                                onChange={(e) => handleFilterChange('per_page', parseInt(e.target.value))}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value={10}>10 per page</option>
                                <option value={25}>25 per page</option>
                                <option value={50}>50 per page</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Purchases List */}
                {purchases.data && purchases.data.length > 0 ? (
                    <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                                Your Purchases ({purchases.total})
                            </h3>
                        </div>
                        <div className="divide-y divide-gray-200 dark:divide-gray-700">
                            {purchases.data.map((purchase) => (
                                <div key={purchase.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                                                        {purchase.service.name}
                                                    </h4>
                                                    <div className="mt-1 flex items-center space-x-4">
                                                        <span className="text-sm text-gray-500 dark:text-gray-400">
                                                            Purchase #{purchase.id}
                                                        </span>
                                                        {purchase.service.category && (
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                                {purchase.service.category}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-lg font-semibold text-gray-900 dark:text-white">
                                                        {serviceService.formatPrice(purchase.amount)}
                                                    </div>
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${serviceService.getStatusBadgeClass(purchase.status)}`}>
                                                        {purchase.status.charAt(0).toUpperCase() + purchase.status.slice(1)}
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            {purchase.service.description && (
                                                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                                    {purchase.service.description.length > 150 
                                                        ? `${purchase.service.description.substring(0, 150)}...`
                                                        : purchase.service.description
                                                    }
                                                </p>
                                            )}

                                            <div className="mt-3 flex items-center justify-between">
                                                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                                    <div>
                                                        <span className="font-medium">Purchased:</span> {' '}
                                                        {purchase.purchased_at 
                                                            ? new Date(purchase.purchased_at).toLocaleDateString('en-US', {
                                                                year: 'numeric',
                                                                month: 'long',
                                                                day: 'numeric',
                                                                hour: '2-digit',
                                                                minute: '2-digit'
                                                            })
                                                            : 'Pending'
                                                        }
                                                    </div>
                                                    {purchase.stripe_payment_intent_id && (
                                                        <div>
                                                            <span className="font-medium">Payment ID:</span> {' '}
                                                            <span className="font-mono text-xs">
                                                                {purchase.stripe_payment_intent_id.substring(0, 20)}...
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="flex space-x-2">
                                                    <Link
                                                        to={`/app/services/${purchase.service.id}`}
                                                        className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                                                    >
                                                        View Service
                                                    </Link>
                                                    {purchase.status === 'failed' && (
                                                        <button
                                                            onClick={() => window.location.reload()}
                                                            className="text-green-600 hover:text-green-500 text-sm font-medium"
                                                        >
                                                            Retry Purchase
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Pagination */}
                        {purchases.links && (
                            <div className="bg-white dark:bg-gray-800 px-6 py-3 border-t border-gray-200 dark:border-gray-700">
                                <div className="flex justify-center">
                                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        {purchases.links.map((link, index) => (
                                            <button
                                                key={index}
                                                onClick={() => {
                                                    if (link.url && !link.active) {
                                                        // Handle pagination - would need to extract page number from URL
                                                        console.log('Navigate to:', link.url);
                                                    }
                                                }}
                                                disabled={!link.url}
                                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                    link.active
                                                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900 dark:border-blue-400 dark:text-blue-200'
                                                        : link.url
                                                        ? 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700'
                                                        : 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'
                                                } ${index === 0 ? 'rounded-l-md' : ''} ${index === purchases.links.length - 1 ? 'rounded-r-md' : ''}`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </nav>
                                </div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="text-center py-12">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No purchases found</h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            {filters.status ? 'Try adjusting your filters.' : 'You haven\'t purchased any services yet.'}
                        </p>
                        {!filters.status && (
                            <div className="mt-6">
                                <Link
                                    to="/app/services"
                                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    Browse Services
                                </Link>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default PurchaseHistory;
