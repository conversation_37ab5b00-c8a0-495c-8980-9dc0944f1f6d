import React, { useState, useEffect } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { serviceService } from '../../services/serviceService';
import LoadingSpinner from '../../components/LoadingSpinner';

const ServiceDetail = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [service, setService] = useState(null);
    const [loading, setLoading] = useState(true);
    const [purchasing, setPurchasing] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        loadService();
    }, [id]);

    const loadService = async () => {
        setLoading(true);
        try {
            const result = await serviceService.getService(id);
            if (result.success) {
                setService(result.data);
                setError('');
            } else {
                setError(result.message);
            }
        } catch (err) {
            setError('Failed to load service details');
            console.error('Error loading service:', err);
        } finally {
            setLoading(false);
        }
    };

    const handlePurchase = async () => {
        setPurchasing(true);
        try {
            const result = await serviceService.purchaseService(id);
            if (result.success && result.checkout_url) {
                serviceService.redirectToCheckout(result.checkout_url);
            } else {
                setError(result.message || 'Failed to initiate purchase');
            }
        } catch (err) {
            setError('Failed to initiate purchase');
            console.error('Error purchasing service:', err);
        } finally {
            setPurchasing(false);
        }
    };

    if (loading) {
        return <LoadingSpinner />;
    }

    if (error && !service) {
        return (
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
                <div className="text-center">
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 max-w-md">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4">
                        <Link
                            to="/app/services"
                            className="text-blue-600 hover:text-blue-500 font-medium"
                        >
                            ← Back to Services
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    if (!service) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Breadcrumb */}
                <nav className="flex mb-8" aria-label="Breadcrumb">
                    <ol className="inline-flex items-center space-x-1 md:space-x-3">
                        <li className="inline-flex items-center">
                            <Link
                                to="/app/services"
                                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"
                            >
                                Services
                            </Link>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                                    {service.name}
                                </span>
                            </div>
                        </li>
                    </ol>
                </nav>

                {/* Error Message */}
                {error && (
                    <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Service Details */}
                <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div className="px-6 py-8">
                        {/* Header */}
                        <div className="flex justify-between items-start mb-6">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                                    {service.name}
                                </h1>
                                <div className="mt-2 flex items-center space-x-3">
                                    <span className={`px-3 py-1 text-sm font-medium rounded-full ${serviceService.getRoleBadgeClass(service.target_role)}`}>
                                        {service.target_role === 'all' ? 'All Users' : service.target_role.charAt(0).toUpperCase() + service.target_role.slice(1)}
                                    </span>
                                    {service.category && (
                                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                            {service.category}
                                        </span>
                                    )}
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                                    {serviceService.formatPrice(service.price)}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                    One-time payment
                                </div>
                            </div>
                        </div>

                        {/* Description */}
                        {service.description && (
                            <div className="mb-8">
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                                    Description
                                </h2>
                                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                                    {service.description}
                                </p>
                            </div>
                        )}

                        {/* Features */}
                        {service.features && service.features.length > 0 && (
                            <div className="mb-8">
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    What's Included
                                </h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {service.features.map((feature, index) => (
                                        <div key={index} className="flex items-start">
                                            <svg className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Purchase History */}
                        {service.service_purchases && service.service_purchases.length > 0 && (
                            <div className="mb-8">
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    Your Purchase History
                                </h2>
                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div className="space-y-3">
                                        {service.service_purchases.slice(0, 3).map((purchase) => (
                                            <div key={purchase.id} className="flex justify-between items-center">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                        Purchase #{purchase.id}
                                                    </div>
                                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                                        {purchase.purchased_at ? new Date(purchase.purchased_at).toLocaleDateString() : 'Pending'}
                                                    </div>
                                                </div>
                                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${serviceService.getStatusBadgeClass(purchase.status)}`}>
                                                    {purchase.status.charAt(0).toUpperCase() + purchase.status.slice(1)}
                                                </span>
                                            </div>
                                        ))}
                                        {service.service_purchases.length > 3 && (
                                            <div className="text-center">
                                                <Link
                                                    to="/app/services/purchase-history"
                                                    className="text-sm text-blue-600 hover:text-blue-500 font-medium"
                                                >
                                                    View all purchases
                                                </Link>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Purchase Button */}
                        <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
                            <Link
                                to="/app/services"
                                className="text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 font-medium"
                            >
                                ← Back to Services
                            </Link>
                            <button
                                onClick={handlePurchase}
                                disabled={purchasing}
                                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-8 rounded-md transition duration-200 flex items-center"
                            >
                                {purchasing ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                        </svg>
                                        Processing...
                                    </>
                                ) : (
                                    <>
                                        Purchase for {serviceService.formatPrice(service.price)}
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ServiceDetail;
