import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { serviceService } from '../../services/serviceService';
import LoadingSpinner from '../../components/LoadingSpinner';

const Services = () => {
    const [services, setServices] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [filters, setFilters] = useState({
        category: '',
        per_page: 12
    });

    useEffect(() => {
        loadServices();
        loadCategories();
    }, [filters]);

    const loadServices = async () => {
        setLoading(true);
        try {
            const result = await serviceService.getServices(filters);
            if (result.success) {
                setServices(result.data);
                setError('');
            } else {
                setError(result.message);
            }
        } catch (err) {
            setError('Failed to load services');
            console.error('Error loading services:', err);
        } finally {
            setLoading(false);
        }
    };

    const loadCategories = async () => {
        try {
            const result = await serviceService.getCategories();
            if (result.success) {
                setCategories(result.data);
            }
        } catch (err) {
            console.error('Error loading categories:', err);
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handlePurchase = async (serviceId) => {
        try {
            const result = await serviceService.purchaseService(serviceId);
            if (result.success && result.checkout_url) {
                serviceService.redirectToCheckout(result.checkout_url);
            } else {
                setError(result.message || 'Failed to initiate purchase');
            }
        } catch (err) {
            setError('Failed to initiate purchase');
            console.error('Error purchasing service:', err);
        }
    };

    if (loading) {
        return <LoadingSpinner />;
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                        Available Services
                    </h1>
                    <p className="mt-2 text-gray-600 dark:text-gray-400">
                        Discover and purchase services tailored to your needs
                    </p>
                </div>

                {/* Filters */}
                <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Category
                            </label>
                            <select
                                value={filters.category}
                                onChange={(e) => handleFilterChange('category', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value="">All Categories</option>
                                {categories.map((category) => (
                                    <option key={category} value={category}>
                                        {category}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Per Page
                            </label>
                            <select
                                value={filters.per_page}
                                onChange={(e) => handleFilterChange('per_page', parseInt(e.target.value))}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value={6}>6 per page</option>
                                <option value={12}>12 per page</option>
                                <option value={24}>24 per page</option>
                            </select>
                        </div>
                        <div className="flex items-end">
                            <Link
                                to="/app/services/purchase-history"
                                className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-200"
                            >
                                View Purchase History
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Services Grid */}
                {services.data && services.data.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {services.data.map((service) => (
                            <div key={service.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                                <div className="p-6">
                                    <div className="flex justify-between items-start mb-4">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {service.name}
                                        </h3>
                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${serviceService.getRoleBadgeClass(service.target_role)}`}>
                                            {service.target_role === 'all' ? 'All Users' : service.target_role.charAt(0).toUpperCase() + service.target_role.slice(1)}
                                        </span>
                                    </div>

                                    {service.description && (
                                        <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                                            {service.description}
                                        </p>
                                    )}

                                    {service.category && (
                                        <div className="mb-4">
                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                {service.category}
                                            </span>
                                        </div>
                                    )}

                                    {service.features && service.features.length > 0 && (
                                        <div className="mb-4">
                                            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Features:</h4>
                                            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                                {service.features.slice(0, 3).map((feature, index) => (
                                                    <li key={index} className="flex items-start">
                                                        <svg className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                        </svg>
                                                        {feature}
                                                    </li>
                                                ))}
                                                {service.features.length > 3 && (
                                                    <li className="text-xs text-gray-500 dark:text-gray-400">
                                                        +{service.features.length - 3} more features
                                                    </li>
                                                )}
                                            </ul>
                                        </div>
                                    )}

                                    <div className="flex justify-between items-center">
                                        <div className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {serviceService.formatPrice(service.price)}
                                        </div>
                                        <div className="flex space-x-2">
                                            <Link
                                                to={`/app/services/${service.id}`}
                                                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition duration-200"
                                            >
                                                View Details
                                            </Link>
                                            <button
                                                onClick={() => handlePurchase(service.id)}
                                                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition duration-200"
                                            >
                                                Purchase
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-12">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No services available</h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            {filters.category ? 'Try adjusting your filters.' : 'Services will appear here when they become available.'}
                        </p>
                    </div>
                )}

                {/* Pagination */}
                {services.data && services.data.length > 0 && services.links && (
                    <div className="mt-8 flex justify-center">
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            {services.links.map((link, index) => (
                                <button
                                    key={index}
                                    onClick={() => {
                                        if (link.url && !link.active) {
                                            // Handle pagination - would need to extract page number from URL
                                            console.log('Navigate to:', link.url);
                                        }
                                    }}
                                    disabled={!link.url}
                                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                        link.active
                                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                            : link.url
                                            ? 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                            : 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                                    } ${index === 0 ? 'rounded-l-md' : ''} ${index === services.links.length - 1 ? 'rounded-r-md' : ''}`}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </nav>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Services;
