import { apiService } from './apiService';

class SubscriptionService {
    /**
     * Create a direct subscription using Stripe Checkout (recommended approach)
     */
    async createDirectSubscription(productId, successUrl = null, cancelUrl = null) {
        try {
            const requestData = {
                product_id: productId
            };

            // Add custom URLs if provided
            if (successUrl) {
                requestData.success_url = successUrl;
            }
            if (cancelUrl) {
                requestData.cancel_url = cancelUrl;
            }

            const response = await apiService.post('/subscriptions/direct', requestData);
            const data = response.data.data;

            if (data && data.checkout_url) {
                return {
                    success: true,
                    checkout_url: data.checkout_url,
                    session_id: data.session_id,
                    requires_redirect: true,
                    product: data.product,
                    message: 'Checkout session created successfully'
                };
            }

            throw new Error('Invalid response from direct subscription creation');
        } catch (error) {
            console.error('Direct subscription creation failed:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Failed to create subscription checkout',
                status_code: error.response?.status
            };
        }
    }

    /**
     * Create a subscription with automatic payment method retry logic
     */
    async createSubscriptionWithRetry(subscriptionProductId) {
        try {
            // First, try to create subscription normally (optimized flow)
            const response = await apiService.post('/subscriptions', {
                subscription_product_id: subscriptionProductId
            });

            const data = response.data.data;

            // Check if subscription was created directly (optimized flow)
            if (data && data.id && !data.requires_redirect) {
                return {
                    success: true,
                    subscription: data,
                    direct_creation: true,
                    message: 'Subscription created successfully using existing payment method'
                };
            }

            // Check if we need to redirect to Stripe Checkout (fallback)
            if (data && data.checkout_url) {
                return {
                    success: true,
                    checkout_url: data.checkout_url,
                    requires_redirect: true,
                    message: 'Redirecting to payment setup'
                };
            }

            throw new Error('Invalid response from subscription creation');
        } catch (error) {
            console.error('Subscription creation failed:', error);

            // If subscription creation fails, it might be due to payment method issues
            return {
                success: false,
                error: error.response?.data?.message || 'Failed to create subscription',
                needsPaymentMethod: this.isPaymentMethodError(error)
            };
        }
    }

    /**
     * Check if the error is related to payment method issues
     */
    isPaymentMethodError(error) {
        const errorMessage = error.response?.data?.message?.toLowerCase() || '';
        const paymentMethodErrors = [
            'no payment method',
            'payment method required',
            'invalid payment method',
            'payment failed',
            'card declined'
        ];
        
        return paymentMethodErrors.some(errorText => errorMessage.includes(errorText));
    }

    /**
     * Get user's payment methods ordered by default first
     */
    async getPaymentMethodsForRetry() {
        try {
            const response = await apiService.getPaymentMethods();
            const paymentMethods = response.data.data || [];
            
            // Sort by default first, then by creation date
            return paymentMethods.sort((a, b) => {
                if (a.is_default && !b.is_default) return -1;
                if (!a.is_default && b.is_default) return 1;
                return new Date(b.created_at) - new Date(a.created_at);
            });
        } catch (error) {
            console.error('Failed to fetch payment methods:', error);
            return [];
        }
    }

    /**
     * Attempt subscription with specific payment method
     */
    async createSubscriptionWithPaymentMethod(subscriptionProductId, paymentMethodId) {
        try {
            const response = await apiService.post('/subscriptions', {
                subscription_product_id: subscriptionProductId,
                payment_method_id: paymentMethodId
            });

            const data = response.data.data;

            // Check if subscription was created directly (optimized flow)
            if (data && data.id && !data.requires_redirect) {
                return {
                    success: true,
                    subscription: data,
                    direct_creation: true
                };
            }

            // Check if we need to redirect to Stripe Checkout (fallback)
            if (data && data.checkout_url) {
                return {
                    success: true,
                    checkout_url: data.checkout_url,
                    requires_redirect: true
                };
            }

            throw new Error('Invalid response from subscription creation');
        } catch (error) {
            return {
                success: false,
                error: error.response?.data?.message || 'Payment failed'
            };
        }
    }

    /**
     * Handle subscription creation with comprehensive retry logic
     */
    async handleSubscriptionCreation(subscriptionProductId, onProgress = null) {
        // Step 1: Try normal subscription creation
        if (onProgress) onProgress('Creating subscription...');
        
        const initialResult = await this.createSubscriptionWithRetry(subscriptionProductId);
        
        if (initialResult.success) {
            return initialResult;
        }

        // Step 2: If it failed due to payment method issues, try retry logic
        if (initialResult.needsPaymentMethod) {
            if (onProgress) onProgress('Checking payment methods...');
            
            const paymentMethods = await this.getPaymentMethodsForRetry();
            
            if (paymentMethods.length === 0) {
                return {
                    success: false,
                    error: 'No payment methods available. Please add a payment method first.',
                    needsPaymentMethod: true
                };
            }

            // Step 3: Try each payment method in order
            for (let i = 0; i < paymentMethods.length; i++) {
                const paymentMethod = paymentMethods[i];
                
                if (onProgress) {
                    onProgress(`Trying payment method ${i + 1} of ${paymentMethods.length}...`);
                }
                
                const result = await this.createSubscriptionWithPaymentMethod(
                    subscriptionProductId, 
                    paymentMethod.id
                );
                
                if (result.success) {
                    return {
                        ...result,
                        message: `Subscription created using ${paymentMethod.card_brand} •••• ${paymentMethod.card_last_four}`
                    };
                }
                
                // Log the failure but continue to next payment method
                console.warn(`Payment method ${paymentMethod.id} failed:`, result.error);
            }
            
            // All payment methods failed
            return {
                success: false,
                error: 'All payment methods failed. Please check your payment methods and try again.',
                allMethodsFailed: true
            };
        }

        // Return the original error if it's not payment method related
        return initialResult;
    }

    /**
     * Check if user has any payment methods
     */
    async hasPaymentMethods() {
        try {
            const paymentMethods = await this.getPaymentMethodsForRetry();
            return paymentMethods.length > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get subscription status and details
     */
    async getSubscriptionStatus() {
        try {
            const response = await apiService.get('/subscriptions/status');
            return response.data.data;
        } catch (error) {
            console.error('Failed to get subscription status:', error);
            return null;
        }
    }

    /**
     * Cancel subscription
     */
    async cancelSubscription(subscriptionId) {
        try {
            const response = await apiService.delete(`/subscriptions/${subscriptionId}`);
            return {
                success: true,
                data: response.data.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data?.message || 'Failed to cancel subscription'
            };
        }
    }

    /**
     * Upgrade/downgrade subscription
     */
    async updateSubscription(subscriptionId, newProductId) {
        try {
            const response = await apiService.put(`/subscriptions/${subscriptionId}`, {
                subscription_product_id: newProductId
            });
            return {
                success: true,
                data: response.data.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data?.message || 'Failed to update subscription'
            };
        }
    }
}

export const subscriptionService = new SubscriptionService();
