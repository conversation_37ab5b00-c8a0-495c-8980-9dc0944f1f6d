import { apiService } from './apiService';

class ServiceService {
    /**
     * Get all services available to the current user
     */
    async getServices(params = {}) {
        try {
            const response = await apiService.get('/services', { params });
            return {
                success: true,
                data: response.data.data,
                message: response.data.message
            };
        } catch (error) {
            console.error('Error fetching services:', error);
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to fetch services',
                error: error.response?.data
            };
        }
    }

    /**
     * Get a specific service by ID
     */
    async getService(serviceId) {
        try {
            const response = await apiService.get(`/services/${serviceId}`);
            return {
                success: true,
                data: response.data.data,
                message: response.data.message
            };
        } catch (error) {
            console.error('Error fetching service:', error);
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to fetch service',
                error: error.response?.data
            };
        }
    }

    /**
     * Purchase a service
     */
    async purchaseService(serviceId, options = {}) {
        try {
            const requestData = {
                success_url: options.successUrl || `${window.location.origin}/app/services/purchase-success`,
                cancel_url: options.cancelUrl || `${window.location.origin}/app/services`
            };

            const response = await apiService.post(`/services/${serviceId}/purchase`, requestData);
            const data = response.data.data;

            if (data && data.checkout_url) {
                return {
                    success: true,
                    checkout_url: data.checkout_url,
                    session_id: data.session_id,
                    service_purchase_id: data.service_purchase_id,
                    service: data.service,
                    requires_redirect: true,
                    message: 'Service purchase initiated successfully'
                };
            }

            return {
                success: false,
                message: 'Invalid response from server'
            };

        } catch (error) {
            console.error('Error purchasing service:', error);
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to initiate service purchase',
                error: error.response?.data
            };
        }
    }

    /**
     * Get user's service purchase history
     */
    async getPurchaseHistory(params = {}) {
        try {
            const response = await apiService.get('/service-purchases', { params });
            return {
                success: true,
                data: response.data.data,
                message: response.data.message
            };
        } catch (error) {
            console.error('Error fetching purchase history:', error);
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to fetch purchase history',
                error: error.response?.data
            };
        }
    }

    /**
     * Get available service categories
     */
    async getCategories() {
        try {
            const response = await apiService.get('/service-categories');
            return {
                success: true,
                data: response.data.data,
                message: response.data.message
            };
        } catch (error) {
            console.error('Error fetching service categories:', error);
            return {
                success: false,
                message: error.response?.data?.message || 'Failed to fetch categories',
                error: error.response?.data
            };
        }
    }

    /**
     * Redirect to Stripe Checkout for service purchase
     */
    redirectToCheckout(checkoutUrl) {
        if (checkoutUrl) {
            window.location.href = checkoutUrl;
        } else {
            throw new Error('No checkout URL provided');
        }
    }

    /**
     * Format price for display
     */
    formatPrice(price) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    }

    /**
     * Get status badge class for purchase status
     */
    getStatusBadgeClass(status) {
        const statusClasses = {
            'completed': 'bg-green-100 text-green-800',
            'pending': 'bg-yellow-100 text-yellow-800',
            'failed': 'bg-red-100 text-red-800',
            'refunded': 'bg-gray-100 text-gray-800'
        };
        return statusClasses[status] || 'bg-gray-100 text-gray-800';
    }

    /**
     * Get role badge class
     */
    getRoleBadgeClass(role) {
        const roleClasses = {
            'investor': 'bg-blue-100 text-blue-800',
            'startup': 'bg-green-100 text-green-800',
            'analyst': 'bg-purple-100 text-purple-800',
            'all': 'bg-gray-100 text-gray-800'
        };
        return roleClasses[role] || 'bg-gray-100 text-gray-800';
    }
}

export const serviceService = new ServiceService();
export default serviceService;
